{"name": "postal-mime", "version": "2.4.4", "description": "Email parser for browser environments", "main": "./src/postal-mime.js", "exports": {"import": "./src/postal-mime.js", "types": "./postal-mime.d.ts", "default": "./src/postal-mime.js"}, "type": "module", "types": "postal-mime.d.ts", "scripts": {"test": "eslint && node --test", "update": "rm -rf node_modules package-lock.json && ncu -u && npm install"}, "keywords": ["mime", "email"], "repository": {"type": "git", "url": "git+https://github.com/postalsys/postal-mime.git"}, "bugs": {"url": "https://github.com/postalsys/postal-mime/issues"}, "author": "<PERSON><PERSON>", "license": "MIT-0", "devDependencies": {"@types/node": "24.0.4", "cross-blob": "3.0.2", "cross-env": "7.0.3", "eslint": "8.57.0", "eslint-cli": "1.1.1", "iframe-resizer": "4.3.6"}}