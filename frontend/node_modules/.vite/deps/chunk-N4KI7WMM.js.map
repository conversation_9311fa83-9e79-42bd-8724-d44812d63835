{"version": 3, "sources": ["../../../../node_modules/@fortawesome/fontawesome-svg-core/index.mjs"], "sourcesContent": ["/*!\n * Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)\n * Copyright 2025 Fonticons, Inc.\n */\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return _arrayLikeToArray(r);\n}\nfunction _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nfunction _createForOfIteratorHelper(r, e) {\n  var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (!t) {\n    if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) {\n      t && (r = t);\n      var n = 0,\n        F = function () {};\n      return {\n        s: F,\n        n: function () {\n          return n >= r.length ? {\n            done: !0\n          } : {\n            done: !1,\n            value: r[n++]\n          };\n        },\n        e: function (r) {\n          throw r;\n        },\n        f: F\n      };\n    }\n    throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n  var o,\n    a = !0,\n    u = !1;\n  return {\n    s: function () {\n      t = t.call(r);\n    },\n    n: function () {\n      var r = t.next();\n      return a = r.done, r;\n    },\n    e: function (r) {\n      u = !0, o = r;\n    },\n    f: function () {\n      try {\n        a || null == t.return || t.return();\n      } finally {\n        if (u) throw o;\n      }\n    }\n  };\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && _setPrototypeOf(t, e);\n}\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\nfunction _toConsumableArray(r) {\n  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\nfunction _wrapRegExp() {\n  _wrapRegExp = function (e, r) {\n    return new BabelRegExp(e, void 0, r);\n  };\n  var e = RegExp.prototype,\n    r = new WeakMap();\n  function BabelRegExp(e, t, p) {\n    var o = RegExp(e, t);\n    return r.set(o, p || r.get(e)), _setPrototypeOf(o, BabelRegExp.prototype);\n  }\n  function buildGroups(e, t) {\n    var p = r.get(t);\n    return Object.keys(p).reduce(function (r, t) {\n      var o = p[t];\n      if (\"number\" == typeof o) r[t] = e[o];else {\n        for (var i = 0; void 0 === e[o[i]] && i + 1 < o.length;) i++;\n        r[t] = e[o[i]];\n      }\n      return r;\n    }, Object.create(null));\n  }\n  return _inherits(BabelRegExp, RegExp), BabelRegExp.prototype.exec = function (r) {\n    var t = e.exec.call(this, r);\n    if (t) {\n      t.groups = buildGroups(t, this);\n      var p = t.indices;\n      p && (p.groups = buildGroups(p, this));\n    }\n    return t;\n  }, BabelRegExp.prototype[Symbol.replace] = function (t, p) {\n    if (\"string\" == typeof p) {\n      var o = r.get(this);\n      return e[Symbol.replace].call(this, t, p.replace(/\\$<([^>]+)(>|$)/g, function (e, r, t) {\n        if (\"\" === t) return e;\n        var p = o[r];\n        return Array.isArray(p) ? \"$\" + p.join(\"$\") : \"number\" == typeof p ? \"$\" + p : \"\";\n      }));\n    }\n    if (\"function\" == typeof p) {\n      var i = this;\n      return e[Symbol.replace].call(this, t, function () {\n        var e = arguments;\n        return \"object\" != typeof e[e.length - 1] && (e = [].slice.call(e)).push(buildGroups(e, i)), p.apply(this, e);\n      });\n    }\n    return e[Symbol.replace].call(this, t, p);\n  }, _wrapRegExp.apply(this, arguments);\n}\n\nvar noop = function noop() {};\nvar _WINDOW = {};\nvar _DOCUMENT = {};\nvar _MUTATION_OBSERVER = null;\nvar _PERFORMANCE = {\n  mark: noop,\n  measure: noop\n};\ntry {\n  if (typeof window !== 'undefined') _WINDOW = window;\n  if (typeof document !== 'undefined') _DOCUMENT = document;\n  if (typeof MutationObserver !== 'undefined') _MUTATION_OBSERVER = MutationObserver;\n  if (typeof performance !== 'undefined') _PERFORMANCE = performance;\n} catch (e) {} // eslint-disable-line no-empty\n\nvar _ref = _WINDOW.navigator || {},\n  _ref$userAgent = _ref.userAgent,\n  userAgent = _ref$userAgent === void 0 ? '' : _ref$userAgent;\nvar WINDOW = _WINDOW;\nvar DOCUMENT = _DOCUMENT;\nvar MUTATION_OBSERVER = _MUTATION_OBSERVER;\nvar PERFORMANCE = _PERFORMANCE;\nvar IS_BROWSER = !!WINDOW.document;\nvar IS_DOM = !!DOCUMENT.documentElement && !!DOCUMENT.head && typeof DOCUMENT.addEventListener === 'function' && typeof DOCUMENT.createElement === 'function';\nvar IS_IE = ~userAgent.indexOf('MSIE') || ~userAgent.indexOf('Trident/');\n\nvar _so;\nvar K = /fa(k|kd|s|r|l|t|d|dr|dl|dt|b|slr|slpr|wsb|tl|ns|nds|es|jr|jfr|jdr|cr|ss|sr|sl|st|sds|sdr|sdl|sdt)?[\\-\\ ]/,\n  W = /Font ?Awesome ?([567 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit|Notdog Duo|Notdog|Chisel|Etch|Thumbprint|Jelly Fill|Jelly Duo|Jelly|Slab Press|Slab|Whiteboard)?.*/i;\nvar z = {\n    classic: {\n      fa: \"solid\",\n      fas: \"solid\",\n      \"fa-solid\": \"solid\",\n      far: \"regular\",\n      \"fa-regular\": \"regular\",\n      fal: \"light\",\n      \"fa-light\": \"light\",\n      fat: \"thin\",\n      \"fa-thin\": \"thin\",\n      fab: \"brands\",\n      \"fa-brands\": \"brands\"\n    },\n    duotone: {\n      fa: \"solid\",\n      fad: \"solid\",\n      \"fa-solid\": \"solid\",\n      \"fa-duotone\": \"solid\",\n      fadr: \"regular\",\n      \"fa-regular\": \"regular\",\n      fadl: \"light\",\n      \"fa-light\": \"light\",\n      fadt: \"thin\",\n      \"fa-thin\": \"thin\"\n    },\n    sharp: {\n      fa: \"solid\",\n      fass: \"solid\",\n      \"fa-solid\": \"solid\",\n      fasr: \"regular\",\n      \"fa-regular\": \"regular\",\n      fasl: \"light\",\n      \"fa-light\": \"light\",\n      fast: \"thin\",\n      \"fa-thin\": \"thin\"\n    },\n    \"sharp-duotone\": {\n      fa: \"solid\",\n      fasds: \"solid\",\n      \"fa-solid\": \"solid\",\n      fasdr: \"regular\",\n      \"fa-regular\": \"regular\",\n      fasdl: \"light\",\n      \"fa-light\": \"light\",\n      fasdt: \"thin\",\n      \"fa-thin\": \"thin\"\n    },\n    slab: {\n      \"fa-regular\": \"regular\",\n      faslr: \"regular\"\n    },\n    \"slab-press\": {\n      \"fa-regular\": \"regular\",\n      faslpr: \"regular\"\n    },\n    thumbprint: {\n      \"fa-light\": \"light\",\n      fatl: \"light\"\n    },\n    whiteboard: {\n      \"fa-semibold\": \"semibold\",\n      fawsb: \"semibold\"\n    },\n    notdog: {\n      \"fa-solid\": \"solid\",\n      fans: \"solid\"\n    },\n    \"notdog-duo\": {\n      \"fa-solid\": \"solid\",\n      fands: \"solid\"\n    },\n    etch: {\n      \"fa-solid\": \"solid\",\n      faes: \"solid\"\n    },\n    jelly: {\n      \"fa-regular\": \"regular\",\n      fajr: \"regular\"\n    },\n    \"jelly-fill\": {\n      \"fa-regular\": \"regular\",\n      fajfr: \"regular\"\n    },\n    \"jelly-duo\": {\n      \"fa-regular\": \"regular\",\n      fajdr: \"regular\"\n    },\n    chisel: {\n      \"fa-regular\": \"regular\",\n      facr: \"regular\"\n    }\n  },\n  O = {\n    GROUP: \"duotone-group\",\n    SWAP_OPACITY: \"swap-opacity\",\n    PRIMARY: \"primary\",\n    SECONDARY: \"secondary\"\n  },\n  G = [\"fa-classic\", \"fa-duotone\", \"fa-sharp\", \"fa-sharp-duotone\", \"fa-thumbprint\", \"fa-whiteboard\", \"fa-notdog\", \"fa-notdog-duo\", \"fa-chisel\", \"fa-etch\", \"fa-jelly\", \"fa-jelly-fill\", \"fa-jelly-duo\", \"fa-slab\", \"fa-slab-press\"];\nvar a = \"classic\",\n  o = \"duotone\",\n  d = \"sharp\",\n  t = \"sharp-duotone\",\n  i = \"chisel\",\n  n = \"etch\",\n  h = \"jelly\",\n  s = \"jelly-duo\",\n  f = \"jelly-fill\",\n  g = \"notdog\",\n  l = \"notdog-duo\",\n  u = \"slab\",\n  p = \"slab-press\",\n  e = \"thumbprint\",\n  w = \"whiteboard\",\n  m = \"Classic\",\n  y = \"Duotone\",\n  x = \"Sharp\",\n  c = \"Sharp Duotone\",\n  I = \"Chisel\",\n  b = \"Etch\",\n  F = \"Jelly\",\n  v = \"Jelly Duo\",\n  S = \"Jelly Fill\",\n  A = \"Notdog\",\n  P = \"Notdog Duo\",\n  j = \"Slab\",\n  B = \"Slab Press\",\n  N = \"Thumbprint\",\n  k = \"Whiteboard\",\n  oo = [a, o, d, t, i, n, h, s, f, g, l, u, p, e, w],\n  so = (_so = {}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_so, a, m), o, y), d, x), t, c), i, I), n, b), h, F), s, v), f, S), g, A), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_so, l, P), u, j), p, B), e, N), w, k));\nvar io = {\n    classic: {\n      900: \"fas\",\n      400: \"far\",\n      normal: \"far\",\n      300: \"fal\",\n      100: \"fat\"\n    },\n    duotone: {\n      900: \"fad\",\n      400: \"fadr\",\n      300: \"fadl\",\n      100: \"fadt\"\n    },\n    sharp: {\n      900: \"fass\",\n      400: \"fasr\",\n      300: \"fasl\",\n      100: \"fast\"\n    },\n    \"sharp-duotone\": {\n      900: \"fasds\",\n      400: \"fasdr\",\n      300: \"fasdl\",\n      100: \"fasdt\"\n    },\n    slab: {\n      400: \"faslr\"\n    },\n    \"slab-press\": {\n      400: \"faslpr\"\n    },\n    whiteboard: {\n      600: \"fawsb\"\n    },\n    thumbprint: {\n      300: \"fatl\"\n    },\n    notdog: {\n      900: \"fans\"\n    },\n    \"notdog-duo\": {\n      900: \"fands\"\n    },\n    etch: {\n      900: \"faes\"\n    },\n    chisel: {\n      400: \"facr\"\n    },\n    jelly: {\n      400: \"fajr\"\n    },\n    \"jelly-fill\": {\n      400: \"fajfr\"\n    },\n    \"jelly-duo\": {\n      400: \"fajdr\"\n    }\n  };\nvar Bo = {\n    \"Font Awesome 7 Free\": {\n      900: \"fas\",\n      400: \"far\"\n    },\n    \"Font Awesome 7 Pro\": {\n      900: \"fas\",\n      400: \"far\",\n      normal: \"far\",\n      300: \"fal\",\n      100: \"fat\"\n    },\n    \"Font Awesome 7 Brands\": {\n      400: \"fab\",\n      normal: \"fab\"\n    },\n    \"Font Awesome 7 Duotone\": {\n      900: \"fad\",\n      400: \"fadr\",\n      normal: \"fadr\",\n      300: \"fadl\",\n      100: \"fadt\"\n    },\n    \"Font Awesome 7 Sharp\": {\n      900: \"fass\",\n      400: \"fasr\",\n      normal: \"fasr\",\n      300: \"fasl\",\n      100: \"fast\"\n    },\n    \"Font Awesome 7 Sharp Duotone\": {\n      900: \"fasds\",\n      400: \"fasdr\",\n      normal: \"fasdr\",\n      300: \"fasdl\",\n      100: \"fasdt\"\n    },\n    \"Font Awesome 7 Jelly\": {\n      400: \"fajr\",\n      normal: \"fajr\"\n    },\n    \"Font Awesome 7 Jelly Fill\": {\n      400: \"fajfr\",\n      normal: \"fajfr\"\n    },\n    \"Font Awesome 7 Jelly Duo\": {\n      400: \"fajdr\",\n      normal: \"fajdr\"\n    },\n    \"Font Awesome 7 Slab\": {\n      400: \"faslr\",\n      normal: \"faslr\"\n    },\n    \"Font Awesome 7 Slab Press\": {\n      400: \"faslpr\",\n      normal: \"faslpr\"\n    },\n    \"Font Awesome 7 Thumbprint\": {\n      300: \"fatl\",\n      normal: \"fatl\"\n    },\n    \"Font Awesome 7 Notdog\": {\n      900: \"fans\",\n      normal: \"fans\"\n    },\n    \"Font Awesome 7 Notdog Duo\": {\n      900: \"fands\",\n      normal: \"fands\"\n    },\n    \"Font Awesome 7 Etch\": {\n      900: \"faes\",\n      normal: \"faes\"\n    },\n    \"Font Awesome 7 Chisel\": {\n      400: \"facr\",\n      normal: \"facr\"\n    },\n    \"Font Awesome 7 Whiteboard\": {\n      600: \"fawsb\",\n      normal: \"fawsb\"\n    }\n  };\nvar Co = new Map([[\"classic\", {\n    defaultShortPrefixId: \"fas\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\", \"brands\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"duotone\", {\n    defaultShortPrefixId: \"fad\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"sharp\", {\n    defaultShortPrefixId: \"fass\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"sharp-duotone\", {\n    defaultShortPrefixId: \"fasds\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"chisel\", {\n    defaultShortPrefixId: \"facr\",\n    defaultStyleId: \"regular\",\n    styleIds: [\"regular\"],\n    futureStyleIds: [],\n    defaultFontWeight: 400\n  }], [\"etch\", {\n    defaultShortPrefixId: \"faes\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"jelly\", {\n    defaultShortPrefixId: \"fajr\",\n    defaultStyleId: \"regular\",\n    styleIds: [\"regular\"],\n    futureStyleIds: [],\n    defaultFontWeight: 400\n  }], [\"jelly-duo\", {\n    defaultShortPrefixId: \"fajdr\",\n    defaultStyleId: \"regular\",\n    styleIds: [\"regular\"],\n    futureStyleIds: [],\n    defaultFontWeight: 400\n  }], [\"jelly-fill\", {\n    defaultShortPrefixId: \"fajfr\",\n    defaultStyleId: \"regular\",\n    styleIds: [\"regular\"],\n    futureStyleIds: [],\n    defaultFontWeight: 400\n  }], [\"notdog\", {\n    defaultShortPrefixId: \"fans\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"notdog-duo\", {\n    defaultShortPrefixId: \"fands\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"slab\", {\n    defaultShortPrefixId: \"faslr\",\n    defaultStyleId: \"regular\",\n    styleIds: [\"regular\"],\n    futureStyleIds: [],\n    defaultFontWeight: 400\n  }], [\"slab-press\", {\n    defaultShortPrefixId: \"faslpr\",\n    defaultStyleId: \"regular\",\n    styleIds: [\"regular\"],\n    futureStyleIds: [],\n    defaultFontWeight: 400\n  }], [\"thumbprint\", {\n    defaultShortPrefixId: \"fatl\",\n    defaultStyleId: \"light\",\n    styleIds: [\"light\"],\n    futureStyleIds: [],\n    defaultFontWeight: 300\n  }], [\"whiteboard\", {\n    defaultShortPrefixId: \"fawsb\",\n    defaultStyleId: \"semibold\",\n    styleIds: [\"semibold\"],\n    futureStyleIds: [],\n    defaultFontWeight: 600\n  }]]),\n  Ro = {\n    chisel: {\n      regular: \"facr\"\n    },\n    classic: {\n      brands: \"fab\",\n      light: \"fal\",\n      regular: \"far\",\n      solid: \"fas\",\n      thin: \"fat\"\n    },\n    duotone: {\n      light: \"fadl\",\n      regular: \"fadr\",\n      solid: \"fad\",\n      thin: \"fadt\"\n    },\n    etch: {\n      solid: \"faes\"\n    },\n    jelly: {\n      regular: \"fajr\"\n    },\n    \"jelly-duo\": {\n      regular: \"fajdr\"\n    },\n    \"jelly-fill\": {\n      regular: \"fajfr\"\n    },\n    notdog: {\n      solid: \"fans\"\n    },\n    \"notdog-duo\": {\n      solid: \"fands\"\n    },\n    sharp: {\n      light: \"fasl\",\n      regular: \"fasr\",\n      solid: \"fass\",\n      thin: \"fast\"\n    },\n    \"sharp-duotone\": {\n      light: \"fasdl\",\n      regular: \"fasdr\",\n      solid: \"fasds\",\n      thin: \"fasdt\"\n    },\n    slab: {\n      regular: \"faslr\"\n    },\n    \"slab-press\": {\n      regular: \"faslpr\"\n    },\n    thumbprint: {\n      light: \"fatl\"\n    },\n    whiteboard: {\n      semibold: \"fawsb\"\n    }\n  };\nvar zo = [\"fak\", \"fa-kit\", \"fakd\", \"fa-kit-duotone\"],\n  Oo = {\n    kit: {\n      fak: \"kit\",\n      \"fa-kit\": \"kit\"\n    },\n    \"kit-duotone\": {\n      fakd: \"kit-duotone\",\n      \"fa-kit-duotone\": \"kit-duotone\"\n    }\n  },\n  Go = [\"kit\"];\nvar D = \"kit\",\n  r = \"kit-duotone\",\n  T = \"Kit\",\n  C = \"Kit Duotone\",\n  qo = _defineProperty(_defineProperty({}, D, T), r, C);\nvar Xo = {\n  kit: {\n    \"fa-kit\": \"fak\"\n  },\n  \"kit-duotone\": {\n    \"fa-kit-duotone\": \"fakd\"\n  }\n};\nvar lt = {\n    \"Font Awesome Kit\": {\n      400: \"fak\",\n      normal: \"fak\"\n    },\n    \"Font Awesome Kit Duotone\": {\n      400: \"fakd\",\n      normal: \"fakd\"\n    }\n  },\n  et = {\n    kit: {\n      fak: \"fa-kit\"\n    },\n    \"kit-duotone\": {\n      fakd: \"fa-kit-duotone\"\n    }\n  };\nvar dt = {\n    kit: {\n      kit: \"fak\"\n    },\n    \"kit-duotone\": {\n      \"kit-duotone\": \"fakd\"\n    }\n  };\n\nvar _fl;\nvar l$1 = {\n    GROUP: \"duotone-group\",\n    SWAP_OPACITY: \"swap-opacity\",\n    PRIMARY: \"primary\",\n    SECONDARY: \"secondary\"\n  },\n  i$1 = [\"fa-classic\", \"fa-duotone\", \"fa-sharp\", \"fa-sharp-duotone\", \"fa-thumbprint\", \"fa-whiteboard\", \"fa-notdog\", \"fa-notdog-duo\", \"fa-chisel\", \"fa-etch\", \"fa-jelly\", \"fa-jelly-fill\", \"fa-jelly-duo\", \"fa-slab\", \"fa-slab-press\"];\nvar f$1 = \"classic\",\n  a$1 = \"duotone\",\n  n$1 = \"sharp\",\n  t$1 = \"sharp-duotone\",\n  h$1 = \"chisel\",\n  g$1 = \"etch\",\n  u$1 = \"jelly\",\n  s$1 = \"jelly-duo\",\n  p$1 = \"jelly-fill\",\n  y$1 = \"notdog\",\n  e$1 = \"notdog-duo\",\n  m$1 = \"slab\",\n  c$1 = \"slab-press\",\n  r$1 = \"thumbprint\",\n  w$1 = \"whiteboard\",\n  x$1 = \"Classic\",\n  I$1 = \"Duotone\",\n  b$1 = \"Sharp\",\n  F$1 = \"Sharp Duotone\",\n  v$1 = \"Chisel\",\n  S$1 = \"Etch\",\n  A$1 = \"Jelly\",\n  j$1 = \"Jelly Duo\",\n  P$1 = \"Jelly Fill\",\n  B$1 = \"Notdog\",\n  k$1 = \"Notdog Duo\",\n  N$1 = \"Slab\",\n  D$1 = \"Slab Press\",\n  C$1 = \"Thumbprint\",\n  T$1 = \"Whiteboard\",\n  fl = (_fl = {}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_fl, f$1, x$1), a$1, I$1), n$1, b$1), t$1, F$1), h$1, v$1), g$1, S$1), u$1, A$1), s$1, j$1), p$1, P$1), y$1, B$1), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_fl, e$1, k$1), m$1, N$1), c$1, D$1), r$1, C$1), w$1, T$1));\nvar L = \"kit\",\n  d$1 = \"kit-duotone\",\n  R$1 = \"Kit\",\n  W$1 = \"Kit Duotone\",\n  lo$1 = _defineProperty(_defineProperty({}, L, R$1), d$1, W$1);\nvar zo$1 = {\n    classic: {\n      \"fa-brands\": \"fab\",\n      \"fa-duotone\": \"fad\",\n      \"fa-light\": \"fal\",\n      \"fa-regular\": \"far\",\n      \"fa-solid\": \"fas\",\n      \"fa-thin\": \"fat\"\n    },\n    duotone: {\n      \"fa-regular\": \"fadr\",\n      \"fa-light\": \"fadl\",\n      \"fa-thin\": \"fadt\"\n    },\n    sharp: {\n      \"fa-solid\": \"fass\",\n      \"fa-regular\": \"fasr\",\n      \"fa-light\": \"fasl\",\n      \"fa-thin\": \"fast\"\n    },\n    \"sharp-duotone\": {\n      \"fa-solid\": \"fasds\",\n      \"fa-regular\": \"fasdr\",\n      \"fa-light\": \"fasdl\",\n      \"fa-thin\": \"fasdt\"\n    },\n    slab: {\n      \"fa-regular\": \"faslr\"\n    },\n    \"slab-press\": {\n      \"fa-regular\": \"faslpr\"\n    },\n    whiteboard: {\n      \"fa-semibold\": \"fawsb\"\n    },\n    thumbprint: {\n      \"fa-light\": \"fatl\"\n    },\n    notdog: {\n      \"fa-solid\": \"fans\"\n    },\n    \"notdog-duo\": {\n      \"fa-solid\": \"fands\"\n    },\n    etch: {\n      \"fa-solid\": \"faes\"\n    },\n    jelly: {\n      \"fa-regular\": \"fajr\"\n    },\n    \"jelly-fill\": {\n      \"fa-regular\": \"fajfr\"\n    },\n    \"jelly-duo\": {\n      \"fa-regular\": \"fajdr\"\n    },\n    chisel: {\n      \"fa-regular\": \"facr\"\n    }\n  },\n  J$1 = {\n    classic: [\"fas\", \"far\", \"fal\", \"fat\", \"fad\"],\n    duotone: [\"fadr\", \"fadl\", \"fadt\"],\n    sharp: [\"fass\", \"fasr\", \"fasl\", \"fast\"],\n    \"sharp-duotone\": [\"fasds\", \"fasdr\", \"fasdl\", \"fasdt\"],\n    slab: [\"faslr\"],\n    \"slab-press\": [\"faslpr\"],\n    whiteboard: [\"fawsb\"],\n    thumbprint: [\"fatl\"],\n    notdog: [\"fans\"],\n    \"notdog-duo\": [\"fands\"],\n    etch: [\"faes\"],\n    jelly: [\"fajr\"],\n    \"jelly-fill\": [\"fajfr\"],\n    \"jelly-duo\": [\"fajdr\"],\n    chisel: [\"facr\"]\n  },\n  Go$1 = {\n    classic: {\n      fab: \"fa-brands\",\n      fad: \"fa-duotone\",\n      fal: \"fa-light\",\n      far: \"fa-regular\",\n      fas: \"fa-solid\",\n      fat: \"fa-thin\"\n    },\n    duotone: {\n      fadr: \"fa-regular\",\n      fadl: \"fa-light\",\n      fadt: \"fa-thin\"\n    },\n    sharp: {\n      fass: \"fa-solid\",\n      fasr: \"fa-regular\",\n      fasl: \"fa-light\",\n      fast: \"fa-thin\"\n    },\n    \"sharp-duotone\": {\n      fasds: \"fa-solid\",\n      fasdr: \"fa-regular\",\n      fasdl: \"fa-light\",\n      fasdt: \"fa-thin\"\n    },\n    slab: {\n      faslr: \"fa-regular\"\n    },\n    \"slab-press\": {\n      faslpr: \"fa-regular\"\n    },\n    whiteboard: {\n      fawsb: \"fa-semibold\"\n    },\n    thumbprint: {\n      fatl: \"fa-light\"\n    },\n    notdog: {\n      fans: \"fa-solid\"\n    },\n    \"notdog-duo\": {\n      fands: \"fa-solid\"\n    },\n    etch: {\n      faes: \"fa-solid\"\n    },\n    jelly: {\n      fajr: \"fa-regular\"\n    },\n    \"jelly-fill\": {\n      fajfr: \"fa-regular\"\n    },\n    \"jelly-duo\": {\n      fajdr: \"fa-regular\"\n    },\n    chisel: {\n      facr: \"fa-regular\"\n    }\n  },\n  E$1 = [\"fa-solid\", \"fa-regular\", \"fa-light\", \"fa-thin\", \"fa-duotone\", \"fa-brands\", \"fa-semibold\"],\n  $o$1 = [\"fa\", \"fas\", \"far\", \"fal\", \"fat\", \"fad\", \"fadr\", \"fadl\", \"fadt\", \"fab\", \"fass\", \"fasr\", \"fasl\", \"fast\", \"fasds\", \"fasdr\", \"fasdl\", \"fasdt\", \"faslr\", \"faslpr\", \"fawsb\", \"fatl\", \"fans\", \"fands\", \"faes\", \"fajr\", \"fajfr\", \"fajdr\", \"facr\"].concat(i$1, E$1),\n  _$1 = [\"solid\", \"regular\", \"light\", \"thin\", \"duotone\", \"brands\", \"semibold\"],\n  K$1 = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],\n  M$1 = K$1.concat([11, 12, 13, 14, 15, 16, 17, 18, 19, 20]),\n  O$1 = [\"aw\", \"fw\", \"pull-left\", \"pull-right\"],\n  Ho$1 = [].concat(_toConsumableArray(Object.keys(J$1)), _$1, O$1, [\"2xs\", \"xs\", \"sm\", \"lg\", \"xl\", \"2xl\", \"beat\", \"border\", \"fade\", \"beat-fade\", \"bounce\", \"flip-both\", \"flip-horizontal\", \"flip-vertical\", \"flip\", \"inverse\", \"layers\", \"layers-bottom-left\", \"layers-bottom-right\", \"layers-counter\", \"layers-text\", \"layers-top-left\", \"layers-top-right\", \"li\", \"pull-end\", \"pull-start\", \"pulse\", \"rotate-180\", \"rotate-270\", \"rotate-90\", \"rotate-by\", \"shake\", \"spin-pulse\", \"spin-reverse\", \"spin\", \"stack-1x\", \"stack-2x\", \"stack\", \"ul\", \"width-auto\", \"width-fixed\", l$1.GROUP, l$1.SWAP_OPACITY, l$1.PRIMARY, l$1.SECONDARY]).concat(K$1.map(function (o) {\n    return \"\".concat(o, \"x\");\n  })).concat(M$1.map(function (o) {\n    return \"w-\".concat(o);\n  }));\nvar oa = {\n    \"Font Awesome 5 Free\": {\n      900: \"fas\",\n      400: \"far\"\n    },\n    \"Font Awesome 5 Pro\": {\n      900: \"fas\",\n      400: \"far\",\n      normal: \"far\",\n      300: \"fal\"\n    },\n    \"Font Awesome 5 Brands\": {\n      400: \"fab\",\n      normal: \"fab\"\n    },\n    \"Font Awesome 5 Duotone\": {\n      900: \"fad\"\n    }\n  };\n\nvar NAMESPACE_IDENTIFIER = '___FONT_AWESOME___';\nvar UNITS_IN_GRID = 16;\nvar DEFAULT_CSS_PREFIX = 'fa';\nvar DEFAULT_REPLACEMENT_CLASS = 'svg-inline--fa';\nvar DATA_FA_I2SVG = 'data-fa-i2svg';\nvar DATA_FA_PSEUDO_ELEMENT = 'data-fa-pseudo-element';\nvar DATA_FA_PSEUDO_ELEMENT_PENDING = 'data-fa-pseudo-element-pending';\nvar DATA_PREFIX = 'data-prefix';\nvar DATA_ICON = 'data-icon';\nvar HTML_CLASS_I2SVG_BASE_CLASS = 'fontawesome-i2svg';\nvar MUTATION_APPROACH_ASYNC = 'async';\nvar TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS = ['HTML', 'HEAD', 'STYLE', 'SCRIPT'];\nvar PSEUDO_ELEMENTS = ['::before', '::after', ':before', ':after'];\nvar PRODUCTION = function () {\n  try {\n    return process.env.NODE_ENV === 'production';\n  } catch (e$$1) {\n    return false;\n  }\n}();\nfunction familyProxy(obj) {\n  // Defaults to the classic family if family is not available\n  return new Proxy(obj, {\n    get: function get(target, prop) {\n      return prop in target ? target[prop] : target[a];\n    }\n  });\n}\nvar _PREFIX_TO_STYLE = _objectSpread2({}, z);\n\n// We changed FACSSClassesToStyleId in the icons repo to be canonical and as such, \"classic\" family does not have any\n// duotone styles.  But we do still need duotone in _PREFIX_TO_STYLE below, so we are manually adding\n// {'fa-duotone': 'duotone'}\n_PREFIX_TO_STYLE[a] = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\n  'fa-duotone': 'duotone'\n}), z[a]), Oo['kit']), Oo['kit-duotone']);\nvar PREFIX_TO_STYLE = familyProxy(_PREFIX_TO_STYLE);\nvar _STYLE_TO_PREFIX = _objectSpread2({}, Ro);\n\n// We changed FAStyleIdToShortPrefixId in the icons repo to be canonical and as such, \"classic\" family does not have any\n// duotone styles.  But we do still need duotone in _STYLE_TO_PREFIX below, so we are manually adding {duotone: 'fad'}\n_STYLE_TO_PREFIX[a] = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\n  duotone: 'fad'\n}), _STYLE_TO_PREFIX[a]), dt['kit']), dt['kit-duotone']);\nvar STYLE_TO_PREFIX = familyProxy(_STYLE_TO_PREFIX);\nvar _PREFIX_TO_LONG_STYLE = _objectSpread2({}, Go$1);\n_PREFIX_TO_LONG_STYLE[a] = _objectSpread2(_objectSpread2({}, _PREFIX_TO_LONG_STYLE[a]), et['kit']);\nvar PREFIX_TO_LONG_STYLE = familyProxy(_PREFIX_TO_LONG_STYLE);\nvar _LONG_STYLE_TO_PREFIX = _objectSpread2({}, zo$1);\n_LONG_STYLE_TO_PREFIX[a] = _objectSpread2(_objectSpread2({}, _LONG_STYLE_TO_PREFIX[a]), Xo['kit']);\nvar LONG_STYLE_TO_PREFIX = familyProxy(_LONG_STYLE_TO_PREFIX);\nvar ICON_SELECTION_SYNTAX_PATTERN = K;\nvar LAYERS_TEXT_CLASSNAME = 'fa-layers-text';\nvar FONT_FAMILY_PATTERN = W;\nvar _FONT_WEIGHT_TO_PREFIX = _objectSpread2({}, io);\nvar FONT_WEIGHT_TO_PREFIX = familyProxy(_FONT_WEIGHT_TO_PREFIX);\nvar ATTRIBUTES_WATCHED_FOR_MUTATION = ['class', 'data-prefix', 'data-icon', 'data-fa-transform', 'data-fa-mask'];\nvar DUOTONE_CLASSES = O;\nvar RESERVED_CLASSES = [].concat(_toConsumableArray(Go), _toConsumableArray(Ho$1));\n\nvar initial = WINDOW.FontAwesomeConfig || {};\nfunction getAttrConfig(attr) {\n  var element = DOCUMENT.querySelector('script[' + attr + ']');\n  if (element) {\n    return element.getAttribute(attr);\n  }\n}\nfunction coerce(val) {\n  // Getting an empty string will occur if the attribute is set on the HTML tag but without a value\n  // We'll assume that this is an indication that it should be toggled to true\n  if (val === '') return true;\n  if (val === 'false') return false;\n  if (val === 'true') return true;\n  return val;\n}\nif (DOCUMENT && typeof DOCUMENT.querySelector === 'function') {\n  var attrs = [['data-family-prefix', 'familyPrefix'], ['data-css-prefix', 'cssPrefix'], ['data-family-default', 'familyDefault'], ['data-style-default', 'styleDefault'], ['data-replacement-class', 'replacementClass'], ['data-auto-replace-svg', 'autoReplaceSvg'], ['data-auto-add-css', 'autoAddCss'], ['data-search-pseudo-elements', 'searchPseudoElements'], ['data-search-pseudo-elements-warnings', 'searchPseudoElementsWarnings'], ['data-search-pseudo-elements-full-scan', 'searchPseudoElementsFullScan'], ['data-observe-mutations', 'observeMutations'], ['data-mutate-approach', 'mutateApproach'], ['data-keep-original-source', 'keepOriginalSource'], ['data-measure-performance', 'measurePerformance'], ['data-show-missing-icons', 'showMissingIcons']];\n  attrs.forEach(function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      attr = _ref2[0],\n      key = _ref2[1];\n    var val = coerce(getAttrConfig(attr));\n    if (val !== undefined && val !== null) {\n      initial[key] = val;\n    }\n  });\n}\nvar _default = {\n  styleDefault: 'solid',\n  familyDefault: a,\n  cssPrefix: DEFAULT_CSS_PREFIX,\n  replacementClass: DEFAULT_REPLACEMENT_CLASS,\n  autoReplaceSvg: true,\n  autoAddCss: true,\n  searchPseudoElements: false,\n  searchPseudoElementsWarnings: true,\n  searchPseudoElementsFullScan: false,\n  observeMutations: true,\n  mutateApproach: 'async',\n  keepOriginalSource: true,\n  measurePerformance: false,\n  showMissingIcons: true\n};\n\n// familyPrefix is deprecated but we must still support it if present\nif (initial.familyPrefix) {\n  initial.cssPrefix = initial.familyPrefix;\n}\nvar _config = _objectSpread2(_objectSpread2({}, _default), initial);\nif (!_config.autoReplaceSvg) _config.observeMutations = false;\nvar config = {};\nObject.keys(_default).forEach(function (key) {\n  Object.defineProperty(config, key, {\n    enumerable: true,\n    set: function set(val) {\n      _config[key] = val;\n      _onChangeCb.forEach(function (cb) {\n        return cb(config);\n      });\n    },\n    get: function get() {\n      return _config[key];\n    }\n  });\n});\n\n// familyPrefix is deprecated as of 6.2.0 and should be removed in 7.0.0\nObject.defineProperty(config, 'familyPrefix', {\n  enumerable: true,\n  set: function set(val) {\n    _config.cssPrefix = val;\n    _onChangeCb.forEach(function (cb) {\n      return cb(config);\n    });\n  },\n  get: function get() {\n    return _config.cssPrefix;\n  }\n});\nWINDOW.FontAwesomeConfig = config;\nvar _onChangeCb = [];\nfunction onChange(cb) {\n  _onChangeCb.push(cb);\n  return function () {\n    _onChangeCb.splice(_onChangeCb.indexOf(cb), 1);\n  };\n}\n\nvar d$2 = UNITS_IN_GRID;\nvar meaninglessTransform = {\n  size: 16,\n  x: 0,\n  y: 0,\n  rotate: 0,\n  flipX: false,\n  flipY: false\n};\nfunction insertCss(css) {\n  if (!css || !IS_DOM) {\n    return;\n  }\n  var style = DOCUMENT.createElement('style');\n  style.setAttribute('type', 'text/css');\n  style.innerHTML = css;\n  var headChildren = DOCUMENT.head.childNodes;\n  var beforeChild = null;\n  for (var i = headChildren.length - 1; i > -1; i--) {\n    var child = headChildren[i];\n    var tagName = (child.tagName || '').toUpperCase();\n    if (['STYLE', 'LINK'].indexOf(tagName) > -1) {\n      beforeChild = child;\n    }\n  }\n  DOCUMENT.head.insertBefore(style, beforeChild);\n  return css;\n}\nvar idPool = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\nfunction nextUniqueId() {\n  var size = 12;\n  var id = '';\n  while (size-- > 0) {\n    id += idPool[Math.random() * 62 | 0];\n  }\n  return id;\n}\nfunction toArray(obj) {\n  var array = [];\n  for (var i = (obj || []).length >>> 0; i--;) {\n    array[i] = obj[i];\n  }\n  return array;\n}\nfunction classArray(node) {\n  if (node.classList) {\n    return toArray(node.classList);\n  } else {\n    return (node.getAttribute('class') || '').split(' ').filter(function (i) {\n      return i;\n    });\n  }\n}\nfunction htmlEscape(str) {\n  return \"\".concat(str).replace(/&/g, '&amp;').replace(/\"/g, '&quot;').replace(/'/g, '&#39;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n}\nfunction joinAttributes(attributes) {\n  return Object.keys(attributes || {}).reduce(function (acc, attributeName) {\n    return acc + \"\".concat(attributeName, \"=\\\"\").concat(htmlEscape(attributes[attributeName]), \"\\\" \");\n  }, '').trim();\n}\nfunction joinStyles(styles) {\n  return Object.keys(styles || {}).reduce(function (acc, styleName) {\n    return acc + \"\".concat(styleName, \": \").concat(styles[styleName].trim(), \";\");\n  }, '');\n}\nfunction transformIsMeaningful(transform) {\n  return transform.size !== meaninglessTransform.size || transform.x !== meaninglessTransform.x || transform.y !== meaninglessTransform.y || transform.rotate !== meaninglessTransform.rotate || transform.flipX || transform.flipY;\n}\nfunction transformForSvg(_ref) {\n  var transform = _ref.transform,\n    containerWidth = _ref.containerWidth,\n    iconWidth = _ref.iconWidth;\n  var outer = {\n    transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n  };\n  var innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n  var innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n  var innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n  var inner = {\n    transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n  };\n  var path = {\n    transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n  };\n  return {\n    outer: outer,\n    inner: inner,\n    path: path\n  };\n}\nfunction transformForCss(_ref2) {\n  var transform = _ref2.transform,\n    _ref2$width = _ref2.width,\n    width = _ref2$width === void 0 ? UNITS_IN_GRID : _ref2$width,\n    _ref2$height = _ref2.height,\n    height = _ref2$height === void 0 ? UNITS_IN_GRID : _ref2$height,\n    _ref2$startCentered = _ref2.startCentered,\n    startCentered = _ref2$startCentered === void 0 ? false : _ref2$startCentered;\n  var val = '';\n  if (startCentered && IS_IE) {\n    val += \"translate(\".concat(transform.x / d$2 - width / 2, \"em, \").concat(transform.y / d$2 - height / 2, \"em) \");\n  } else if (startCentered) {\n    val += \"translate(calc(-50% + \".concat(transform.x / d$2, \"em), calc(-50% + \").concat(transform.y / d$2, \"em)) \");\n  } else {\n    val += \"translate(\".concat(transform.x / d$2, \"em, \").concat(transform.y / d$2, \"em) \");\n  }\n  val += \"scale(\".concat(transform.size / d$2 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / d$2 * (transform.flipY ? -1 : 1), \") \");\n  val += \"rotate(\".concat(transform.rotate, \"deg) \");\n  return val;\n}\n\nvar baseStyles = \":root, :host {\\n  --fa-font-solid: normal 900 1em/1 \\\"Font Awesome 7 Free\\\";\\n  --fa-font-regular: normal 400 1em/1 \\\"Font Awesome 7 Free\\\";\\n  --fa-font-light: normal 300 1em/1 \\\"Font Awesome 7 Pro\\\";\\n  --fa-font-thin: normal 100 1em/1 \\\"Font Awesome 7 Pro\\\";\\n  --fa-font-duotone: normal 900 1em/1 \\\"Font Awesome 7 Duotone\\\";\\n  --fa-font-duotone-regular: normal 400 1em/1 \\\"Font Awesome 7 Duotone\\\";\\n  --fa-font-duotone-light: normal 300 1em/1 \\\"Font Awesome 7 Duotone\\\";\\n  --fa-font-duotone-thin: normal 100 1em/1 \\\"Font Awesome 7 Duotone\\\";\\n  --fa-font-brands: normal 400 1em/1 \\\"Font Awesome 7 Brands\\\";\\n  --fa-font-sharp-solid: normal 900 1em/1 \\\"Font Awesome 7 Sharp\\\";\\n  --fa-font-sharp-regular: normal 400 1em/1 \\\"Font Awesome 7 Sharp\\\";\\n  --fa-font-sharp-light: normal 300 1em/1 \\\"Font Awesome 7 Sharp\\\";\\n  --fa-font-sharp-thin: normal 100 1em/1 \\\"Font Awesome 7 Sharp\\\";\\n  --fa-font-sharp-duotone-solid: normal 900 1em/1 \\\"Font Awesome 7 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-regular: normal 400 1em/1 \\\"Font Awesome 7 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-light: normal 300 1em/1 \\\"Font Awesome 7 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-thin: normal 100 1em/1 \\\"Font Awesome 7 Sharp Duotone\\\";\\n  --fa-font-slab-regular: normal 400 1em/1 \\\"Font Awesome 7 Slab\\\";\\n  --fa-font-slab-press-regular: normal 400 1em/1 \\\"Font Awesome 7 Slab Press\\\";\\n  --fa-font-whiteboard-semibold: normal 600 1em/1 \\\"Font Awesome 7 Whiteboard\\\";\\n  --fa-font-thumbprint-light: normal 300 1em/1 \\\"Font Awesome 7 Thumbprint\\\";\\n  --fa-font-notdog-solid: normal 900 1em/1 \\\"Font Awesome 7 Notdog\\\";\\n  --fa-font-notdog-duo-solid: normal 900 1em/1 \\\"Font Awesome 7 Notdog Duo\\\";\\n  --fa-font-etch-solid: normal 900 1em/1 \\\"Font Awesome 7 Etch\\\";\\n  --fa-font-jelly-regular: normal 400 1em/1 \\\"Font Awesome 7 Jelly\\\";\\n  --fa-font-jelly-fill-regular: normal 400 1em/1 \\\"Font Awesome 7 Jelly Fill\\\";\\n  --fa-font-jelly-duo-regular: normal 400 1em/1 \\\"Font Awesome 7 Jelly Duo\\\";\\n  --fa-font-chisel-regular: normal 400 1em/1 \\\"Font Awesome 7 Chisel\\\";\\n}\\n\\n.svg-inline--fa {\\n  box-sizing: content-box;\\n  display: var(--fa-display, inline-block);\\n  height: 1em;\\n  overflow: visible;\\n  vertical-align: -0.125em;\\n  width: var(--fa-width, 1.25em);\\n}\\n.svg-inline--fa.fa-2xs {\\n  vertical-align: 0.1em;\\n}\\n.svg-inline--fa.fa-xs {\\n  vertical-align: 0em;\\n}\\n.svg-inline--fa.fa-sm {\\n  vertical-align: -0.0714285714em;\\n}\\n.svg-inline--fa.fa-lg {\\n  vertical-align: -0.2em;\\n}\\n.svg-inline--fa.fa-xl {\\n  vertical-align: -0.25em;\\n}\\n.svg-inline--fa.fa-2xl {\\n  vertical-align: -0.3125em;\\n}\\n.svg-inline--fa.fa-pull-left,\\n.svg-inline--fa .fa-pull-start {\\n  float: inline-start;\\n  margin-inline-end: var(--fa-pull-margin, 0.3em);\\n}\\n.svg-inline--fa.fa-pull-right,\\n.svg-inline--fa .fa-pull-end {\\n  float: inline-end;\\n  margin-inline-start: var(--fa-pull-margin, 0.3em);\\n}\\n.svg-inline--fa.fa-li {\\n  width: var(--fa-li-width, 2em);\\n  inset-inline-start: calc(-1 * var(--fa-li-width, 2em));\\n  inset-block-start: 0.25em; /* syncing vertical alignment with Web Font rendering */\\n}\\n\\n.fa-layers-counter, .fa-layers-text {\\n  display: inline-block;\\n  position: absolute;\\n  text-align: center;\\n}\\n\\n.fa-layers {\\n  display: inline-block;\\n  height: 1em;\\n  position: relative;\\n  text-align: center;\\n  vertical-align: -0.125em;\\n  width: var(--fa-width, 1.25em);\\n}\\n.fa-layers .svg-inline--fa {\\n  inset: 0;\\n  margin: auto;\\n  position: absolute;\\n  transform-origin: center center;\\n}\\n\\n.fa-layers-text {\\n  left: 50%;\\n  top: 50%;\\n  transform: translate(-50%, -50%);\\n  transform-origin: center center;\\n}\\n\\n.fa-layers-counter {\\n  background-color: var(--fa-counter-background-color, #ff253a);\\n  border-radius: var(--fa-counter-border-radius, 1em);\\n  box-sizing: border-box;\\n  color: var(--fa-inverse, #fff);\\n  line-height: var(--fa-counter-line-height, 1);\\n  max-width: var(--fa-counter-max-width, 5em);\\n  min-width: var(--fa-counter-min-width, 1.5em);\\n  overflow: hidden;\\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\\n  right: var(--fa-right, 0);\\n  text-overflow: ellipsis;\\n  top: var(--fa-top, 0);\\n  transform: scale(var(--fa-counter-scale, 0.25));\\n  transform-origin: top right;\\n}\\n\\n.fa-layers-bottom-right {\\n  bottom: var(--fa-bottom, 0);\\n  right: var(--fa-right, 0);\\n  top: auto;\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: bottom right;\\n}\\n\\n.fa-layers-bottom-left {\\n  bottom: var(--fa-bottom, 0);\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: auto;\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: bottom left;\\n}\\n\\n.fa-layers-top-right {\\n  top: var(--fa-top, 0);\\n  right: var(--fa-right, 0);\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: top right;\\n}\\n\\n.fa-layers-top-left {\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: var(--fa-top, 0);\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: top left;\\n}\\n\\n.fa-1x {\\n  font-size: 1em;\\n}\\n\\n.fa-2x {\\n  font-size: 2em;\\n}\\n\\n.fa-3x {\\n  font-size: 3em;\\n}\\n\\n.fa-4x {\\n  font-size: 4em;\\n}\\n\\n.fa-5x {\\n  font-size: 5em;\\n}\\n\\n.fa-6x {\\n  font-size: 6em;\\n}\\n\\n.fa-7x {\\n  font-size: 7em;\\n}\\n\\n.fa-8x {\\n  font-size: 8em;\\n}\\n\\n.fa-9x {\\n  font-size: 9em;\\n}\\n\\n.fa-10x {\\n  font-size: 10em;\\n}\\n\\n.fa-2xs {\\n  font-size: calc(10 / 16 * 1em); /* converts a 10px size into an em-based value that's relative to the scale's 16px base */\\n  line-height: calc(1 / 10 * 1em); /* sets the line-height of the icon back to that of it's parent */\\n  vertical-align: calc((6 / 10 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text's descender */\\n}\\n\\n.fa-xs {\\n  font-size: calc(12 / 16 * 1em); /* converts a 12px size into an em-based value that's relative to the scale's 16px base */\\n  line-height: calc(1 / 12 * 1em); /* sets the line-height of the icon back to that of it's parent */\\n  vertical-align: calc((6 / 12 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text's descender */\\n}\\n\\n.fa-sm {\\n  font-size: calc(14 / 16 * 1em); /* converts a 14px size into an em-based value that's relative to the scale's 16px base */\\n  line-height: calc(1 / 14 * 1em); /* sets the line-height of the icon back to that of it's parent */\\n  vertical-align: calc((6 / 14 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text's descender */\\n}\\n\\n.fa-lg {\\n  font-size: calc(20 / 16 * 1em); /* converts a 20px size into an em-based value that's relative to the scale's 16px base */\\n  line-height: calc(1 / 20 * 1em); /* sets the line-height of the icon back to that of it's parent */\\n  vertical-align: calc((6 / 20 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text's descender */\\n}\\n\\n.fa-xl {\\n  font-size: calc(24 / 16 * 1em); /* converts a 24px size into an em-based value that's relative to the scale's 16px base */\\n  line-height: calc(1 / 24 * 1em); /* sets the line-height of the icon back to that of it's parent */\\n  vertical-align: calc((6 / 24 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text's descender */\\n}\\n\\n.fa-2xl {\\n  font-size: calc(32 / 16 * 1em); /* converts a 32px size into an em-based value that's relative to the scale's 16px base */\\n  line-height: calc(1 / 32 * 1em); /* sets the line-height of the icon back to that of it's parent */\\n  vertical-align: calc((6 / 32 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text's descender */\\n}\\n\\n.fa-width-auto {\\n  --fa-width: auto;\\n}\\n\\n.fa-fw,\\n.fa-width-fixed {\\n  --fa-width: 1.25em;\\n}\\n\\n.fa-ul {\\n  list-style-type: none;\\n  margin-inline-start: var(--fa-li-margin, 2.5em);\\n  padding-inline-start: 0;\\n}\\n.fa-ul > li {\\n  position: relative;\\n}\\n\\n.fa-li {\\n  inset-inline-start: calc(-1 * var(--fa-li-width, 2em));\\n  position: absolute;\\n  text-align: center;\\n  width: var(--fa-li-width, 2em);\\n  line-height: inherit;\\n}\\n\\n/* Heads Up: Bordered Icons will not be supported in the future!\\n  - This feature will be deprecated in the next major release of Font Awesome (v8)!\\n  - You may continue to use it in this version *v7), but it will not be supported in Font Awesome v8.\\n*/\\n/* Notes:\\n* --@{v.$css-prefix}-border-width = 1/16 by default (to render as ~1px based on a 16px default font-size)\\n* --@{v.$css-prefix}-border-padding =\\n  ** 3/16 for vertical padding (to give ~2px of vertical whitespace around an icon considering it's vertical alignment)\\n  ** 4/16 for horizontal padding (to give ~4px of horizontal whitespace around an icon)\\n*/\\n.fa-border {\\n  border-color: var(--fa-border-color, #eee);\\n  border-radius: var(--fa-border-radius, 0.1em);\\n  border-style: var(--fa-border-style, solid);\\n  border-width: var(--fa-border-width, 0.0625em);\\n  box-sizing: var(--fa-border-box-sizing, content-box);\\n  padding: var(--fa-border-padding, 0.1875em 0.25em);\\n}\\n\\n.fa-pull-left,\\n.fa-pull-start {\\n  float: inline-start;\\n  margin-inline-end: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-pull-right,\\n.fa-pull-end {\\n  float: inline-end;\\n  margin-inline-start: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-beat {\\n  animation-name: fa-beat;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-bounce {\\n  animation-name: fa-bounce;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\\n}\\n\\n.fa-fade {\\n  animation-name: fa-fade;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-beat-fade {\\n  animation-name: fa-beat-fade;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-flip {\\n  animation-name: fa-flip;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-shake {\\n  animation-name: fa-shake;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin {\\n  animation-name: fa-spin;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 2s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin-reverse {\\n  --fa-animation-direction: reverse;\\n}\\n\\n.fa-pulse,\\n.fa-spin-pulse {\\n  animation-name: fa-spin;\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, steps(8));\\n}\\n\\n@media (prefers-reduced-motion: reduce) {\\n  .fa-beat,\\n  .fa-bounce,\\n  .fa-fade,\\n  .fa-beat-fade,\\n  .fa-flip,\\n  .fa-pulse,\\n  .fa-shake,\\n  .fa-spin,\\n  .fa-spin-pulse {\\n    animation: none !important;\\n    transition: none !important;\\n  }\\n}\\n@keyframes fa-beat {\\n  0%, 90% {\\n    transform: scale(1);\\n  }\\n  45% {\\n    transform: scale(var(--fa-beat-scale, 1.25));\\n  }\\n}\\n@keyframes fa-bounce {\\n  0% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n  10% {\\n    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\\n  }\\n  30% {\\n    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\\n  }\\n  50% {\\n    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\\n  }\\n  57% {\\n    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\\n  }\\n  64% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n  100% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n}\\n@keyframes fa-fade {\\n  50% {\\n    opacity: var(--fa-fade-opacity, 0.4);\\n  }\\n}\\n@keyframes fa-beat-fade {\\n  0%, 100% {\\n    opacity: var(--fa-beat-fade-opacity, 0.4);\\n    transform: scale(1);\\n  }\\n  50% {\\n    opacity: 1;\\n    transform: scale(var(--fa-beat-fade-scale, 1.125));\\n  }\\n}\\n@keyframes fa-flip {\\n  50% {\\n    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\\n  }\\n}\\n@keyframes fa-shake {\\n  0% {\\n    transform: rotate(-15deg);\\n  }\\n  4% {\\n    transform: rotate(15deg);\\n  }\\n  8%, 24% {\\n    transform: rotate(-18deg);\\n  }\\n  12%, 28% {\\n    transform: rotate(18deg);\\n  }\\n  16% {\\n    transform: rotate(-22deg);\\n  }\\n  20% {\\n    transform: rotate(22deg);\\n  }\\n  32% {\\n    transform: rotate(-12deg);\\n  }\\n  36% {\\n    transform: rotate(12deg);\\n  }\\n  40%, 100% {\\n    transform: rotate(0deg);\\n  }\\n}\\n@keyframes fa-spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.fa-rotate-90 {\\n  transform: rotate(90deg);\\n}\\n\\n.fa-rotate-180 {\\n  transform: rotate(180deg);\\n}\\n\\n.fa-rotate-270 {\\n  transform: rotate(270deg);\\n}\\n\\n.fa-flip-horizontal {\\n  transform: scale(-1, 1);\\n}\\n\\n.fa-flip-vertical {\\n  transform: scale(1, -1);\\n}\\n\\n.fa-flip-both,\\n.fa-flip-horizontal.fa-flip-vertical {\\n  transform: scale(-1, -1);\\n}\\n\\n.fa-rotate-by {\\n  transform: rotate(var(--fa-rotate-angle, 0));\\n}\\n\\n.svg-inline--fa .fa-primary {\\n  fill: var(--fa-primary-color, currentColor);\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa .fa-secondary {\\n  fill: var(--fa-secondary-color, currentColor);\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-primary {\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa mask .fa-primary,\\n.svg-inline--fa mask .fa-secondary {\\n  fill: black;\\n}\\n\\n.svg-inline--fa.fa-inverse {\\n  fill: var(--fa-inverse, #fff);\\n}\\n\\n.fa-stack {\\n  display: inline-block;\\n  height: 2em;\\n  line-height: 2em;\\n  position: relative;\\n  vertical-align: middle;\\n  width: 2.5em;\\n}\\n\\n.fa-inverse {\\n  color: var(--fa-inverse, #fff);\\n}\\n\\n.svg-inline--fa.fa-stack-1x {\\n  height: 1em;\\n  width: 1.25em;\\n}\\n.svg-inline--fa.fa-stack-2x {\\n  height: 2em;\\n  width: 2.5em;\\n}\\n\\n.fa-stack-1x,\\n.fa-stack-2x {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n  z-index: var(--fa-stack-z-index, auto);\\n}\";\n\nfunction css() {\n  var dcp = DEFAULT_CSS_PREFIX;\n  var drc = DEFAULT_REPLACEMENT_CLASS;\n  var fp = config.cssPrefix;\n  var rc = config.replacementClass;\n  var s = baseStyles;\n  if (fp !== dcp || rc !== drc) {\n    var dPatt = new RegExp(\"\\\\.\".concat(dcp, \"\\\\-\"), 'g');\n    var customPropPatt = new RegExp(\"\\\\--\".concat(dcp, \"\\\\-\"), 'g');\n    var rPatt = new RegExp(\"\\\\.\".concat(drc), 'g');\n    s = s.replace(dPatt, \".\".concat(fp, \"-\")).replace(customPropPatt, \"--\".concat(fp, \"-\")).replace(rPatt, \".\".concat(rc));\n  }\n  return s;\n}\nvar _cssInserted = false;\nfunction ensureCss() {\n  if (config.autoAddCss && !_cssInserted) {\n    insertCss(css());\n    _cssInserted = true;\n  }\n}\nvar InjectCSS = {\n  mixout: function mixout() {\n    return {\n      dom: {\n        css: css,\n        insertCss: ensureCss\n      }\n    };\n  },\n  hooks: function hooks() {\n    return {\n      beforeDOMElementCreation: function beforeDOMElementCreation() {\n        ensureCss();\n      },\n      beforeI2svg: function beforeI2svg() {\n        ensureCss();\n      }\n    };\n  }\n};\n\nvar w$2 = WINDOW || {};\nif (!w$2[NAMESPACE_IDENTIFIER]) w$2[NAMESPACE_IDENTIFIER] = {};\nif (!w$2[NAMESPACE_IDENTIFIER].styles) w$2[NAMESPACE_IDENTIFIER].styles = {};\nif (!w$2[NAMESPACE_IDENTIFIER].hooks) w$2[NAMESPACE_IDENTIFIER].hooks = {};\nif (!w$2[NAMESPACE_IDENTIFIER].shims) w$2[NAMESPACE_IDENTIFIER].shims = [];\nvar namespace = w$2[NAMESPACE_IDENTIFIER];\n\nvar functions = [];\nvar _listener = function listener() {\n  DOCUMENT.removeEventListener('DOMContentLoaded', _listener);\n  loaded = 1;\n  functions.map(function (fn) {\n    return fn();\n  });\n};\nvar loaded = false;\nif (IS_DOM) {\n  loaded = (DOCUMENT.documentElement.doScroll ? /^loaded|^c/ : /^loaded|^i|^c/).test(DOCUMENT.readyState);\n  if (!loaded) DOCUMENT.addEventListener('DOMContentLoaded', _listener);\n}\nfunction domready (fn) {\n  if (!IS_DOM) return;\n  loaded ? setTimeout(fn, 0) : functions.push(fn);\n}\n\nfunction toHtml(abstractNodes) {\n  var tag = abstractNodes.tag,\n    _abstractNodes$attrib = abstractNodes.attributes,\n    attributes = _abstractNodes$attrib === void 0 ? {} : _abstractNodes$attrib,\n    _abstractNodes$childr = abstractNodes.children,\n    children = _abstractNodes$childr === void 0 ? [] : _abstractNodes$childr;\n  if (typeof abstractNodes === 'string') {\n    return htmlEscape(abstractNodes);\n  } else {\n    return \"<\".concat(tag, \" \").concat(joinAttributes(attributes), \">\").concat(children.map(toHtml).join(''), \"</\").concat(tag, \">\");\n  }\n}\n\nfunction iconFromMapping(mapping, prefix, iconName) {\n  if (mapping && mapping[prefix] && mapping[prefix][iconName]) {\n    return {\n      prefix: prefix,\n      iconName: iconName,\n      icon: mapping[prefix][iconName]\n    };\n  }\n}\n\n/**\n * Internal helper to bind a function known to have 4 arguments\n * to a given context.\n */\nvar bindInternal4 = function bindInternal4(func, thisContext) {\n  return function (a, b, c, d) {\n    return func.call(thisContext, a, b, c, d);\n  };\n};\n\n/**\n * # Reduce\n *\n * A fast object `.reduce()` implementation.\n *\n * @param  {Object}   subject      The object to reduce over.\n * @param  {Function} fn           The reducer function.\n * @param  {mixed}    initialValue The initial value for the reducer, defaults to subject[0].\n * @param  {Object}   thisContext  The context for the reducer.\n * @return {mixed}                 The final result.\n */\nvar reduce = function fastReduceObject(subject, fn, initialValue, thisContext) {\n  var keys = Object.keys(subject),\n    length = keys.length,\n    iterator = thisContext !== undefined ? bindInternal4(fn, thisContext) : fn,\n    i,\n    key,\n    result;\n  if (initialValue === undefined) {\n    i = 1;\n    result = subject[keys[0]];\n  } else {\n    i = 0;\n    result = initialValue;\n  }\n  for (; i < length; i++) {\n    key = keys[i];\n    result = iterator(result, subject[key], key, subject);\n  }\n  return result;\n};\n\n/**\n * Return hexadecimal string for a unicode character\n * Returns `null` when more than one character (not bytes!) are passed\n * For example: 'K' → '7B'\n */\nfunction toHex(unicode) {\n  if (_toConsumableArray(unicode).length !== 1) return null;\n  return unicode.codePointAt(0).toString(16);\n}\n\nfunction normalizeIcons(icons) {\n  return Object.keys(icons).reduce(function (acc, iconName) {\n    var icon = icons[iconName];\n    var expanded = !!icon.icon;\n    if (expanded) {\n      acc[icon.iconName] = icon.icon;\n    } else {\n      acc[iconName] = icon;\n    }\n    return acc;\n  }, {});\n}\nfunction defineIcons(prefix, icons) {\n  var params = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var _params$skipHooks = params.skipHooks,\n    skipHooks = _params$skipHooks === void 0 ? false : _params$skipHooks;\n  var normalized = normalizeIcons(icons);\n  if (typeof namespace.hooks.addPack === 'function' && !skipHooks) {\n    namespace.hooks.addPack(prefix, normalizeIcons(icons));\n  } else {\n    namespace.styles[prefix] = _objectSpread2(_objectSpread2({}, namespace.styles[prefix] || {}), normalized);\n  }\n\n  /**\n   * Font Awesome 4 used the prefix of `fa` for all icons. With the introduction\n   * of new styles we needed to differentiate between them. Prefix `fa` is now an alias\n   * for `fas` so we'll ease the upgrade process for our users by automatically defining\n   * this as well.\n   */\n  if (prefix === 'fas') {\n    defineIcons('fa', icons);\n  }\n}\n\nvar MONO = 0x1;\nvar DUO = 0x2;\n\n// New v7 compatible matchers using the style attribute to determine layer membership\nvar modernMatches = [[DUO, /*#__PURE__*/_wrapRegExp(/path opacity=\"([^\"]*)\".*d=\"([^\"]*)\".*path.*d=\"([^\"]*)\"/, {\n  d2: 2,\n  d1: 3\n})], [DUO, /*#__PURE__*/_wrapRegExp(/path opacity=\"([^\"]*)\".*d=\"([^\"]*)\"/, {\n  d2: 2\n})], [MONO, /*#__PURE__*/_wrapRegExp(/path fill=\"currentColor\".*d=\"([^\"]+)\"/, {\n  d1: 1\n})]];\n\n// Old <= v6 matchers, these should rarely get used.\n//\n// Why are there so many? Over the lifecycle of version 5 and 6 we modified the\n// syntax for our source SVG files several times. These patterns match the\n// historical record and attempt to handle backwards-compatibility in rare edge\n// cases (loading version 6 SVG source files using the version 7 SVG parser)\nvar legacyMatches = [[DUO, /*#__PURE__*/_wrapRegExp(/path class=\"([^\"]*secondary[^\"]*)\".*d=\"([^\"]+)\".*path class=\"([^\"]*primary[^\"]*)\".*d=\"([^\"]+)\"/, {\n  attr2: 1,\n  d2: 2,\n  attr1: 3,\n  d1: 4\n})], [DUO, /*#__PURE__*/_wrapRegExp(/path class=\"([^\"]*primary[^\"]*)\".*d=\"([^\"]+)\".*path class=\"([^\"]*secondary[^\"]*)\".*d=\"([^\"]+)\"/, {\n  attr1: 1,\n  d1: 2,\n  attr2: 3,\n  d2: 4\n})], [DUO, /*#__PURE__*/_wrapRegExp(/path class=\"([^\"]*primary[^\"]*)\".*d=\"([^\"]+)\"/, {\n  attr1: 1,\n  d1: 2\n})], [DUO, /*#__PURE__*/_wrapRegExp(/path class=\"([^\"]*secondary[^\"]*)\".*d=\"([^\"]+)\"/, {\n  attr2: 1,\n  d2: 2\n})], [DUO, /*#__PURE__*/_wrapRegExp(/path d=\"([^\"]+)\".*class=\"([^\"]*secondary[^\"]*)\".*path d=\"([^\"]+)\".*class=\"([^\"]*primary[^\"]*)\"/, {\n  d2: 1,\n  attr2: 2,\n  d1: 3,\n  attr1: 4\n})], [DUO, /*#__PURE__*/_wrapRegExp(/path d=\"([^\"]+)\".*class=\"([^\"]*primary[^\"]*)\".*path d=\"([^\"]+)\".*class=\"([^\"]*secondary[^\"]*)\"/, {\n  d1: 1,\n  attr1: 2,\n  d2: 3,\n  attr2: 4\n})], [DUO, /*#__PURE__*/_wrapRegExp(/path d=\"([^\"]+)\".*class=\"([^\"]*primary[^\"]*)\"/, {\n  d1: 1,\n  attr1: 2\n})], [DUO, /*#__PURE__*/_wrapRegExp(/path d=\"([^\"]+)\".*class=\"([^\"]*secondary[^\"]*)\"/, {\n  d2: 1,\n  attr2: 2\n})], [DUO, /*#__PURE__*/_wrapRegExp(/path d=\"([^\"]+)\".*path d=\"([^\"]+)\"/, {\n  d1: 1,\n  d2: 2\n})], [MONO, /*#__PURE__*/_wrapRegExp(/path d=\"([^\"]+)\"/, {\n  d1: 1\n})], [MONO, /*#__PURE__*/_wrapRegExp(/path style=\"([^\"]+)\".*d=\"([^\"]+)\"/, {\n  attr1: 1,\n  d1: 2\n})]];\n\nvar styles = namespace.styles,\n  shims = namespace.shims;\nvar FAMILY_NAMES = Object.keys(PREFIX_TO_LONG_STYLE);\nvar PREFIXES_FOR_FAMILY = FAMILY_NAMES.reduce(function (acc, familyId) {\n  acc[familyId] = Object.keys(PREFIX_TO_LONG_STYLE[familyId]);\n  return acc;\n}, {});\nvar _defaultUsablePrefix = null;\nvar _byUnicode = {};\nvar _byLigature = {};\nvar _byOldName = {};\nvar _byOldUnicode = {};\nvar _byAlias = {};\nfunction isReserved(name) {\n  return ~RESERVED_CLASSES.indexOf(name);\n}\nfunction getIconName(cssPrefix, cls) {\n  var parts = cls.split('-');\n  var prefix = parts[0];\n  var iconName = parts.slice(1).join('-');\n  if (prefix === cssPrefix && iconName !== '' && !isReserved(iconName)) {\n    return iconName;\n  } else {\n    return null;\n  }\n}\nvar build = function build() {\n  var lookup = function lookup(reducer) {\n    return reduce(styles, function (o$$1, style, prefix) {\n      o$$1[prefix] = reduce(style, reducer, {});\n      return o$$1;\n    }, {});\n  };\n  _byUnicode = lookup(function (acc, icon, iconName) {\n    if (icon[3]) {\n      acc[icon[3]] = iconName;\n    }\n    if (icon[2]) {\n      var aliases = icon[2].filter(function (a$$1) {\n        return typeof a$$1 === 'number';\n      });\n      aliases.forEach(function (alias) {\n        acc[alias.toString(16)] = iconName;\n      });\n    }\n    return acc;\n  });\n  _byLigature = lookup(function (acc, icon, iconName) {\n    acc[iconName] = iconName;\n    if (icon[2]) {\n      var aliases = icon[2].filter(function (a$$1) {\n        return typeof a$$1 === 'string';\n      });\n      aliases.forEach(function (alias) {\n        acc[alias] = iconName;\n      });\n    }\n    return acc;\n  });\n  _byAlias = lookup(function (acc, icon, iconName) {\n    var aliases = icon[2];\n    acc[iconName] = iconName;\n    aliases.forEach(function (alias) {\n      acc[alias] = iconName;\n    });\n    return acc;\n  });\n\n  // If we have a Kit, we can't determine if regular is available since we\n  // could be auto-fetching it. We'll have to assume that it is available.\n  var hasRegular = 'far' in styles || config.autoFetchSvg;\n  var shimLookups = reduce(shims, function (acc, shim) {\n    var maybeNameMaybeUnicode = shim[0];\n    var prefix = shim[1];\n    var iconName = shim[2];\n    if (prefix === 'far' && !hasRegular) {\n      prefix = 'fas';\n    }\n    if (typeof maybeNameMaybeUnicode === 'string') {\n      acc.names[maybeNameMaybeUnicode] = {\n        prefix: prefix,\n        iconName: iconName\n      };\n    }\n    if (typeof maybeNameMaybeUnicode === 'number') {\n      acc.unicodes[maybeNameMaybeUnicode.toString(16)] = {\n        prefix: prefix,\n        iconName: iconName\n      };\n    }\n    return acc;\n  }, {\n    names: {},\n    unicodes: {}\n  });\n  _byOldName = shimLookups.names;\n  _byOldUnicode = shimLookups.unicodes;\n  _defaultUsablePrefix = getCanonicalPrefix(config.styleDefault, {\n    family: config.familyDefault\n  });\n};\nonChange(function (c$$1) {\n  _defaultUsablePrefix = getCanonicalPrefix(c$$1.styleDefault, {\n    family: config.familyDefault\n  });\n});\nbuild();\nfunction byUnicode(prefix, unicode) {\n  return (_byUnicode[prefix] || {})[unicode];\n}\nfunction byLigature(prefix, ligature) {\n  return (_byLigature[prefix] || {})[ligature];\n}\nfunction byAlias(prefix, alias) {\n  return (_byAlias[prefix] || {})[alias];\n}\nfunction byOldName(name) {\n  return _byOldName[name] || {\n    prefix: null,\n    iconName: null\n  };\n}\nfunction byOldUnicode(unicode) {\n  var oldUnicode = _byOldUnicode[unicode];\n  var newUnicode = byUnicode('fas', unicode);\n  return oldUnicode || (newUnicode ? {\n    prefix: 'fas',\n    iconName: newUnicode\n  } : null) || {\n    prefix: null,\n    iconName: null\n  };\n}\nfunction getDefaultUsablePrefix() {\n  return _defaultUsablePrefix;\n}\nvar emptyCanonicalIcon = function emptyCanonicalIcon() {\n  return {\n    prefix: null,\n    iconName: null,\n    rest: []\n  };\n};\nfunction getFamilyId(values) {\n  var family = a;\n  var famProps = FAMILY_NAMES.reduce(function (acc, familyId) {\n    acc[familyId] = \"\".concat(config.cssPrefix, \"-\").concat(familyId);\n    return acc;\n  }, {});\n  oo.forEach(function (familyId) {\n    if (values.includes(famProps[familyId]) || values.some(function (v$$1) {\n      return PREFIXES_FOR_FAMILY[familyId].includes(v$$1);\n    })) {\n      family = familyId;\n    }\n  });\n  return family;\n}\nfunction getCanonicalPrefix(styleOrPrefix) {\n  var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _params$family = params.family,\n    family = _params$family === void 0 ? a : _params$family;\n  var style = PREFIX_TO_STYLE[family][styleOrPrefix];\n\n  // handles the exception of passing in only a family of 'duotone' with no style\n  if (family === o && !styleOrPrefix) {\n    return 'fad';\n  }\n  var prefix = STYLE_TO_PREFIX[family][styleOrPrefix] || STYLE_TO_PREFIX[family][style];\n  var defined = styleOrPrefix in namespace.styles ? styleOrPrefix : null;\n  var result = prefix || defined || null;\n  return result;\n}\nfunction moveNonFaClassesToRest(classNames) {\n  var rest = [];\n  var iconName = null;\n  classNames.forEach(function (cls) {\n    var result = getIconName(config.cssPrefix, cls);\n    if (result) {\n      iconName = result;\n    } else if (cls) {\n      rest.push(cls);\n    }\n  });\n  return {\n    iconName: iconName,\n    rest: rest\n  };\n}\nfunction sortedUniqueValues(arr) {\n  return arr.sort().filter(function (value, index, arr) {\n    return arr.indexOf(value) === index;\n  });\n}\nvar _faCombinedClasses = $o$1.concat(zo);\nfunction getCanonicalIcon(values) {\n  var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _params$skipLookups = params.skipLookups,\n    skipLookups = _params$skipLookups === void 0 ? false : _params$skipLookups;\n  var givenPrefix = null;\n  var faStyleOrFamilyClasses = sortedUniqueValues(values.filter(function (cls) {\n    return _faCombinedClasses.includes(cls);\n  }));\n  var nonStyleOrFamilyClasses = sortedUniqueValues(values.filter(function (cls) {\n    return !_faCombinedClasses.includes(cls);\n  }));\n  var faStyles = faStyleOrFamilyClasses.filter(function (cls) {\n    givenPrefix = cls;\n    return !G.includes(cls);\n  });\n  var _faStyles = _slicedToArray(faStyles, 1),\n    _faStyles$ = _faStyles[0],\n    styleFromValues = _faStyles$ === void 0 ? null : _faStyles$;\n  var family = getFamilyId(faStyleOrFamilyClasses);\n  var canonical = _objectSpread2(_objectSpread2({}, moveNonFaClassesToRest(nonStyleOrFamilyClasses)), {}, {\n    prefix: getCanonicalPrefix(styleFromValues, {\n      family: family\n    })\n  });\n  return _objectSpread2(_objectSpread2(_objectSpread2({}, canonical), getDefaultCanonicalPrefix({\n    values: values,\n    family: family,\n    styles: styles,\n    config: config,\n    canonical: canonical,\n    givenPrefix: givenPrefix\n  })), applyShimAndAlias(skipLookups, givenPrefix, canonical));\n}\nfunction applyShimAndAlias(skipLookups, givenPrefix, canonical) {\n  var prefix = canonical.prefix,\n    iconName = canonical.iconName;\n  if (skipLookups || !prefix || !iconName) {\n    return {\n      prefix: prefix,\n      iconName: iconName\n    };\n  }\n  var shim = givenPrefix === 'fa' ? byOldName(iconName) : {};\n  var aliasIconName = byAlias(prefix, iconName);\n  iconName = shim.iconName || aliasIconName || iconName;\n  prefix = shim.prefix || prefix;\n  if (prefix === 'far' && !styles['far'] && styles['fas'] && !config.autoFetchSvg) {\n    // Allow a fallback from the regular style to solid if regular is not available\n    // but only if we aren't auto-fetching SVGs\n    prefix = 'fas';\n  }\n  return {\n    prefix: prefix,\n    iconName: iconName\n  };\n}\nvar newCanonicalFamilies = oo.filter(function (familyId) {\n  return familyId !== a || familyId !== o;\n});\nvar newCanonicalStyles = Object.keys(Go$1).filter(function (key) {\n  return key !== a;\n}).map(function (key) {\n  return Object.keys(Go$1[key]);\n}).flat();\nfunction getDefaultCanonicalPrefix(prefixOptions) {\n  var values = prefixOptions.values,\n    family = prefixOptions.family,\n    canonical = prefixOptions.canonical,\n    _prefixOptions$givenP = prefixOptions.givenPrefix,\n    givenPrefix = _prefixOptions$givenP === void 0 ? '' : _prefixOptions$givenP,\n    _prefixOptions$styles = prefixOptions.styles,\n    styles = _prefixOptions$styles === void 0 ? {} : _prefixOptions$styles,\n    _prefixOptions$config = prefixOptions.config,\n    config$$1 = _prefixOptions$config === void 0 ? {} : _prefixOptions$config;\n  var isDuotoneFamily = family === o;\n  var valuesHasDuotone = values.includes('fa-duotone') || values.includes('fad');\n  var defaultFamilyIsDuotone = config$$1.familyDefault === 'duotone';\n  var canonicalPrefixIsDuotone = canonical.prefix === 'fad' || canonical.prefix === 'fa-duotone';\n  if (!isDuotoneFamily && (valuesHasDuotone || defaultFamilyIsDuotone || canonicalPrefixIsDuotone)) {\n    canonical.prefix = 'fad';\n  }\n  if (values.includes('fa-brands') || values.includes('fab')) {\n    canonical.prefix = 'fab';\n  }\n  if (!canonical.prefix && newCanonicalFamilies.includes(family)) {\n    var validPrefix = Object.keys(styles).find(function (key) {\n      return newCanonicalStyles.includes(key);\n    });\n    if (validPrefix || config$$1.autoFetchSvg) {\n      var defaultPrefix = Co.get(family).defaultShortPrefixId;\n      canonical.prefix = defaultPrefix;\n      canonical.iconName = byAlias(canonical.prefix, canonical.iconName) || canonical.iconName;\n    }\n  }\n  if (canonical.prefix === 'fa' || givenPrefix === 'fa') {\n    // The fa prefix is not canonical. So if it has made it through until this point\n    // we will shift it to the correct prefix.\n    canonical.prefix = getDefaultUsablePrefix() || 'fas';\n  }\n  return canonical;\n}\n\nvar Library = /*#__PURE__*/function () {\n  function Library() {\n    _classCallCheck(this, Library);\n    this.definitions = {};\n  }\n  return _createClass(Library, [{\n    key: \"add\",\n    value: function add() {\n      var _this = this;\n      for (var _len = arguments.length, definitions = new Array(_len), _key = 0; _key < _len; _key++) {\n        definitions[_key] = arguments[_key];\n      }\n      var additions = definitions.reduce(this._pullDefinitions, {});\n      Object.keys(additions).forEach(function (key) {\n        _this.definitions[key] = _objectSpread2(_objectSpread2({}, _this.definitions[key] || {}), additions[key]);\n        defineIcons(key, additions[key]);\n        build();\n      });\n    }\n  }, {\n    key: \"reset\",\n    value: function reset() {\n      this.definitions = {};\n    }\n  }, {\n    key: \"_pullDefinitions\",\n    value: function _pullDefinitions(additions, definition) {\n      var normalized = definition.prefix && definition.iconName && definition.icon ? {\n        0: definition\n      } : definition;\n      Object.keys(normalized).map(function (key) {\n        var _normalized$key = normalized[key],\n          prefix = _normalized$key.prefix,\n          iconName = _normalized$key.iconName,\n          icon = _normalized$key.icon;\n        var aliases = icon[2];\n        if (!additions[prefix]) additions[prefix] = {};\n        if (aliases.length > 0) {\n          aliases.forEach(function (alias) {\n            if (typeof alias === 'string') {\n              additions[prefix][alias] = icon;\n            }\n          });\n        }\n        additions[prefix][iconName] = icon;\n      });\n      return additions;\n    }\n  }]);\n}();\n\nvar _plugins = [];\nvar _hooks = {};\nvar providers = {};\nvar defaultProviderKeys = Object.keys(providers);\nfunction registerPlugins(nextPlugins, _ref) {\n  var obj = _ref.mixoutsTo;\n  _plugins = nextPlugins;\n  _hooks = {};\n  Object.keys(providers).forEach(function (k) {\n    if (defaultProviderKeys.indexOf(k) === -1) {\n      delete providers[k];\n    }\n  });\n  _plugins.forEach(function (plugin) {\n    var mixout = plugin.mixout ? plugin.mixout() : {};\n    Object.keys(mixout).forEach(function (tk) {\n      if (typeof mixout[tk] === 'function') {\n        obj[tk] = mixout[tk];\n      }\n      if (_typeof(mixout[tk]) === 'object') {\n        Object.keys(mixout[tk]).forEach(function (sk) {\n          if (!obj[tk]) {\n            obj[tk] = {};\n          }\n          obj[tk][sk] = mixout[tk][sk];\n        });\n      }\n    });\n    if (plugin.hooks) {\n      var hooks = plugin.hooks();\n      Object.keys(hooks).forEach(function (hook) {\n        if (!_hooks[hook]) {\n          _hooks[hook] = [];\n        }\n        _hooks[hook].push(hooks[hook]);\n      });\n    }\n    if (plugin.provides) {\n      plugin.provides(providers);\n    }\n  });\n  return obj;\n}\nfunction chainHooks(hook, accumulator) {\n  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    args[_key - 2] = arguments[_key];\n  }\n  var hookFns = _hooks[hook] || [];\n  hookFns.forEach(function (hookFn) {\n    accumulator = hookFn.apply(null, [accumulator].concat(args));\n  });\n  return accumulator;\n}\nfunction callHooks(hook) {\n  for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    args[_key2 - 1] = arguments[_key2];\n  }\n  var hookFns = _hooks[hook] || [];\n  hookFns.forEach(function (hookFn) {\n    hookFn.apply(null, args);\n  });\n  return undefined;\n}\nfunction callProvided() {\n  var hook = arguments[0];\n  var args = Array.prototype.slice.call(arguments, 1);\n  return providers[hook] ? providers[hook].apply(null, args) : undefined;\n}\n\nfunction findIconDefinition(iconLookup) {\n  if (iconLookup.prefix === 'fa') {\n    iconLookup.prefix = 'fas';\n  }\n  var iconName = iconLookup.iconName;\n  var prefix = iconLookup.prefix || getDefaultUsablePrefix();\n  if (!iconName) return;\n  iconName = byAlias(prefix, iconName) || iconName;\n  return iconFromMapping(library.definitions, prefix, iconName) || iconFromMapping(namespace.styles, prefix, iconName);\n}\nvar library = new Library();\nvar noAuto = function noAuto() {\n  config.autoReplaceSvg = false;\n  config.observeMutations = false;\n  callHooks('noAuto');\n};\nvar dom = {\n  i2svg: function i2svg() {\n    var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (IS_DOM) {\n      callHooks('beforeI2svg', params);\n      callProvided('pseudoElements2svg', params);\n      return callProvided('i2svg', params);\n    } else {\n      return Promise.reject(new Error('Operation requires a DOM of some kind.'));\n    }\n  },\n  watch: function watch() {\n    var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var autoReplaceSvgRoot = params.autoReplaceSvgRoot;\n    if (config.autoReplaceSvg === false) {\n      config.autoReplaceSvg = true;\n    }\n    config.observeMutations = true;\n    domready(function () {\n      autoReplace({\n        autoReplaceSvgRoot: autoReplaceSvgRoot\n      });\n      callHooks('watch', params);\n    });\n  }\n};\nvar parse = {\n  icon: function icon(_icon) {\n    if (_icon === null) {\n      return null;\n    }\n    if (_typeof(_icon) === 'object' && _icon.prefix && _icon.iconName) {\n      return {\n        prefix: _icon.prefix,\n        iconName: byAlias(_icon.prefix, _icon.iconName) || _icon.iconName\n      };\n    }\n    if (Array.isArray(_icon) && _icon.length === 2) {\n      var iconName = _icon[1].indexOf('fa-') === 0 ? _icon[1].slice(3) : _icon[1];\n      var prefix = getCanonicalPrefix(_icon[0]);\n      return {\n        prefix: prefix,\n        iconName: byAlias(prefix, iconName) || iconName\n      };\n    }\n    if (typeof _icon === 'string' && (_icon.indexOf(\"\".concat(config.cssPrefix, \"-\")) > -1 || _icon.match(ICON_SELECTION_SYNTAX_PATTERN))) {\n      var canonicalIcon = getCanonicalIcon(_icon.split(' '), {\n        skipLookups: true\n      });\n      return {\n        prefix: canonicalIcon.prefix || getDefaultUsablePrefix(),\n        iconName: byAlias(canonicalIcon.prefix, canonicalIcon.iconName) || canonicalIcon.iconName\n      };\n    }\n    if (typeof _icon === 'string') {\n      var _prefix = getDefaultUsablePrefix();\n      return {\n        prefix: _prefix,\n        iconName: byAlias(_prefix, _icon) || _icon\n      };\n    }\n  }\n};\nvar api = {\n  noAuto: noAuto,\n  config: config,\n  dom: dom,\n  parse: parse,\n  library: library,\n  findIconDefinition: findIconDefinition,\n  toHtml: toHtml\n};\nvar autoReplace = function autoReplace() {\n  var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var _params$autoReplaceSv = params.autoReplaceSvgRoot,\n    autoReplaceSvgRoot = _params$autoReplaceSv === void 0 ? DOCUMENT : _params$autoReplaceSv;\n  if ((Object.keys(namespace.styles).length > 0 || config.autoFetchSvg) && IS_DOM && config.autoReplaceSvg) api.dom.i2svg({\n    node: autoReplaceSvgRoot\n  });\n};\n\nfunction domVariants(val, abstractCreator) {\n  Object.defineProperty(val, 'abstract', {\n    get: abstractCreator\n  });\n  Object.defineProperty(val, 'html', {\n    get: function get() {\n      return val.abstract.map(function (a) {\n        return toHtml(a);\n      });\n    }\n  });\n  Object.defineProperty(val, 'node', {\n    get: function get() {\n      if (!IS_DOM) return undefined;\n      var container = DOCUMENT.createElement('div');\n      container.innerHTML = val.html;\n      return container.children;\n    }\n  });\n  return val;\n}\n\nfunction asIcon (_ref) {\n  var children = _ref.children,\n    main = _ref.main,\n    mask = _ref.mask,\n    attributes = _ref.attributes,\n    styles = _ref.styles,\n    transform = _ref.transform;\n  if (transformIsMeaningful(transform) && main.found && !mask.found) {\n    var width = main.width,\n      height = main.height;\n    var offset = {\n      x: width / height / 2,\n      y: 0.5\n    };\n    attributes['style'] = joinStyles(_objectSpread2(_objectSpread2({}, styles), {}, {\n      'transform-origin': \"\".concat(offset.x + transform.x / 16, \"em \").concat(offset.y + transform.y / 16, \"em\")\n    }));\n  }\n  return [{\n    tag: 'svg',\n    attributes: attributes,\n    children: children\n  }];\n}\n\nfunction asSymbol (_ref) {\n  var prefix = _ref.prefix,\n    iconName = _ref.iconName,\n    children = _ref.children,\n    attributes = _ref.attributes,\n    symbol = _ref.symbol;\n  var id = symbol === true ? \"\".concat(prefix, \"-\").concat(config.cssPrefix, \"-\").concat(iconName) : symbol;\n  return [{\n    tag: 'svg',\n    attributes: {\n      style: 'display: none;'\n    },\n    children: [{\n      tag: 'symbol',\n      attributes: _objectSpread2(_objectSpread2({}, attributes), {}, {\n        id: id\n      }),\n      children: children\n    }]\n  }];\n}\n\n// If any of these attributes are present, don't assume the icon is decorative\nfunction isLabeled(attributes) {\n  var labels = ['aria-label', 'aria-labelledby', 'title', 'role'];\n  return labels.some(function (label) {\n    return label in attributes;\n  });\n}\nfunction makeInlineSvgAbstract(params) {\n  var _params$icons = params.icons,\n    main = _params$icons.main,\n    mask = _params$icons.mask,\n    prefix = params.prefix,\n    iconName = params.iconName,\n    transform = params.transform,\n    symbol = params.symbol,\n    maskId = params.maskId,\n    extra = params.extra,\n    _params$watchable = params.watchable,\n    watchable = _params$watchable === void 0 ? false : _params$watchable;\n  var _ref = mask.found ? mask : main,\n    width = _ref.width,\n    height = _ref.height;\n  var attrClass = [config.replacementClass, iconName ? \"\".concat(config.cssPrefix, \"-\").concat(iconName) : ''].filter(function (c) {\n    return extra.classes.indexOf(c) === -1;\n  }).filter(function (c) {\n    return c !== '' || !!c;\n  }).concat(extra.classes).join(' ');\n  var content = {\n    children: [],\n    attributes: _objectSpread2(_objectSpread2({}, extra.attributes), {}, {\n      'data-prefix': prefix,\n      'data-icon': iconName,\n      'class': attrClass,\n      'role': extra.attributes.role || 'img',\n      'viewBox': \"0 0 \".concat(width, \" \").concat(height)\n    })\n  };\n  if (!isLabeled(extra.attributes) && !extra.attributes['aria-hidden']) {\n    content.attributes['aria-hidden'] = 'true';\n  }\n  if (watchable) {\n    content.attributes[DATA_FA_I2SVG] = '';\n  }\n  var args = _objectSpread2(_objectSpread2({}, content), {}, {\n    prefix: prefix,\n    iconName: iconName,\n    main: main,\n    mask: mask,\n    maskId: maskId,\n    transform: transform,\n    symbol: symbol,\n    styles: _objectSpread2({}, extra.styles)\n  });\n  var _ref2 = mask.found && main.found ? callProvided('generateAbstractMask', args) || {\n      children: [],\n      attributes: {}\n    } : callProvided('generateAbstractIcon', args) || {\n      children: [],\n      attributes: {}\n    },\n    children = _ref2.children,\n    attributes = _ref2.attributes;\n  args.children = children;\n  args.attributes = attributes;\n  if (symbol) {\n    return asSymbol(args);\n  } else {\n    return asIcon(args);\n  }\n}\nfunction makeLayersTextAbstract(params) {\n  var content = params.content,\n    width = params.width,\n    height = params.height,\n    transform = params.transform,\n    extra = params.extra,\n    _params$watchable2 = params.watchable,\n    watchable = _params$watchable2 === void 0 ? false : _params$watchable2;\n  var attributes = _objectSpread2(_objectSpread2({}, extra.attributes), {}, {\n    class: extra.classes.join(' ')\n  });\n  if (watchable) {\n    attributes[DATA_FA_I2SVG] = '';\n  }\n  var styles = _objectSpread2({}, extra.styles);\n  if (transformIsMeaningful(transform)) {\n    styles['transform'] = transformForCss({\n      transform: transform,\n      startCentered: true,\n      width: width,\n      height: height\n    });\n    styles['-webkit-transform'] = styles['transform'];\n  }\n  var styleString = joinStyles(styles);\n  if (styleString.length > 0) {\n    attributes['style'] = styleString;\n  }\n  var val = [];\n  val.push({\n    tag: 'span',\n    attributes: attributes,\n    children: [content]\n  });\n  return val;\n}\nfunction makeLayersCounterAbstract(params) {\n  var content = params.content,\n    extra = params.extra;\n  var attributes = _objectSpread2(_objectSpread2({}, extra.attributes), {}, {\n    class: extra.classes.join(' ')\n  });\n  var styleString = joinStyles(extra.styles);\n  if (styleString.length > 0) {\n    attributes['style'] = styleString;\n  }\n  var val = [];\n  val.push({\n    tag: 'span',\n    attributes: attributes,\n    children: [content]\n  });\n  return val;\n}\n\nvar styles$1 = namespace.styles;\nfunction asFoundIcon(icon) {\n  var width = icon[0];\n  var height = icon[1];\n  var _icon$slice = icon.slice(4),\n    _icon$slice2 = _slicedToArray(_icon$slice, 1),\n    vectorData = _icon$slice2[0];\n  var element = null;\n  if (Array.isArray(vectorData)) {\n    element = {\n      tag: 'g',\n      attributes: {\n        class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.GROUP)\n      },\n      children: [{\n        tag: 'path',\n        attributes: {\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.SECONDARY),\n          fill: 'currentColor',\n          d: vectorData[0]\n        }\n      }, {\n        tag: 'path',\n        attributes: {\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.PRIMARY),\n          fill: 'currentColor',\n          d: vectorData[1]\n        }\n      }]\n    };\n  } else {\n    element = {\n      tag: 'path',\n      attributes: {\n        fill: 'currentColor',\n        d: vectorData\n      }\n    };\n  }\n  return {\n    found: true,\n    width: width,\n    height: height,\n    icon: element\n  };\n}\nvar missingIconResolutionMixin = {\n  found: false,\n  width: 512,\n  height: 512\n};\nfunction maybeNotifyMissing(iconName, prefix) {\n  if (!PRODUCTION && !config.showMissingIcons && iconName) {\n    console.error(\"Icon with name \\\"\".concat(iconName, \"\\\" and prefix \\\"\").concat(prefix, \"\\\" is missing.\"));\n  }\n}\nfunction findIcon(iconName, prefix) {\n  var givenPrefix = prefix;\n  if (prefix === 'fa' && config.styleDefault !== null) {\n    prefix = getDefaultUsablePrefix();\n  }\n  return new Promise(function (resolve, reject) {\n    if (givenPrefix === 'fa') {\n      var shim = byOldName(iconName) || {};\n      iconName = shim.iconName || iconName;\n      prefix = shim.prefix || prefix;\n    }\n    if (iconName && prefix && styles$1[prefix] && styles$1[prefix][iconName]) {\n      var icon = styles$1[prefix][iconName];\n      return resolve(asFoundIcon(icon));\n    }\n    maybeNotifyMissing(iconName, prefix);\n    resolve(_objectSpread2(_objectSpread2({}, missingIconResolutionMixin), {}, {\n      icon: config.showMissingIcons && iconName ? callProvided('missingIconAbstract') || {} : {}\n    }));\n  });\n}\n\nvar noop$1 = function noop() {};\nvar p$2 = config.measurePerformance && PERFORMANCE && PERFORMANCE.mark && PERFORMANCE.measure ? PERFORMANCE : {\n  mark: noop$1,\n  measure: noop$1\n};\nvar preamble = \"FA \\\"7.0.0\\\"\";\nvar begin = function begin(name) {\n  p$2.mark(\"\".concat(preamble, \" \").concat(name, \" begins\"));\n  return function () {\n    return end(name);\n  };\n};\nvar end = function end(name) {\n  p$2.mark(\"\".concat(preamble, \" \").concat(name, \" ends\"));\n  p$2.measure(\"\".concat(preamble, \" \").concat(name), \"\".concat(preamble, \" \").concat(name, \" begins\"), \"\".concat(preamble, \" \").concat(name, \" ends\"));\n};\nvar perf = {\n  begin: begin,\n  end: end\n};\n\nvar noop$2 = function noop() {};\nfunction isWatched(node) {\n  var i2svg = node.getAttribute ? node.getAttribute(DATA_FA_I2SVG) : null;\n  return typeof i2svg === 'string';\n}\nfunction hasPrefixAndIcon(node) {\n  var prefix = node.getAttribute ? node.getAttribute(DATA_PREFIX) : null;\n  var icon = node.getAttribute ? node.getAttribute(DATA_ICON) : null;\n  return prefix && icon;\n}\nfunction hasBeenReplaced(node) {\n  return node && node.classList && node.classList.contains && node.classList.contains(config.replacementClass);\n}\nfunction getMutator() {\n  if (config.autoReplaceSvg === true) {\n    return mutators.replace;\n  }\n  var mutator = mutators[config.autoReplaceSvg];\n  return mutator || mutators.replace;\n}\nfunction createElementNS(tag) {\n  return DOCUMENT.createElementNS('http://www.w3.org/2000/svg', tag);\n}\nfunction createElement(tag) {\n  return DOCUMENT.createElement(tag);\n}\nfunction convertSVG(abstractObj) {\n  var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _params$ceFn = params.ceFn,\n    ceFn = _params$ceFn === void 0 ? abstractObj.tag === 'svg' ? createElementNS : createElement : _params$ceFn;\n  if (typeof abstractObj === 'string') {\n    return DOCUMENT.createTextNode(abstractObj);\n  }\n  var tag = ceFn(abstractObj.tag);\n  Object.keys(abstractObj.attributes || []).forEach(function (key) {\n    tag.setAttribute(key, abstractObj.attributes[key]);\n  });\n  var children = abstractObj.children || [];\n  children.forEach(function (child) {\n    tag.appendChild(convertSVG(child, {\n      ceFn: ceFn\n    }));\n  });\n  return tag;\n}\nfunction nodeAsComment(node) {\n  var comment = \" \".concat(node.outerHTML, \" \");\n  /* BEGIN.ATTRIBUTION */\n  comment = \"\".concat(comment, \"Font Awesome fontawesome.com \");\n  /* END.ATTRIBUTION */\n  return comment;\n}\nvar mutators = {\n  replace: function replace(mutation) {\n    var node = mutation[0];\n    if (node.parentNode) {\n      mutation[1].forEach(function (abstract) {\n        node.parentNode.insertBefore(convertSVG(abstract), node);\n      });\n      if (node.getAttribute(DATA_FA_I2SVG) === null && config.keepOriginalSource) {\n        var comment = DOCUMENT.createComment(nodeAsComment(node));\n        node.parentNode.replaceChild(comment, node);\n      } else {\n        node.remove();\n      }\n    }\n  },\n  nest: function nest(mutation) {\n    var node = mutation[0];\n    var abstract = mutation[1];\n\n    // If we already have a replaced node we do not want to continue nesting within it.\n    // Short-circuit to the standard replacement\n    if (~classArray(node).indexOf(config.replacementClass)) {\n      return mutators.replace(mutation);\n    }\n    var forSvg = new RegExp(\"\".concat(config.cssPrefix, \"-.*\"));\n    delete abstract[0].attributes.id;\n    if (abstract[0].attributes.class) {\n      var splitClasses = abstract[0].attributes.class.split(' ').reduce(function (acc, cls) {\n        if (cls === config.replacementClass || cls.match(forSvg)) {\n          acc.toSvg.push(cls);\n        } else {\n          acc.toNode.push(cls);\n        }\n        return acc;\n      }, {\n        toNode: [],\n        toSvg: []\n      });\n      abstract[0].attributes.class = splitClasses.toSvg.join(' ');\n      if (splitClasses.toNode.length === 0) {\n        node.removeAttribute('class');\n      } else {\n        node.setAttribute('class', splitClasses.toNode.join(' '));\n      }\n    }\n    var newInnerHTML = abstract.map(function (a) {\n      return toHtml(a);\n    }).join('\\n');\n    node.setAttribute(DATA_FA_I2SVG, '');\n    node.innerHTML = newInnerHTML;\n  }\n};\nfunction performOperationSync(op) {\n  op();\n}\nfunction perform(mutations, callback) {\n  var callbackFunction = typeof callback === 'function' ? callback : noop$2;\n  if (mutations.length === 0) {\n    callbackFunction();\n  } else {\n    var frame = performOperationSync;\n    if (config.mutateApproach === MUTATION_APPROACH_ASYNC) {\n      frame = WINDOW.requestAnimationFrame || performOperationSync;\n    }\n    frame(function () {\n      var mutator = getMutator();\n      var mark = perf.begin('mutate');\n      mutations.map(mutator);\n      mark();\n      callbackFunction();\n    });\n  }\n}\nvar disabled = false;\nfunction disableObservation() {\n  disabled = true;\n}\nfunction enableObservation() {\n  disabled = false;\n}\nvar mo = null;\nfunction observe(options) {\n  if (!MUTATION_OBSERVER) {\n    return;\n  }\n  if (!config.observeMutations) {\n    return;\n  }\n  var _options$treeCallback = options.treeCallback,\n    treeCallback = _options$treeCallback === void 0 ? noop$2 : _options$treeCallback,\n    _options$nodeCallback = options.nodeCallback,\n    nodeCallback = _options$nodeCallback === void 0 ? noop$2 : _options$nodeCallback,\n    _options$pseudoElemen = options.pseudoElementsCallback,\n    pseudoElementsCallback = _options$pseudoElemen === void 0 ? noop$2 : _options$pseudoElemen,\n    _options$observeMutat = options.observeMutationsRoot,\n    observeMutationsRoot = _options$observeMutat === void 0 ? DOCUMENT : _options$observeMutat;\n  mo = new MUTATION_OBSERVER(function (objects) {\n    if (disabled) return;\n    var defaultPrefix = getDefaultUsablePrefix();\n    toArray(objects).forEach(function (mutationRecord) {\n      if (mutationRecord.type === 'childList' && mutationRecord.addedNodes.length > 0 && !isWatched(mutationRecord.addedNodes[0])) {\n        if (config.searchPseudoElements) {\n          pseudoElementsCallback(mutationRecord.target);\n        }\n        treeCallback(mutationRecord.target);\n      }\n      if (mutationRecord.type === 'attributes' && mutationRecord.target.parentNode && config.searchPseudoElements) {\n        pseudoElementsCallback([mutationRecord.target], true);\n      }\n      if (mutationRecord.type === 'attributes' && isWatched(mutationRecord.target) && ~ATTRIBUTES_WATCHED_FOR_MUTATION.indexOf(mutationRecord.attributeName)) {\n        if (mutationRecord.attributeName === 'class' && hasPrefixAndIcon(mutationRecord.target)) {\n          var _getCanonicalIcon = getCanonicalIcon(classArray(mutationRecord.target)),\n            prefix = _getCanonicalIcon.prefix,\n            iconName = _getCanonicalIcon.iconName;\n          mutationRecord.target.setAttribute(DATA_PREFIX, prefix || defaultPrefix);\n          if (iconName) mutationRecord.target.setAttribute(DATA_ICON, iconName);\n        } else if (hasBeenReplaced(mutationRecord.target)) {\n          nodeCallback(mutationRecord.target);\n        }\n      }\n    });\n  });\n  if (!IS_DOM) return;\n  mo.observe(observeMutationsRoot, {\n    childList: true,\n    attributes: true,\n    characterData: true,\n    subtree: true\n  });\n}\nfunction disconnect() {\n  if (!mo) return;\n  mo.disconnect();\n}\n\nfunction styleParser (node) {\n  var style = node.getAttribute('style');\n  var val = [];\n  if (style) {\n    val = style.split(';').reduce(function (acc, style) {\n      var styles = style.split(':');\n      var prop = styles[0];\n      var value = styles.slice(1);\n      if (prop && value.length > 0) {\n        acc[prop] = value.join(':').trim();\n      }\n      return acc;\n    }, {});\n  }\n  return val;\n}\n\nfunction classParser (node) {\n  var existingPrefix = node.getAttribute('data-prefix');\n  var existingIconName = node.getAttribute('data-icon');\n  var innerText = node.innerText !== undefined ? node.innerText.trim() : '';\n  var val = getCanonicalIcon(classArray(node));\n  if (!val.prefix) {\n    val.prefix = getDefaultUsablePrefix();\n  }\n  if (existingPrefix && existingIconName) {\n    val.prefix = existingPrefix;\n    val.iconName = existingIconName;\n  }\n  if (val.iconName && val.prefix) {\n    return val;\n  }\n  if (val.prefix && innerText.length > 0) {\n    val.iconName = byLigature(val.prefix, node.innerText) || byUnicode(val.prefix, toHex(node.innerText));\n  }\n  if (!val.iconName && config.autoFetchSvg && node.firstChild && node.firstChild.nodeType === Node.TEXT_NODE) {\n    val.iconName = node.firstChild.data;\n  }\n  return val;\n}\n\nfunction attributesParser (node) {\n  var extraAttributes = toArray(node.attributes).reduce(function (acc, attr) {\n    if (acc.name !== 'class' && acc.name !== 'style') {\n      acc[attr.name] = attr.value;\n    }\n    return acc;\n  }, {});\n  return extraAttributes;\n}\n\nfunction blankMeta() {\n  return {\n    iconName: null,\n    prefix: null,\n    transform: meaninglessTransform,\n    symbol: false,\n    mask: {\n      iconName: null,\n      prefix: null,\n      rest: []\n    },\n    maskId: null,\n    extra: {\n      classes: [],\n      styles: {},\n      attributes: {}\n    }\n  };\n}\nfunction parseMeta(node) {\n  var parser = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    styleParser: true\n  };\n  var _classParser = classParser(node),\n    iconName = _classParser.iconName,\n    prefix = _classParser.prefix,\n    extraClasses = _classParser.rest;\n  var extraAttributes = attributesParser(node);\n  var pluginMeta = chainHooks('parseNodeAttributes', {}, node);\n  var extraStyles = parser.styleParser ? styleParser(node) : [];\n  return _objectSpread2({\n    iconName: iconName,\n    prefix: prefix,\n    transform: meaninglessTransform,\n    mask: {\n      iconName: null,\n      prefix: null,\n      rest: []\n    },\n    maskId: null,\n    symbol: false,\n    extra: {\n      classes: extraClasses,\n      styles: extraStyles,\n      attributes: extraAttributes\n    }\n  }, pluginMeta);\n}\n\nvar styles$2 = namespace.styles;\nfunction generateMutation(node) {\n  var nodeMeta = config.autoReplaceSvg === 'nest' ? parseMeta(node, {\n    styleParser: false\n  }) : parseMeta(node);\n  if (~nodeMeta.extra.classes.indexOf(LAYERS_TEXT_CLASSNAME)) {\n    return callProvided('generateLayersText', node, nodeMeta);\n  } else {\n    return callProvided('generateSvgReplacementMutation', node, nodeMeta);\n  }\n}\nfunction getKnownPrefixes() {\n  return [].concat(_toConsumableArray(zo), _toConsumableArray($o$1));\n}\nfunction onTree(root) {\n  var callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  if (!IS_DOM) return Promise.resolve();\n  var htmlClassList = DOCUMENT.documentElement.classList;\n  var hclAdd = function hclAdd(suffix) {\n    return htmlClassList.add(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n  };\n  var hclRemove = function hclRemove(suffix) {\n    return htmlClassList.remove(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n  };\n  var prefixes = config.autoFetchSvg ? getKnownPrefixes() : G.concat(Object.keys(styles$2));\n  if (!prefixes.includes('fa')) {\n    prefixes.push('fa');\n  }\n  var prefixesDomQuery = [\".\".concat(LAYERS_TEXT_CLASSNAME, \":not([\").concat(DATA_FA_I2SVG, \"])\")].concat(prefixes.map(function (p$$1) {\n    return \".\".concat(p$$1, \":not([\").concat(DATA_FA_I2SVG, \"])\");\n  })).join(', ');\n  if (prefixesDomQuery.length === 0) {\n    return Promise.resolve();\n  }\n  var candidates = [];\n  try {\n    candidates = toArray(root.querySelectorAll(prefixesDomQuery));\n  } catch (e$$1) {\n    // noop\n  }\n  if (candidates.length > 0) {\n    hclAdd('pending');\n    hclRemove('complete');\n  } else {\n    return Promise.resolve();\n  }\n  var mark = perf.begin('onTree');\n  var mutations = candidates.reduce(function (acc, node) {\n    try {\n      var mutation = generateMutation(node);\n      if (mutation) {\n        acc.push(mutation);\n      }\n    } catch (e$$1) {\n      if (!PRODUCTION) {\n        if (e$$1.name === 'MissingIcon') {\n          console.error(e$$1);\n        }\n      }\n    }\n    return acc;\n  }, []);\n  return new Promise(function (resolve, reject) {\n    Promise.all(mutations).then(function (resolvedMutations) {\n      perform(resolvedMutations, function () {\n        hclAdd('active');\n        hclAdd('complete');\n        hclRemove('pending');\n        if (typeof callback === 'function') callback();\n        mark();\n        resolve();\n      });\n    }).catch(function (e$$1) {\n      mark();\n      reject(e$$1);\n    });\n  });\n}\nfunction onNode(node) {\n  var callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  generateMutation(node).then(function (mutation) {\n    if (mutation) {\n      perform([mutation], callback);\n    }\n  });\n}\nfunction resolveIcons(next) {\n  return function (maybeIconDefinition) {\n    var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var iconDefinition = (maybeIconDefinition || {}).icon ? maybeIconDefinition : findIconDefinition(maybeIconDefinition || {});\n    var mask = params.mask;\n    if (mask) {\n      mask = (mask || {}).icon ? mask : findIconDefinition(mask || {});\n    }\n    return next(iconDefinition, _objectSpread2(_objectSpread2({}, params), {}, {\n      mask: mask\n    }));\n  };\n}\nvar render = function render(iconDefinition) {\n  var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _params$transform = params.transform,\n    transform = _params$transform === void 0 ? meaninglessTransform : _params$transform,\n    _params$symbol = params.symbol,\n    symbol = _params$symbol === void 0 ? false : _params$symbol,\n    _params$mask = params.mask,\n    mask = _params$mask === void 0 ? null : _params$mask,\n    _params$maskId = params.maskId,\n    maskId = _params$maskId === void 0 ? null : _params$maskId,\n    _params$classes = params.classes,\n    classes = _params$classes === void 0 ? [] : _params$classes,\n    _params$attributes = params.attributes,\n    attributes = _params$attributes === void 0 ? {} : _params$attributes,\n    _params$styles = params.styles,\n    styles = _params$styles === void 0 ? {} : _params$styles;\n  if (!iconDefinition) return;\n  var prefix = iconDefinition.prefix,\n    iconName = iconDefinition.iconName,\n    icon = iconDefinition.icon;\n  return domVariants(_objectSpread2({\n    type: 'icon'\n  }, iconDefinition), function () {\n    callHooks('beforeDOMElementCreation', {\n      iconDefinition: iconDefinition,\n      params: params\n    });\n    return makeInlineSvgAbstract({\n      icons: {\n        main: asFoundIcon(icon),\n        mask: mask ? asFoundIcon(mask.icon) : {\n          found: false,\n          width: null,\n          height: null,\n          icon: {}\n        }\n      },\n      prefix: prefix,\n      iconName: iconName,\n      transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\n      symbol: symbol,\n      maskId: maskId,\n      extra: {\n        attributes: attributes,\n        styles: styles,\n        classes: classes\n      }\n    });\n  });\n};\nvar ReplaceElements = {\n  mixout: function mixout() {\n    return {\n      icon: resolveIcons(render)\n    };\n  },\n  hooks: function hooks() {\n    return {\n      mutationObserverCallbacks: function mutationObserverCallbacks(accumulator) {\n        accumulator.treeCallback = onTree;\n        accumulator.nodeCallback = onNode;\n        return accumulator;\n      }\n    };\n  },\n  provides: function provides(providers$$1) {\n    providers$$1.i2svg = function (params) {\n      var _params$node = params.node,\n        node = _params$node === void 0 ? DOCUMENT : _params$node,\n        _params$callback = params.callback,\n        callback = _params$callback === void 0 ? function () {} : _params$callback;\n      return onTree(node, callback);\n    };\n    providers$$1.generateSvgReplacementMutation = function (node, nodeMeta) {\n      var iconName = nodeMeta.iconName,\n        prefix = nodeMeta.prefix,\n        transform = nodeMeta.transform,\n        symbol = nodeMeta.symbol,\n        mask = nodeMeta.mask,\n        maskId = nodeMeta.maskId,\n        extra = nodeMeta.extra;\n      return new Promise(function (resolve, reject) {\n        Promise.all([findIcon(iconName, prefix), mask.iconName ? findIcon(mask.iconName, mask.prefix) : Promise.resolve({\n          found: false,\n          width: 512,\n          height: 512,\n          icon: {}\n        })]).then(function (_ref) {\n          var _ref2 = _slicedToArray(_ref, 2),\n            main = _ref2[0],\n            mask = _ref2[1];\n          resolve([node, makeInlineSvgAbstract({\n            icons: {\n              main: main,\n              mask: mask\n            },\n            prefix: prefix,\n            iconName: iconName,\n            transform: transform,\n            symbol: symbol,\n            maskId: maskId,\n            extra: extra,\n            watchable: true\n          })]);\n        }).catch(reject);\n      });\n    };\n    providers$$1.generateAbstractIcon = function (_ref3) {\n      var children = _ref3.children,\n        attributes = _ref3.attributes,\n        main = _ref3.main,\n        transform = _ref3.transform,\n        styles = _ref3.styles;\n      var styleString = joinStyles(styles);\n      if (styleString.length > 0) {\n        attributes['style'] = styleString;\n      }\n      var nextChild;\n      if (transformIsMeaningful(transform)) {\n        nextChild = callProvided('generateAbstractTransformGrouping', {\n          main: main,\n          transform: transform,\n          containerWidth: main.width,\n          iconWidth: main.width\n        });\n      }\n      children.push(nextChild || main.icon);\n      return {\n        children: children,\n        attributes: attributes\n      };\n    };\n  }\n};\n\nvar Layers = {\n  mixout: function mixout() {\n    return {\n      layer: function layer(assembler) {\n        var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        var _params$classes = params.classes,\n          classes = _params$classes === void 0 ? [] : _params$classes;\n        return domVariants({\n          type: 'layer'\n        }, function () {\n          callHooks('beforeDOMElementCreation', {\n            assembler: assembler,\n            params: params\n          });\n          var children = [];\n          assembler(function (args) {\n            Array.isArray(args) ? args.map(function (a) {\n              children = children.concat(a.abstract);\n            }) : children = children.concat(args.abstract);\n          });\n          return [{\n            tag: 'span',\n            attributes: {\n              class: [\"\".concat(config.cssPrefix, \"-layers\")].concat(_toConsumableArray(classes)).join(' ')\n            },\n            children: children\n          }];\n        });\n      }\n    };\n  }\n};\n\nvar LayersCounter = {\n  mixout: function mixout() {\n    return {\n      counter: function counter(content) {\n        var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        var _params$title = params.title,\n          title = _params$title === void 0 ? null : _params$title,\n          _params$classes = params.classes,\n          classes = _params$classes === void 0 ? [] : _params$classes,\n          _params$attributes = params.attributes,\n          attributes = _params$attributes === void 0 ? {} : _params$attributes,\n          _params$styles = params.styles,\n          styles = _params$styles === void 0 ? {} : _params$styles;\n        return domVariants({\n          type: 'counter',\n          content: content\n        }, function () {\n          callHooks('beforeDOMElementCreation', {\n            content: content,\n            params: params\n          });\n          return makeLayersCounterAbstract({\n            content: content.toString(),\n            title: title,\n            extra: {\n              attributes: attributes,\n              styles: styles,\n              classes: [\"\".concat(config.cssPrefix, \"-layers-counter\")].concat(_toConsumableArray(classes))\n            }\n          });\n        });\n      }\n    };\n  }\n};\n\nvar LayersText = {\n  mixout: function mixout() {\n    return {\n      text: function text(content) {\n        var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        var _params$transform = params.transform,\n          transform = _params$transform === void 0 ? meaninglessTransform : _params$transform,\n          _params$classes = params.classes,\n          classes = _params$classes === void 0 ? [] : _params$classes,\n          _params$attributes = params.attributes,\n          attributes = _params$attributes === void 0 ? {} : _params$attributes,\n          _params$styles = params.styles,\n          styles = _params$styles === void 0 ? {} : _params$styles;\n        return domVariants({\n          type: 'text',\n          content: content\n        }, function () {\n          callHooks('beforeDOMElementCreation', {\n            content: content,\n            params: params\n          });\n          return makeLayersTextAbstract({\n            content: content,\n            transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\n            extra: {\n              attributes: attributes,\n              styles: styles,\n              classes: [\"\".concat(config.cssPrefix, \"-layers-text\")].concat(_toConsumableArray(classes))\n            }\n          });\n        });\n      }\n    };\n  },\n  provides: function provides(providers$$1) {\n    providers$$1.generateLayersText = function (node, nodeMeta) {\n      var transform = nodeMeta.transform,\n        extra = nodeMeta.extra;\n      var width = null;\n      var height = null;\n      if (IS_IE) {\n        var computedFontSize = parseInt(getComputedStyle(node).fontSize, 10);\n        var boundingClientRect = node.getBoundingClientRect();\n        width = boundingClientRect.width / computedFontSize;\n        height = boundingClientRect.height / computedFontSize;\n      }\n      return Promise.resolve([node, makeLayersTextAbstract({\n        content: node.innerHTML,\n        width: width,\n        height: height,\n        transform: transform,\n        extra: extra,\n        watchable: true\n      })]);\n    };\n  }\n};\n\nvar CLEAN_CONTENT_PATTERN = new RegExp(\"\\\"\", 'ug');\nvar SECONDARY_UNICODE_RANGE = [1105920, 1112319];\nvar _FONT_FAMILY_WEIGHT_TO_PREFIX = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\n  FontAwesome: {\n    normal: 'fas',\n    400: 'fas'\n  }\n}), Bo), oa), lt);\nvar FONT_FAMILY_WEIGHT_TO_PREFIX = Object.keys(_FONT_FAMILY_WEIGHT_TO_PREFIX).reduce(function (acc, key) {\n  acc[key.toLowerCase()] = _FONT_FAMILY_WEIGHT_TO_PREFIX[key];\n  return acc;\n}, {});\nvar FONT_FAMILY_WEIGHT_FALLBACK = Object.keys(FONT_FAMILY_WEIGHT_TO_PREFIX).reduce(function (acc, fontFamily) {\n  var weights = FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamily];\n  acc[fontFamily] = weights[900] || _toConsumableArray(Object.entries(weights))[0][1];\n  return acc;\n}, {});\n\n// Return hex value of *first* character in `content`\nfunction hexValueFromContent(content) {\n  var cleaned = content.replace(CLEAN_CONTENT_PATTERN, '');\n  return toHex(_toConsumableArray(cleaned)[0] || '');\n}\n\n// Check if it's a secondary Duotone layer, by checking if:\n// - Unicode value in `content` is repeated\n// - Unicode value in `content` is above 0x10000\n// - The \"ss01\" font feature is enabled on the `content`\nfunction isSecondaryLayer(styles) {\n  var hasStylisticSet = styles.getPropertyValue('font-feature-settings').includes('ss01');\n  var content = styles.getPropertyValue('content');\n  var cleaned = content.replace(CLEAN_CONTENT_PATTERN, '');\n  var codePoint = cleaned.codePointAt(0);\n  var isPrependTen = codePoint >= SECONDARY_UNICODE_RANGE[0] && codePoint <= SECONDARY_UNICODE_RANGE[1];\n  var isDoubled = cleaned.length === 2 ? cleaned[0] === cleaned[1] : false;\n  return isPrependTen || isDoubled || hasStylisticSet;\n}\nfunction getPrefix(fontFamily, fontWeight) {\n  var fontFamilySanitized = fontFamily.replace(/^['\"]|['\"]$/g, '').toLowerCase();\n  var fontWeightInteger = parseInt(fontWeight);\n  var fontWeightSanitized = isNaN(fontWeightInteger) ? 'normal' : fontWeightInteger;\n  return (FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamilySanitized] || {})[fontWeightSanitized] || FONT_FAMILY_WEIGHT_FALLBACK[fontFamilySanitized];\n}\nfunction replaceForPosition(node, position) {\n  var pendingAttribute = \"\".concat(DATA_FA_PSEUDO_ELEMENT_PENDING).concat(position.replace(':', '-'));\n  return new Promise(function (resolve, reject) {\n    if (node.getAttribute(pendingAttribute) !== null) {\n      // This node is already being processed\n      return resolve();\n    }\n    var children = toArray(node.children);\n    var alreadyProcessedPseudoElement = children.filter(function (c$$1) {\n      return c$$1.getAttribute(DATA_FA_PSEUDO_ELEMENT) === position;\n    })[0];\n    var styles = WINDOW.getComputedStyle(node, position);\n    var fontFamily = styles.getPropertyValue('font-family');\n    var fontFamilyMatch = fontFamily.match(FONT_FAMILY_PATTERN);\n    var fontWeight = styles.getPropertyValue('font-weight');\n    var content = styles.getPropertyValue('content');\n    if (alreadyProcessedPseudoElement && !fontFamilyMatch) {\n      // If we've already processed it but the current computed style does not result in a font-family,\n      // that probably means that a class name that was previously present to make the icon has been\n      // removed. So we now should delete the icon.\n      node.removeChild(alreadyProcessedPseudoElement);\n      return resolve();\n    } else if (fontFamilyMatch && content !== 'none' && content !== '') {\n      var _content = styles.getPropertyValue('content');\n      var prefix = getPrefix(fontFamily, fontWeight);\n      var hexValue = hexValueFromContent(_content);\n      var isV4 = fontFamilyMatch[0].startsWith('FontAwesome');\n      var isSecondary = isSecondaryLayer(styles);\n      var iconName = byUnicode(prefix, hexValue);\n      var iconIdentifier = iconName;\n      if (isV4) {\n        var iconName4 = byOldUnicode(hexValue);\n        if (iconName4.iconName && iconName4.prefix) {\n          iconName = iconName4.iconName;\n          prefix = iconName4.prefix;\n        }\n      }\n\n      // Only convert the pseudo element in this ::before/::after position into an icon if we haven't\n      // already done so with the same prefix and iconName\n      if (iconName && !isSecondary && (!alreadyProcessedPseudoElement || alreadyProcessedPseudoElement.getAttribute(DATA_PREFIX) !== prefix || alreadyProcessedPseudoElement.getAttribute(DATA_ICON) !== iconIdentifier)) {\n        node.setAttribute(pendingAttribute, iconIdentifier);\n        if (alreadyProcessedPseudoElement) {\n          // Delete the old one, since we're replacing it with a new one\n          node.removeChild(alreadyProcessedPseudoElement);\n        }\n        var meta = blankMeta();\n        var extra = meta.extra;\n        extra.attributes[DATA_FA_PSEUDO_ELEMENT] = position;\n        findIcon(iconName, prefix).then(function (main) {\n          var abstract = makeInlineSvgAbstract(_objectSpread2(_objectSpread2({}, meta), {}, {\n            icons: {\n              main: main,\n              mask: emptyCanonicalIcon()\n            },\n            prefix: prefix,\n            iconName: iconIdentifier,\n            extra: extra,\n            watchable: true\n          }));\n          var element = DOCUMENT.createElementNS('http://www.w3.org/2000/svg', 'svg');\n          if (position === '::before') {\n            node.insertBefore(element, node.firstChild);\n          } else {\n            node.appendChild(element);\n          }\n          element.outerHTML = abstract.map(function (a$$1) {\n            return toHtml(a$$1);\n          }).join('\\n');\n          node.removeAttribute(pendingAttribute);\n          resolve();\n        }).catch(reject);\n      } else {\n        resolve();\n      }\n    } else {\n      resolve();\n    }\n  });\n}\nfunction replace(node) {\n  return Promise.all([replaceForPosition(node, '::before'), replaceForPosition(node, '::after')]);\n}\nfunction processable(node) {\n  return node.parentNode !== document.head && !~TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS.indexOf(node.tagName.toUpperCase()) && !node.getAttribute(DATA_FA_PSEUDO_ELEMENT) && (!node.parentNode || node.parentNode.tagName !== 'svg');\n}\nvar hasPseudoElement = function hasPseudoElement(selector) {\n  return !!selector && PSEUDO_ELEMENTS.some(function (pseudoSelector) {\n    return selector.includes(pseudoSelector);\n  });\n};\n\n// Return selectors from all available stylesheets that have\n// pseudo-elements defined.\nvar parseCSSRuleForPseudos = function parseCSSRuleForPseudos(selectorText) {\n  if (!selectorText) return [];\n  var selectorSet = new Set();\n  var selectors = [selectorText];\n\n  // First, split on pseudo-classes like :is, :where, etc. as we\n  // don't want to split those by comma\n  var splitters = [/(?=\\s:)/, /(?<=\\)\\)?[^,]*,)/];\n  var _loop = function _loop() {\n    var splitter = _splitters[_i];\n    selectors = selectors.flatMap(function (selector) {\n      return selector.split(splitter).map(function (s$$1) {\n        return s$$1.replace(/,\\s*$/, '').trim();\n      });\n    });\n  };\n  for (var _i = 0, _splitters = splitters; _i < _splitters.length; _i++) {\n    _loop();\n  }\n  selectors = selectors.flatMap(function (selector) {\n    return selector.includes('(') ? selector : selector.split(',').map(function (s$$1) {\n      return s$$1.trim();\n    });\n  });\n  var _iterator = _createForOfIteratorHelper(selectors),\n    _step;\n  try {\n    for (_iterator.s(); !(_step = _iterator.n()).done;) {\n      var selector = _step.value;\n      if (hasPseudoElement(selector)) {\n        // Remove pseudo-elements from the selector\n        var selectorWithoutPseudo = PSEUDO_ELEMENTS.reduce(function (acc, pseudoSelector) {\n          return acc.replace(pseudoSelector, '');\n        }, selector);\n        if (selectorWithoutPseudo !== '' && selectorWithoutPseudo !== '*') {\n          selectorSet.add(selectorWithoutPseudo);\n        }\n      }\n    }\n  } catch (err) {\n    _iterator.e(err);\n  } finally {\n    _iterator.f();\n  }\n  return selectorSet;\n};\nfunction searchPseudoElements(root) {\n  var useAsNodeList = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  if (!IS_DOM) return;\n  var nodeList;\n  if (useAsNodeList) {\n    nodeList = root;\n  } else if (config.searchPseudoElementsFullScan) {\n    nodeList = root.querySelectorAll('*');\n  } else {\n    // Get elements that have pseudo elements defined in the CSS\n    var selectorSet = new Set();\n    var _iterator2 = _createForOfIteratorHelper(document.styleSheets),\n      _step2;\n    try {\n      for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n        var stylesheet = _step2.value;\n        try {\n          var _iterator3 = _createForOfIteratorHelper(stylesheet.cssRules),\n            _step3;\n          try {\n            for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n              var rule = _step3.value;\n              var parsedSelectors = parseCSSRuleForPseudos(rule.selectorText);\n              var _iterator4 = _createForOfIteratorHelper(parsedSelectors),\n                _step4;\n              try {\n                for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {\n                  var selector = _step4.value;\n                  selectorSet.add(selector);\n                }\n              } catch (err) {\n                _iterator4.e(err);\n              } finally {\n                _iterator4.f();\n              }\n            }\n          } catch (err) {\n            _iterator3.e(err);\n          } finally {\n            _iterator3.f();\n          }\n        } catch (e$$1) {\n          if (config.searchPseudoElementsWarnings) {\n            console.warn(\"Font Awesome: cannot parse stylesheet: \".concat(stylesheet.href, \" (\").concat(e$$1.message, \")\\nIf it declares any Font Awesome CSS pseudo-elements, they will not be rendered as SVG icons. Add crossorigin=\\\"anonymous\\\" to the <link>, enable searchPseudoElementsFullScan for slower but more thorough DOM parsing, or suppress this warning by setting searchPseudoElementsWarnings to false.\"));\n          }\n        }\n      }\n    } catch (err) {\n      _iterator2.e(err);\n    } finally {\n      _iterator2.f();\n    }\n    if (!selectorSet.size) return;\n    var cleanSelectors = Array.from(selectorSet).join(', ');\n    try {\n      nodeList = root.querySelectorAll(cleanSelectors);\n    } catch (_unused) {} // eslint-disable-line no-empty\n  }\n  return new Promise(function (resolve, reject) {\n    var operations = toArray(nodeList).filter(processable).map(replace);\n    var end = perf.begin('searchPseudoElements');\n    disableObservation();\n    Promise.all(operations).then(function () {\n      end();\n      enableObservation();\n      resolve();\n    }).catch(function () {\n      end();\n      enableObservation();\n      reject();\n    });\n  });\n}\nvar PseudoElements = {\n  hooks: function hooks() {\n    return {\n      mutationObserverCallbacks: function mutationObserverCallbacks(accumulator) {\n        accumulator.pseudoElementsCallback = searchPseudoElements;\n        return accumulator;\n      }\n    };\n  },\n  provides: function provides(providers) {\n    providers.pseudoElements2svg = function (params) {\n      var _params$node = params.node,\n        node = _params$node === void 0 ? DOCUMENT : _params$node;\n      if (config.searchPseudoElements) {\n        searchPseudoElements(node);\n      }\n    };\n  }\n};\n\nvar _unwatched = false;\nvar MutationObserver$1 = {\n  mixout: function mixout() {\n    return {\n      dom: {\n        unwatch: function unwatch() {\n          disableObservation();\n          _unwatched = true;\n        }\n      }\n    };\n  },\n  hooks: function hooks() {\n    return {\n      bootstrap: function bootstrap() {\n        observe(chainHooks('mutationObserverCallbacks', {}));\n      },\n      noAuto: function noAuto() {\n        disconnect();\n      },\n      watch: function watch(params) {\n        var observeMutationsRoot = params.observeMutationsRoot;\n        if (_unwatched) {\n          enableObservation();\n        } else {\n          observe(chainHooks('mutationObserverCallbacks', {\n            observeMutationsRoot: observeMutationsRoot\n          }));\n        }\n      }\n    };\n  }\n};\n\nvar parseTransformString = function parseTransformString(transformString) {\n  var transform = {\n    size: 16,\n    x: 0,\n    y: 0,\n    flipX: false,\n    flipY: false,\n    rotate: 0\n  };\n  return transformString.toLowerCase().split(' ').reduce(function (acc, n) {\n    var parts = n.toLowerCase().split('-');\n    var first = parts[0];\n    var rest = parts.slice(1).join('-');\n    if (first && rest === 'h') {\n      acc.flipX = true;\n      return acc;\n    }\n    if (first && rest === 'v') {\n      acc.flipY = true;\n      return acc;\n    }\n    rest = parseFloat(rest);\n    if (isNaN(rest)) {\n      return acc;\n    }\n    switch (first) {\n      case 'grow':\n        acc.size = acc.size + rest;\n        break;\n      case 'shrink':\n        acc.size = acc.size - rest;\n        break;\n      case 'left':\n        acc.x = acc.x - rest;\n        break;\n      case 'right':\n        acc.x = acc.x + rest;\n        break;\n      case 'up':\n        acc.y = acc.y - rest;\n        break;\n      case 'down':\n        acc.y = acc.y + rest;\n        break;\n      case 'rotate':\n        acc.rotate = acc.rotate + rest;\n        break;\n    }\n    return acc;\n  }, transform);\n};\nvar PowerTransforms = {\n  mixout: function mixout() {\n    return {\n      parse: {\n        transform: function transform(transformString) {\n          return parseTransformString(transformString);\n        }\n      }\n    };\n  },\n  hooks: function hooks() {\n    return {\n      parseNodeAttributes: function parseNodeAttributes(accumulator, node) {\n        var transformString = node.getAttribute('data-fa-transform');\n        if (transformString) {\n          accumulator.transform = parseTransformString(transformString);\n        }\n        return accumulator;\n      }\n    };\n  },\n  provides: function provides(providers) {\n    providers.generateAbstractTransformGrouping = function (_ref) {\n      var main = _ref.main,\n        transform = _ref.transform,\n        containerWidth = _ref.containerWidth,\n        iconWidth = _ref.iconWidth;\n      var outer = {\n        transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n      };\n      var innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n      var innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n      var innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n      var inner = {\n        transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n      };\n      var path = {\n        transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n      };\n      var operations = {\n        outer: outer,\n        inner: inner,\n        path: path\n      };\n      return {\n        tag: 'g',\n        attributes: _objectSpread2({}, operations.outer),\n        children: [{\n          tag: 'g',\n          attributes: _objectSpread2({}, operations.inner),\n          children: [{\n            tag: main.icon.tag,\n            children: main.icon.children,\n            attributes: _objectSpread2(_objectSpread2({}, main.icon.attributes), operations.path)\n          }]\n        }]\n      };\n    };\n  }\n};\n\nvar ALL_SPACE = {\n  x: 0,\n  y: 0,\n  width: '100%',\n  height: '100%'\n};\nfunction fillBlack(abstract) {\n  var force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  if (abstract.attributes && (abstract.attributes.fill || force)) {\n    abstract.attributes.fill = 'black';\n  }\n  return abstract;\n}\nfunction deGroup(abstract) {\n  if (abstract.tag === 'g') {\n    return abstract.children;\n  } else {\n    return [abstract];\n  }\n}\nvar Masks = {\n  hooks: function hooks() {\n    return {\n      parseNodeAttributes: function parseNodeAttributes(accumulator, node) {\n        var maskData = node.getAttribute('data-fa-mask');\n        var mask = !maskData ? emptyCanonicalIcon() : getCanonicalIcon(maskData.split(' ').map(function (i) {\n          return i.trim();\n        }));\n        if (!mask.prefix) {\n          mask.prefix = getDefaultUsablePrefix();\n        }\n        accumulator.mask = mask;\n        accumulator.maskId = node.getAttribute('data-fa-mask-id');\n        return accumulator;\n      }\n    };\n  },\n  provides: function provides(providers) {\n    providers.generateAbstractMask = function (_ref) {\n      var children = _ref.children,\n        attributes = _ref.attributes,\n        main = _ref.main,\n        mask = _ref.mask,\n        explicitMaskId = _ref.maskId,\n        transform = _ref.transform;\n      var mainWidth = main.width,\n        mainPath = main.icon;\n      var maskWidth = mask.width,\n        maskPath = mask.icon;\n      var trans = transformForSvg({\n        transform: transform,\n        containerWidth: maskWidth,\n        iconWidth: mainWidth\n      });\n      var maskRect = {\n        tag: 'rect',\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\n          fill: 'white'\n        })\n      };\n      var maskInnerGroupChildrenMixin = mainPath.children ? {\n        children: mainPath.children.map(fillBlack)\n      } : {};\n      var maskInnerGroup = {\n        tag: 'g',\n        attributes: _objectSpread2({}, trans.inner),\n        children: [fillBlack(_objectSpread2({\n          tag: mainPath.tag,\n          attributes: _objectSpread2(_objectSpread2({}, mainPath.attributes), trans.path)\n        }, maskInnerGroupChildrenMixin))]\n      };\n      var maskOuterGroup = {\n        tag: 'g',\n        attributes: _objectSpread2({}, trans.outer),\n        children: [maskInnerGroup]\n      };\n      var maskId = \"mask-\".concat(explicitMaskId || nextUniqueId());\n      var clipId = \"clip-\".concat(explicitMaskId || nextUniqueId());\n      var maskTag = {\n        tag: 'mask',\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\n          id: maskId,\n          maskUnits: 'userSpaceOnUse',\n          maskContentUnits: 'userSpaceOnUse'\n        }),\n        children: [maskRect, maskOuterGroup]\n      };\n      var defs = {\n        tag: 'defs',\n        children: [{\n          tag: 'clipPath',\n          attributes: {\n            id: clipId\n          },\n          children: deGroup(maskPath)\n        }, maskTag]\n      };\n      children.push(defs, {\n        tag: 'rect',\n        attributes: _objectSpread2({\n          'fill': 'currentColor',\n          'clip-path': \"url(#\".concat(clipId, \")\"),\n          'mask': \"url(#\".concat(maskId, \")\")\n        }, ALL_SPACE)\n      });\n      return {\n        children: children,\n        attributes: attributes\n      };\n    };\n  }\n};\n\nvar MissingIconIndicator = {\n  provides: function provides(providers) {\n    var reduceMotion = false;\n    if (WINDOW.matchMedia) {\n      reduceMotion = WINDOW.matchMedia('(prefers-reduced-motion: reduce)').matches;\n    }\n    providers.missingIconAbstract = function () {\n      var gChildren = [];\n      var FILL = {\n        fill: 'currentColor'\n      };\n      var ANIMATION_BASE = {\n        attributeType: 'XML',\n        repeatCount: 'indefinite',\n        dur: '2s'\n      };\n\n      // Ring\n      gChildren.push({\n        tag: 'path',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          d: 'M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z'\n        })\n      });\n      var OPACITY_ANIMATE = _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\n        attributeName: 'opacity'\n      });\n      var dot = {\n        tag: 'circle',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          cx: '256',\n          cy: '364',\n          r: '28'\n        }),\n        children: []\n      };\n      if (!reduceMotion) {\n        dot.children.push({\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\n            attributeName: 'r',\n            values: '28;14;28;28;14;28;'\n          })\n        }, {\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n            values: '1;0;1;1;0;1;'\n          })\n        });\n      }\n      gChildren.push(dot);\n      gChildren.push({\n        tag: 'path',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          opacity: '1',\n          d: 'M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z'\n        }),\n        children: reduceMotion ? [] : [{\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n            values: '1;0;0;0;0;1;'\n          })\n        }]\n      });\n      if (!reduceMotion) {\n        // Exclamation\n        gChildren.push({\n          tag: 'path',\n          attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n            opacity: '0',\n            d: 'M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z'\n          }),\n          children: [{\n            tag: 'animate',\n            attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n              values: '0;0;1;1;0;0;'\n            })\n          }]\n        });\n      }\n      return {\n        tag: 'g',\n        attributes: {\n          class: 'missing'\n        },\n        children: gChildren\n      };\n    };\n  }\n};\n\nvar SvgSymbols = {\n  hooks: function hooks() {\n    return {\n      parseNodeAttributes: function parseNodeAttributes(accumulator, node) {\n        var symbolData = node.getAttribute('data-fa-symbol');\n        var symbol = symbolData === null ? false : symbolData === '' ? true : symbolData;\n        accumulator['symbol'] = symbol;\n        return accumulator;\n      }\n    };\n  }\n};\n\nvar plugins = [InjectCSS, ReplaceElements, Layers, LayersCounter, LayersText, PseudoElements, MutationObserver$1, PowerTransforms, Masks, MissingIconIndicator, SvgSymbols];\n\nregisterPlugins(plugins, {\n  mixoutsTo: api\n});\nvar noAuto$1 = api.noAuto;\nvar config$1 = api.config;\nvar library$1 = api.library;\nvar dom$1 = api.dom;\nvar parse$1 = api.parse;\nvar findIconDefinition$1 = api.findIconDefinition;\nvar toHtml$1 = api.toHtml;\nvar icon = api.icon;\nvar layer = api.layer;\nvar text = api.text;\nvar counter = api.counter;\n\nexport { noAuto$1 as noAuto, config$1 as config, library$1 as library, dom$1 as dom, parse$1 as parse, findIconDefinition$1 as findIconDefinition, toHtml$1 as toHtml, icon, layer, text, counter, api };\n"], "mappings": ";AAKA,SAAS,kBAAkBA,IAAGC,IAAG;AAC/B,GAAC,QAAQA,MAAKA,KAAID,GAAE,YAAYC,KAAID,GAAE;AACtC,WAASE,KAAI,GAAGC,KAAI,MAAMF,EAAC,GAAGC,KAAID,IAAGC,KAAK,CAAAC,GAAED,EAAC,IAAIF,GAAEE,EAAC;AACpD,SAAOC;AACT;AACA,SAAS,gBAAgBH,IAAG;AAC1B,MAAI,MAAM,QAAQA,EAAC,EAAG,QAAOA;AAC/B;AACA,SAAS,mBAAmBA,IAAG;AAC7B,MAAI,MAAM,QAAQA,EAAC,EAAG,QAAO,kBAAkBA,EAAC;AAClD;AACA,SAAS,gBAAgBC,IAAGE,IAAG;AAC7B,MAAI,EAAEF,cAAaE,IAAI,OAAM,IAAI,UAAU,mCAAmC;AAChF;AACA,SAAS,kBAAkBD,IAAGF,IAAG;AAC/B,WAASI,KAAI,GAAGA,KAAIJ,GAAE,QAAQI,MAAK;AACjC,QAAIC,KAAIL,GAAEI,EAAC;AACX,IAAAC,GAAE,aAAaA,GAAE,cAAc,OAAIA,GAAE,eAAe,MAAI,WAAWA,OAAMA,GAAE,WAAW,OAAK,OAAO,eAAeH,IAAG,eAAeG,GAAE,GAAG,GAAGA,EAAC;AAAA,EAC9I;AACF;AACA,SAAS,aAAaH,IAAGF,IAAGI,IAAG;AAC7B,SAAOJ,MAAK,kBAAkBE,GAAE,WAAWF,EAAC,GAAGI,MAAK,kBAAkBF,IAAGE,EAAC,GAAG,OAAO,eAAeF,IAAG,aAAa;AAAA,IACjH,UAAU;AAAA,EACZ,CAAC,GAAGA;AACN;AACA,SAAS,2BAA2BF,IAAGE,IAAG;AACxC,MAAIE,KAAI,eAAe,OAAO,UAAUJ,GAAE,OAAO,QAAQ,KAAKA,GAAE,YAAY;AAC5E,MAAI,CAACI,IAAG;AACN,QAAI,MAAM,QAAQJ,EAAC,MAAMI,KAAI,4BAA4BJ,EAAC,MAAME,MAAKF,MAAK,YAAY,OAAOA,GAAE,QAAQ;AACrG,MAAAI,OAAMJ,KAAII;AACV,UAAID,KAAI,GACNG,KAAI,WAAY;AAAA,MAAC;AACnB,aAAO;AAAA,QACL,GAAGA;AAAA,QACH,GAAG,WAAY;AACb,iBAAOH,MAAKH,GAAE,SAAS;AAAA,YACrB,MAAM;AAAA,UACR,IAAI;AAAA,YACF,MAAM;AAAA,YACN,OAAOA,GAAEG,IAAG;AAAA,UACd;AAAA,QACF;AAAA,QACA,GAAG,SAAUH,IAAG;AACd,gBAAMA;AAAA,QACR;AAAA,QACA,GAAGM;AAAA,MACL;AAAA,IACF;AACA,UAAM,IAAI,UAAU,uIAAuI;AAAA,EAC7J;AACA,MAAID,IACFJ,KAAI,MACJM,KAAI;AACN,SAAO;AAAA,IACL,GAAG,WAAY;AACb,MAAAH,KAAIA,GAAE,KAAKJ,EAAC;AAAA,IACd;AAAA,IACA,GAAG,WAAY;AACb,UAAIA,KAAII,GAAE,KAAK;AACf,aAAOH,KAAID,GAAE,MAAMA;AAAA,IACrB;AAAA,IACA,GAAG,SAAUA,IAAG;AACd,MAAAO,KAAI,MAAIF,KAAIL;AAAA,IACd;AAAA,IACA,GAAG,WAAY;AACb,UAAI;AACF,QAAAC,MAAK,QAAQG,GAAE,UAAUA,GAAE,OAAO;AAAA,MACpC,UAAE;AACA,YAAIG,GAAG,OAAMF;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,gBAAgBH,IAAGF,IAAGI,IAAG;AAChC,UAAQJ,KAAI,eAAeA,EAAC,MAAME,KAAI,OAAO,eAAeA,IAAGF,IAAG;AAAA,IAChE,OAAOI;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,CAAC,IAAIF,GAAEF,EAAC,IAAII,IAAGF;AACjB;AACA,SAAS,UAAUE,IAAGF,IAAG;AACvB,MAAI,cAAc,OAAOA,MAAK,SAASA,GAAG,OAAM,IAAI,UAAU,oDAAoD;AAClH,EAAAE,GAAE,YAAY,OAAO,OAAOF,MAAKA,GAAE,WAAW;AAAA,IAC5C,aAAa;AAAA,MACX,OAAOE;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC,GAAG,OAAO,eAAeA,IAAG,aAAa;AAAA,IACxC,UAAU;AAAA,EACZ,CAAC,GAAGF,MAAK,gBAAgBE,IAAGF,EAAC;AAC/B;AACA,SAAS,iBAAiBF,IAAG;AAC3B,MAAI,eAAe,OAAO,UAAU,QAAQA,GAAE,OAAO,QAAQ,KAAK,QAAQA,GAAE,YAAY,EAAG,QAAO,MAAM,KAAKA,EAAC;AAChH;AACA,SAAS,sBAAsBA,IAAGQ,IAAG;AACnC,MAAIJ,KAAI,QAAQJ,KAAI,OAAO,eAAe,OAAO,UAAUA,GAAE,OAAO,QAAQ,KAAKA,GAAE,YAAY;AAC/F,MAAI,QAAQI,IAAG;AACb,QAAIF,IACFC,IACAM,IACAF,IACAN,KAAI,CAAC,GACLS,KAAI,MACJL,KAAI;AACN,QAAI;AACF,UAAII,MAAKL,KAAIA,GAAE,KAAKJ,EAAC,GAAG,MAAM,MAAMQ,IAAG;AACrC,YAAI,OAAOJ,EAAC,MAAMA,GAAG;AACrB,QAAAM,KAAI;AAAA,MACN,MAAO,QAAO,EAAEA,MAAKR,KAAIO,GAAE,KAAKL,EAAC,GAAG,UAAUH,GAAE,KAAKC,GAAE,KAAK,GAAGD,GAAE,WAAWO,KAAIE,KAAI,KAAG;AAAA,IACzF,SAASV,IAAG;AACV,MAAAK,KAAI,MAAIF,KAAIH;AAAA,IACd,UAAE;AACA,UAAI;AACF,YAAI,CAACU,MAAK,QAAQN,GAAE,WAAWG,KAAIH,GAAE,OAAO,GAAG,OAAOG,EAAC,MAAMA,IAAI;AAAA,MACnE,UAAE;AACA,YAAIF,GAAG,OAAMF;AAAA,MACf;AAAA,IACF;AACA,WAAOF;AAAA,EACT;AACF;AACA,SAAS,mBAAmB;AAC1B,QAAM,IAAI,UAAU,2IAA2I;AACjK;AACA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;AACA,SAAS,QAAQC,IAAGF,IAAG;AACrB,MAAII,KAAI,OAAO,KAAKF,EAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAIG,KAAI,OAAO,sBAAsBH,EAAC;AACtC,IAAAF,OAAMK,KAAIA,GAAE,OAAO,SAAUL,IAAG;AAC9B,aAAO,OAAO,yBAAyBE,IAAGF,EAAC,EAAE;AAAA,IAC/C,CAAC,IAAII,GAAE,KAAK,MAAMA,IAAGC,EAAC;AAAA,EACxB;AACA,SAAOD;AACT;AACA,SAAS,eAAeF,IAAG;AACzB,WAASF,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACzC,QAAII,KAAI,QAAQ,UAAUJ,EAAC,IAAI,UAAUA,EAAC,IAAI,CAAC;AAC/C,IAAAA,KAAI,IAAI,QAAQ,OAAOI,EAAC,GAAG,IAAE,EAAE,QAAQ,SAAUJ,IAAG;AAClD,sBAAgBE,IAAGF,IAAGI,GAAEJ,EAAC,CAAC;AAAA,IAC5B,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiBE,IAAG,OAAO,0BAA0BE,EAAC,CAAC,IAAI,QAAQ,OAAOA,EAAC,CAAC,EAAE,QAAQ,SAAUJ,IAAG;AAChJ,aAAO,eAAeE,IAAGF,IAAG,OAAO,yBAAyBI,IAAGJ,EAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AACA,SAAOE;AACT;AACA,SAAS,gBAAgBE,IAAGF,IAAG;AAC7B,SAAO,kBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAUE,IAAGF,IAAG;AAC9F,WAAOE,GAAE,YAAYF,IAAGE;AAAA,EAC1B,GAAG,gBAAgBA,IAAGF,EAAC;AACzB;AACA,SAAS,eAAeF,IAAGE,IAAG;AAC5B,SAAO,gBAAgBF,EAAC,KAAK,sBAAsBA,IAAGE,EAAC,KAAK,4BAA4BF,IAAGE,EAAC,KAAK,iBAAiB;AACpH;AACA,SAAS,mBAAmBF,IAAG;AAC7B,SAAO,mBAAmBA,EAAC,KAAK,iBAAiBA,EAAC,KAAK,4BAA4BA,EAAC,KAAK,mBAAmB;AAC9G;AACA,SAAS,aAAaI,IAAGJ,IAAG;AAC1B,MAAI,YAAY,OAAOI,MAAK,CAACA,GAAG,QAAOA;AACvC,MAAIF,KAAIE,GAAE,OAAO,WAAW;AAC5B,MAAI,WAAWF,IAAG;AAChB,QAAIO,KAAIP,GAAE,KAAKE,IAAGJ,MAAK,SAAS;AAChC,QAAI,YAAY,OAAOS,GAAG,QAAOA;AACjC,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,aAAaT,KAAI,SAAS,QAAQI,EAAC;AAC7C;AACA,SAAS,eAAeA,IAAG;AACzB,MAAIK,KAAI,aAAaL,IAAG,QAAQ;AAChC,SAAO,YAAY,OAAOK,KAAIA,KAAIA,KAAI;AACxC;AACA,SAAS,QAAQJ,IAAG;AAClB;AAEA,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUA,IAAG;AAChG,WAAO,OAAOA;AAAA,EAChB,IAAI,SAAUA,IAAG;AACf,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EACpH,GAAG,QAAQA,EAAC;AACd;AACA,SAAS,4BAA4BL,IAAGC,IAAG;AACzC,MAAID,IAAG;AACL,QAAI,YAAY,OAAOA,GAAG,QAAO,kBAAkBA,IAAGC,EAAC;AACvD,QAAIG,KAAI,CAAC,EAAE,SAAS,KAAKJ,EAAC,EAAE,MAAM,GAAG,EAAE;AACvC,WAAO,aAAaI,MAAKJ,GAAE,gBAAgBI,KAAIJ,GAAE,YAAY,OAAO,UAAUI,MAAK,UAAUA,KAAI,MAAM,KAAKJ,EAAC,IAAI,gBAAgBI,MAAK,2CAA2C,KAAKA,EAAC,IAAI,kBAAkBJ,IAAGC,EAAC,IAAI;AAAA,EACvN;AACF;AACA,SAAS,cAAc;AACrB,gBAAc,SAAUC,IAAGF,IAAG;AAC5B,WAAO,IAAI,YAAYE,IAAG,QAAQF,EAAC;AAAA,EACrC;AACA,MAAIE,KAAI,OAAO,WACbF,KAAI,oBAAI,QAAQ;AAClB,WAAS,YAAYE,IAAGE,IAAGO,IAAG;AAC5B,QAAIN,KAAI,OAAOH,IAAGE,EAAC;AACnB,WAAOJ,GAAE,IAAIK,IAAGM,MAAKX,GAAE,IAAIE,EAAC,CAAC,GAAG,gBAAgBG,IAAG,YAAY,SAAS;AAAA,EAC1E;AACA,WAAS,YAAYH,IAAGE,IAAG;AACzB,QAAIO,KAAIX,GAAE,IAAII,EAAC;AACf,WAAO,OAAO,KAAKO,EAAC,EAAE,OAAO,SAAUX,IAAGI,IAAG;AAC3C,UAAIC,KAAIM,GAAEP,EAAC;AACX,UAAI,YAAY,OAAOC,GAAG,CAAAL,GAAEI,EAAC,IAAIF,GAAEG,EAAC;AAAA,WAAO;AACzC,iBAASI,KAAI,GAAG,WAAWP,GAAEG,GAAEI,EAAC,CAAC,KAAKA,KAAI,IAAIJ,GAAE,SAAS,CAAAI;AACzD,QAAAT,GAAEI,EAAC,IAAIF,GAAEG,GAAEI,EAAC,CAAC;AAAA,MACf;AACA,aAAOT;AAAA,IACT,GAAG,uBAAO,OAAO,IAAI,CAAC;AAAA,EACxB;AACA,SAAO,UAAU,aAAa,MAAM,GAAG,YAAY,UAAU,OAAO,SAAUA,IAAG;AAC/E,QAAII,KAAIF,GAAE,KAAK,KAAK,MAAMF,EAAC;AAC3B,QAAII,IAAG;AACL,MAAAA,GAAE,SAAS,YAAYA,IAAG,IAAI;AAC9B,UAAIO,KAAIP,GAAE;AACV,MAAAO,OAAMA,GAAE,SAAS,YAAYA,IAAG,IAAI;AAAA,IACtC;AACA,WAAOP;AAAA,EACT,GAAG,YAAY,UAAU,OAAO,OAAO,IAAI,SAAUA,IAAGO,IAAG;AACzD,QAAI,YAAY,OAAOA,IAAG;AACxB,UAAIN,KAAIL,GAAE,IAAI,IAAI;AAClB,aAAOE,GAAE,OAAO,OAAO,EAAE,KAAK,MAAME,IAAGO,GAAE,QAAQ,oBAAoB,SAAUT,IAAGF,IAAGI,IAAG;AACtF,YAAI,OAAOA,GAAG,QAAOF;AACrB,YAAIS,KAAIN,GAAEL,EAAC;AACX,eAAO,MAAM,QAAQW,EAAC,IAAI,MAAMA,GAAE,KAAK,GAAG,IAAI,YAAY,OAAOA,KAAI,MAAMA,KAAI;AAAA,MACjF,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,cAAc,OAAOA,IAAG;AAC1B,UAAIF,KAAI;AACR,aAAOP,GAAE,OAAO,OAAO,EAAE,KAAK,MAAME,IAAG,WAAY;AACjD,YAAIF,KAAI;AACR,eAAO,YAAY,OAAOA,GAAEA,GAAE,SAAS,CAAC,MAAMA,KAAI,CAAC,EAAE,MAAM,KAAKA,EAAC,GAAG,KAAK,YAAYA,IAAGO,EAAC,CAAC,GAAGE,GAAE,MAAM,MAAMT,EAAC;AAAA,MAC9G,CAAC;AAAA,IACH;AACA,WAAOA,GAAE,OAAO,OAAO,EAAE,KAAK,MAAME,IAAGO,EAAC;AAAA,EAC1C,GAAG,YAAY,MAAM,MAAM,SAAS;AACtC;AAEA,IAAI,OAAO,SAASC,QAAO;AAAC;AAC5B,IAAI,UAAU,CAAC;AACf,IAAI,YAAY,CAAC;AACjB,IAAI,qBAAqB;AACzB,IAAI,eAAe;AAAA,EACjB,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAI;AACF,MAAI,OAAO,WAAW,YAAa,WAAU;AAC7C,MAAI,OAAO,aAAa,YAAa,aAAY;AACjD,MAAI,OAAO,qBAAqB,YAAa,sBAAqB;AAClE,MAAI,OAAO,gBAAgB,YAAa,gBAAe;AACzD,SAASV,IAAG;AAAC;AAEb,IAAI,OAAO,QAAQ,aAAa,CAAC;AAAjC,IACE,iBAAiB,KAAK;AADxB,IAEE,YAAY,mBAAmB,SAAS,KAAK;AAC/C,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,oBAAoB;AACxB,IAAI,cAAc;AAClB,IAAI,aAAa,CAAC,CAAC,OAAO;AAC1B,IAAI,SAAS,CAAC,CAAC,SAAS,mBAAmB,CAAC,CAAC,SAAS,QAAQ,OAAO,SAAS,qBAAqB,cAAc,OAAO,SAAS,kBAAkB;AACnJ,IAAI,QAAQ,CAAC,UAAU,QAAQ,MAAM,KAAK,CAAC,UAAU,QAAQ,UAAU;AAEvE,IAAI;AACJ,IAAI,IAAI;AAAR,IACE,IAAI;AACN,IAAI,IAAI;AAAA,EACJ,SAAS;AAAA,IACP,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,YAAY;AAAA,IACZ,KAAK;AAAA,IACL,cAAc;AAAA,IACd,KAAK;AAAA,IACL,YAAY;AAAA,IACZ,KAAK;AAAA,IACL,WAAW;AAAA,IACX,KAAK;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,WAAW;AAAA,EACb;AAAA,EACA,iBAAiB;AAAA,IACf,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,cAAc;AAAA,IACd,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,WAAW;AAAA,EACb;AAAA,EACA,MAAM;AAAA,IACJ,cAAc;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,cAAc;AAAA,IACd,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,YAAY;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,YAAY;AAAA,IACV,eAAe;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,YAAY;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,cAAc;AAAA,IACZ,YAAY;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,YAAY;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,cAAc;AAAA,IACZ,cAAc;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,cAAc;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AACF;AA5FF,IA6FE,IAAI;AAAA,EACF,OAAO;AAAA,EACP,cAAc;AAAA,EACd,SAAS;AAAA,EACT,WAAW;AACb;AAlGF,IAmGE,IAAI,CAAC,cAAc,cAAc,YAAY,oBAAoB,iBAAiB,iBAAiB,aAAa,iBAAiB,aAAa,WAAW,YAAY,iBAAiB,gBAAgB,WAAW,eAAe;AAClO,IAAI,IAAI;AAAR,IACE,IAAI;AADN,IAEE,IAAI;AAFN,IAGE,IAAI;AAHN,IAIE,IAAI;AAJN,IAKE,IAAI;AALN,IAME,IAAI;AANN,IAOE,IAAI;AAPN,IAQE,IAAI;AARN,IASE,IAAI;AATN,IAUE,IAAI;AAVN,IAWE,IAAI;AAXN,IAYE,IAAI;AAZN,IAaE,IAAI;AAbN,IAcE,IAAI;AAdN,IAeE,IAAI;AAfN,IAgBE,IAAI;AAhBN,IAiBE,IAAI;AAjBN,IAkBE,IAAI;AAlBN,IAmBE,IAAI;AAnBN,IAoBE,IAAI;AApBN,IAqBE,IAAI;AArBN,IAsBE,IAAI;AAtBN,IAuBE,IAAI;AAvBN,IAwBE,IAAI;AAxBN,IAyBE,IAAI;AAzBN,IA0BE,IAAI;AA1BN,IA2BE,IAAI;AA3BN,IA4BE,IAAI;AA5BN,IA6BE,IAAI;AA7BN,IA8BE,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AA9BnD,IA+BE,MAAM,MAAM,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;AAClX,IAAI,KAAK;AAAA,EACL,SAAS;AAAA,IACP,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,iBAAiB;AAAA,IACf,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,MAAM;AAAA,IACJ,KAAK;AAAA,EACP;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,EACP;AAAA,EACA,YAAY;AAAA,IACV,KAAK;AAAA,EACP;AAAA,EACA,YAAY;AAAA,IACV,KAAK;AAAA,EACP;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,EACP;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,EACP;AAAA,EACA,MAAM;AAAA,IACJ,KAAK;AAAA,EACP;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,EACP;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,EACP;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,EACP;AACF;AACF,IAAI,KAAK;AAAA,EACL,uBAAuB;AAAA,IACrB,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,sBAAsB;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,yBAAyB;AAAA,IACvB,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,0BAA0B;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,wBAAwB;AAAA,IACtB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,gCAAgC;AAAA,IAC9B,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,wBAAwB;AAAA,IACtB,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,IAC3B,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,4BAA4B;AAAA,IAC1B,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,uBAAuB;AAAA,IACrB,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,IAC3B,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,IAC3B,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,yBAAyB;AAAA,IACvB,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,IAC3B,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,uBAAuB;AAAA,IACrB,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,yBAAyB;AAAA,IACvB,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,IAC3B,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AACF;AACF,IAAI,KAAK,oBAAI,IAAI,CAAC,CAAC,WAAW;AAAA,EAC1B,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS,WAAW,SAAS,QAAQ,QAAQ;AAAA,EACxD,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,WAAW;AAAA,EACd,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS,WAAW,SAAS,MAAM;AAAA,EAC9C,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,SAAS;AAAA,EACZ,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS,WAAW,SAAS,MAAM;AAAA,EAC9C,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,iBAAiB;AAAA,EACpB,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS,WAAW,SAAS,MAAM;AAAA,EAC9C,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,UAAU;AAAA,EACb,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS;AAAA,EACpB,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,QAAQ;AAAA,EACX,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,OAAO;AAAA,EAClB,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,SAAS;AAAA,EACZ,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS;AAAA,EACpB,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,aAAa;AAAA,EAChB,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS;AAAA,EACpB,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,cAAc;AAAA,EACjB,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS;AAAA,EACpB,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,UAAU;AAAA,EACb,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,OAAO;AAAA,EAClB,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,cAAc;AAAA,EACjB,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,OAAO;AAAA,EAClB,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,QAAQ;AAAA,EACX,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS;AAAA,EACpB,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,cAAc;AAAA,EACjB,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS;AAAA,EACpB,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,cAAc;AAAA,EACjB,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,OAAO;AAAA,EAClB,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,cAAc;AAAA,EACjB,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,UAAU;AAAA,EACrB,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,CAAC,CAAC;AA1FL,IA2FE,KAAK;AAAA,EACH,QAAQ;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACV,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,UAAU;AAAA,EACZ;AACF;AACF,IAAI,KAAK,CAAC,OAAO,UAAU,QAAQ,gBAAgB;AAAnD,IACE,KAAK;AAAA,EACH,KAAK;AAAA,IACH,KAAK;AAAA,IACL,UAAU;AAAA,EACZ;AAAA,EACA,eAAe;AAAA,IACb,MAAM;AAAA,IACN,kBAAkB;AAAA,EACpB;AACF;AAVF,IAWE,KAAK,CAAC,KAAK;AACb,IAAI,IAAI;AAAR,IACE,IAAI;AADN,IAEE,IAAI;AAFN,IAGE,IAAI;AAHN,IAIE,KAAK,gBAAgB,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;AACtD,IAAI,KAAK;AAAA,EACP,KAAK;AAAA,IACH,UAAU;AAAA,EACZ;AAAA,EACA,eAAe;AAAA,IACb,kBAAkB;AAAA,EACpB;AACF;AACA,IAAI,KAAK;AAAA,EACL,oBAAoB;AAAA,IAClB,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,4BAA4B;AAAA,IAC1B,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AACF;AATF,IAUE,KAAK;AAAA,EACH,KAAK;AAAA,IACH,KAAK;AAAA,EACP;AAAA,EACA,eAAe;AAAA,IACb,MAAM;AAAA,EACR;AACF;AACF,IAAI,KAAK;AAAA,EACL,KAAK;AAAA,IACH,KAAK;AAAA,EACP;AAAA,EACA,eAAe;AAAA,IACb,eAAe;AAAA,EACjB;AACF;AAEF,IAAI;AACJ,IAAI,MAAM;AAAA,EACN,OAAO;AAAA,EACP,cAAc;AAAA,EACd,SAAS;AAAA,EACT,WAAW;AACb;AALF,IAME,MAAM,CAAC,cAAc,cAAc,YAAY,oBAAoB,iBAAiB,iBAAiB,aAAa,iBAAiB,aAAa,WAAW,YAAY,iBAAiB,gBAAgB,WAAW,eAAe;AACpO,IAAI,MAAM;AAAV,IACE,MAAM;AADR,IAEE,MAAM;AAFR,IAGE,MAAM;AAHR,IAIE,MAAM;AAJR,IAKE,MAAM;AALR,IAME,MAAM;AANR,IAOE,MAAM;AAPR,IAQE,MAAM;AARR,IASE,MAAM;AATR,IAUE,MAAM;AAVR,IAWE,MAAM;AAXR,IAYE,MAAM;AAZR,IAaE,MAAM;AAbR,IAcE,MAAM;AAdR,IAeE,MAAM;AAfR,IAgBE,MAAM;AAhBR,IAiBE,MAAM;AAjBR,IAkBE,MAAM;AAlBR,IAmBE,MAAM;AAnBR,IAoBE,MAAM;AApBR,IAqBE,MAAM;AArBR,IAsBE,MAAM;AAtBR,IAuBE,MAAM;AAvBR,IAwBE,MAAM;AAxBR,IAyBE,MAAM;AAzBR,IA0BE,MAAM;AA1BR,IA2BE,MAAM;AA3BR,IA4BE,MAAM;AA5BR,IA6BE,MAAM;AA7BR,IA8BE,MAAM,MAAM,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,gBAAgB,gBAAgB,gBAAgB,gBAAgB,gBAAgB,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG;AAC9a,IAAI,IAAI;AAAR,IACE,MAAM;AADR,IAEE,MAAM;AAFR,IAGE,MAAM;AAHR,IAIE,OAAO,gBAAgB,gBAAgB,CAAC,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG;AAC9D,IAAI,OAAO;AAAA,EACP,SAAS;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AAAA,EACA,iBAAiB;AAAA,IACf,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AAAA,EACA,MAAM;AAAA,IACJ,cAAc;AAAA,EAChB;AAAA,EACA,cAAc;AAAA,IACZ,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,IACV,eAAe;AAAA,EACjB;AAAA,EACA,YAAY;AAAA,IACV,YAAY;AAAA,EACd;AAAA,EACA,QAAQ;AAAA,IACN,YAAY;AAAA,EACd;AAAA,EACA,cAAc;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,MAAM;AAAA,IACJ,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,cAAc;AAAA,EAChB;AAAA,EACA,cAAc;AAAA,IACZ,cAAc;AAAA,EAChB;AAAA,EACA,aAAa;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,cAAc;AAAA,EAChB;AACF;AA3DF,IA4DE,MAAM;AAAA,EACJ,SAAS,CAAC,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3C,SAAS,CAAC,QAAQ,QAAQ,MAAM;AAAA,EAChC,OAAO,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,EACtC,iBAAiB,CAAC,SAAS,SAAS,SAAS,OAAO;AAAA,EACpD,MAAM,CAAC,OAAO;AAAA,EACd,cAAc,CAAC,QAAQ;AAAA,EACvB,YAAY,CAAC,OAAO;AAAA,EACpB,YAAY,CAAC,MAAM;AAAA,EACnB,QAAQ,CAAC,MAAM;AAAA,EACf,cAAc,CAAC,OAAO;AAAA,EACtB,MAAM,CAAC,MAAM;AAAA,EACb,OAAO,CAAC,MAAM;AAAA,EACd,cAAc,CAAC,OAAO;AAAA,EACtB,aAAa,CAAC,OAAO;AAAA,EACrB,QAAQ,CAAC,MAAM;AACjB;AA5EF,IA6EE,OAAO;AAAA,EACL,SAAS;AAAA,IACP,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,EACR;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,EACR;AACF;AAxIF,IAyIE,MAAM,CAAC,YAAY,cAAc,YAAY,WAAW,cAAc,aAAa,aAAa;AAzIlG,IA0IE,OAAO,CAAC,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAS,SAAS,SAAS,UAAU,SAAS,QAAQ,QAAQ,SAAS,QAAQ,QAAQ,SAAS,SAAS,MAAM,EAAE,OAAO,KAAK,GAAG;AA1IpQ,IA2IE,MAAM,CAAC,SAAS,WAAW,SAAS,QAAQ,WAAW,UAAU,UAAU;AA3I7E,IA4IE,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AA5ItC,IA6IE,MAAM,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AA7I3D,IA8IE,MAAM,CAAC,MAAM,MAAM,aAAa,YAAY;AA9I9C,IA+IE,OAAO,CAAC,EAAE,OAAO,mBAAmB,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,QAAQ,UAAU,QAAQ,aAAa,UAAU,aAAa,mBAAmB,iBAAiB,QAAQ,WAAW,UAAU,sBAAsB,uBAAuB,kBAAkB,eAAe,mBAAmB,oBAAoB,MAAM,YAAY,cAAc,SAAS,cAAc,cAAc,aAAa,aAAa,SAAS,cAAc,gBAAgB,QAAQ,YAAY,YAAY,SAAS,MAAM,cAAc,eAAe,IAAI,OAAO,IAAI,cAAc,IAAI,SAAS,IAAI,SAAS,CAAC,EAAE,OAAO,IAAI,IAAI,SAAUG,IAAG;AACloB,SAAO,GAAG,OAAOA,IAAG,GAAG;AACzB,CAAC,CAAC,EAAE,OAAO,IAAI,IAAI,SAAUA,IAAG;AAC9B,SAAO,KAAK,OAAOA,EAAC;AACtB,CAAC,CAAC;AACJ,IAAI,KAAK;AAAA,EACL,uBAAuB;AAAA,IACrB,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,sBAAsB;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,EACP;AAAA,EACA,yBAAyB;AAAA,IACvB,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,0BAA0B;AAAA,IACxB,KAAK;AAAA,EACP;AACF;AAEF,IAAI,uBAAuB;AAC3B,IAAI,gBAAgB;AACpB,IAAI,qBAAqB;AACzB,IAAI,4BAA4B;AAChC,IAAI,gBAAgB;AACpB,IAAI,yBAAyB;AAC7B,IAAI,iCAAiC;AACrC,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,IAAI,8BAA8B;AAClC,IAAI,0BAA0B;AAC9B,IAAI,sCAAsC,CAAC,QAAQ,QAAQ,SAAS,QAAQ;AAC5E,IAAI,kBAAkB,CAAC,YAAY,WAAW,WAAW,QAAQ;AACjE,IAAI,aAAa,WAAY;AAC3B,MAAI;AACF,WAAO;AAAA,EACT,SAAS,MAAM;AACb,WAAO;AAAA,EACT;AACF,EAAE;AACF,SAAS,YAAY,KAAK;AAExB,SAAO,IAAI,MAAM,KAAK;AAAA,IACpB,KAAK,SAASQ,KAAI,QAAQ,MAAM;AAC9B,aAAO,QAAQ,SAAS,OAAO,IAAI,IAAI,OAAO,CAAC;AAAA,IACjD;AAAA,EACF,CAAC;AACH;AACA,IAAI,mBAAmB,eAAe,CAAC,GAAG,CAAC;AAK3C,iBAAiB,CAAC,IAAI,eAAe,eAAe,eAAe,eAAe,CAAC,GAAG;AAAA,EACpF,cAAc;AAChB,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,aAAa,CAAC;AACxC,IAAI,kBAAkB,YAAY,gBAAgB;AAClD,IAAI,mBAAmB,eAAe,CAAC,GAAG,EAAE;AAI5C,iBAAiB,CAAC,IAAI,eAAe,eAAe,eAAe,eAAe,CAAC,GAAG;AAAA,EACpF,SAAS;AACX,CAAC,GAAG,iBAAiB,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,aAAa,CAAC;AACvD,IAAI,kBAAkB,YAAY,gBAAgB;AAClD,IAAI,wBAAwB,eAAe,CAAC,GAAG,IAAI;AACnD,sBAAsB,CAAC,IAAI,eAAe,eAAe,CAAC,GAAG,sBAAsB,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC;AACjG,IAAI,uBAAuB,YAAY,qBAAqB;AAC5D,IAAI,wBAAwB,eAAe,CAAC,GAAG,IAAI;AACnD,sBAAsB,CAAC,IAAI,eAAe,eAAe,CAAC,GAAG,sBAAsB,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC;AACjG,IAAI,uBAAuB,YAAY,qBAAqB;AAC5D,IAAI,gCAAgC;AACpC,IAAI,wBAAwB;AAC5B,IAAI,sBAAsB;AAC1B,IAAI,yBAAyB,eAAe,CAAC,GAAG,EAAE;AAClD,IAAI,wBAAwB,YAAY,sBAAsB;AAC9D,IAAI,kCAAkC,CAAC,SAAS,eAAe,aAAa,qBAAqB,cAAc;AAC/G,IAAI,kBAAkB;AACtB,IAAI,mBAAmB,CAAC,EAAE,OAAO,mBAAmB,EAAE,GAAG,mBAAmB,IAAI,CAAC;AAEjF,IAAI,UAAU,OAAO,qBAAqB,CAAC;AAC3C,SAAS,cAAc,MAAM;AAC3B,MAAI,UAAU,SAAS,cAAc,YAAY,OAAO,GAAG;AAC3D,MAAI,SAAS;AACX,WAAO,QAAQ,aAAa,IAAI;AAAA,EAClC;AACF;AACA,SAAS,OAAO,KAAK;AAGnB,MAAI,QAAQ,GAAI,QAAO;AACvB,MAAI,QAAQ,QAAS,QAAO;AAC5B,MAAI,QAAQ,OAAQ,QAAO;AAC3B,SAAO;AACT;AACA,IAAI,YAAY,OAAO,SAAS,kBAAkB,YAAY;AACxD,UAAQ,CAAC,CAAC,sBAAsB,cAAc,GAAG,CAAC,mBAAmB,WAAW,GAAG,CAAC,uBAAuB,eAAe,GAAG,CAAC,sBAAsB,cAAc,GAAG,CAAC,0BAA0B,kBAAkB,GAAG,CAAC,yBAAyB,gBAAgB,GAAG,CAAC,qBAAqB,YAAY,GAAG,CAAC,+BAA+B,sBAAsB,GAAG,CAAC,wCAAwC,8BAA8B,GAAG,CAAC,yCAAyC,8BAA8B,GAAG,CAAC,0BAA0B,kBAAkB,GAAG,CAAC,wBAAwB,gBAAgB,GAAG,CAAC,6BAA6B,oBAAoB,GAAG,CAAC,4BAA4B,oBAAoB,GAAG,CAAC,2BAA2B,kBAAkB,CAAC;AAC7uB,QAAM,QAAQ,SAAUC,OAAM;AAC5B,QAAIC,SAAQ,eAAeD,OAAM,CAAC,GAChC,OAAOC,OAAM,CAAC,GACd,MAAMA,OAAM,CAAC;AACf,QAAI,MAAM,OAAO,cAAc,IAAI,CAAC;AACpC,QAAI,QAAQ,UAAa,QAAQ,MAAM;AACrC,cAAQ,GAAG,IAAI;AAAA,IACjB;AAAA,EACF,CAAC;AACH;AAVM;AAWN,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,eAAe;AAAA,EACf,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,sBAAsB;AAAA,EACtB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,kBAAkB;AACpB;AAGA,IAAI,QAAQ,cAAc;AACxB,UAAQ,YAAY,QAAQ;AAC9B;AACA,IAAI,UAAU,eAAe,eAAe,CAAC,GAAG,QAAQ,GAAG,OAAO;AAClE,IAAI,CAAC,QAAQ,eAAgB,SAAQ,mBAAmB;AACxD,IAAI,SAAS,CAAC;AACd,OAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,KAAK;AAC3C,SAAO,eAAe,QAAQ,KAAK;AAAA,IACjC,YAAY;AAAA,IACZ,KAAK,SAASC,KAAI,KAAK;AACrB,cAAQ,GAAG,IAAI;AACf,kBAAY,QAAQ,SAAU,IAAI;AAChC,eAAO,GAAG,MAAM;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,IACA,KAAK,SAASH,OAAM;AAClB,aAAO,QAAQ,GAAG;AAAA,IACpB;AAAA,EACF,CAAC;AACH,CAAC;AAGD,OAAO,eAAe,QAAQ,gBAAgB;AAAA,EAC5C,YAAY;AAAA,EACZ,KAAK,SAAS,IAAI,KAAK;AACrB,YAAQ,YAAY;AACpB,gBAAY,QAAQ,SAAU,IAAI;AAChC,aAAO,GAAG,MAAM;AAAA,IAClB,CAAC;AAAA,EACH;AAAA,EACA,KAAK,SAAS,MAAM;AAClB,WAAO,QAAQ;AAAA,EACjB;AACF,CAAC;AACD,OAAO,oBAAoB;AAC3B,IAAI,cAAc,CAAC;AACnB,SAAS,SAAS,IAAI;AACpB,cAAY,KAAK,EAAE;AACnB,SAAO,WAAY;AACjB,gBAAY,OAAO,YAAY,QAAQ,EAAE,GAAG,CAAC;AAAA,EAC/C;AACF;AAEA,IAAI,MAAM;AACV,IAAI,uBAAuB;AAAA,EACzB,MAAM;AAAA,EACN,GAAG;AAAA,EACH,GAAG;AAAA,EACH,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AACT;AACA,SAAS,UAAUI,MAAK;AACtB,MAAI,CAACA,QAAO,CAAC,QAAQ;AACnB;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,cAAc,OAAO;AAC1C,QAAM,aAAa,QAAQ,UAAU;AACrC,QAAM,YAAYA;AAClB,MAAI,eAAe,SAAS,KAAK;AACjC,MAAI,cAAc;AAClB,WAASR,KAAI,aAAa,SAAS,GAAGA,KAAI,IAAIA,MAAK;AACjD,QAAI,QAAQ,aAAaA,EAAC;AAC1B,QAAI,WAAW,MAAM,WAAW,IAAI,YAAY;AAChD,QAAI,CAAC,SAAS,MAAM,EAAE,QAAQ,OAAO,IAAI,IAAI;AAC3C,oBAAc;AAAA,IAChB;AAAA,EACF;AACA,WAAS,KAAK,aAAa,OAAO,WAAW;AAC7C,SAAOQ;AACT;AACA,IAAI,SAAS;AACb,SAAS,eAAe;AACtB,MAAI,OAAO;AACX,MAAI,KAAK;AACT,SAAO,SAAS,GAAG;AACjB,UAAM,OAAO,KAAK,OAAO,IAAI,KAAK,CAAC;AAAA,EACrC;AACA,SAAO;AACT;AACA,SAAS,QAAQ,KAAK;AACpB,MAAI,QAAQ,CAAC;AACb,WAASR,MAAK,OAAO,CAAC,GAAG,WAAW,GAAGA,QAAM;AAC3C,UAAMA,EAAC,IAAI,IAAIA,EAAC;AAAA,EAClB;AACA,SAAO;AACT;AACA,SAAS,WAAW,MAAM;AACxB,MAAI,KAAK,WAAW;AAClB,WAAO,QAAQ,KAAK,SAAS;AAAA,EAC/B,OAAO;AACL,YAAQ,KAAK,aAAa,OAAO,KAAK,IAAI,MAAM,GAAG,EAAE,OAAO,SAAUA,IAAG;AACvE,aAAOA;AAAA,IACT,CAAC;AAAA,EACH;AACF;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,GAAG,OAAO,GAAG,EAAE,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM;AACxI;AACA,SAAS,eAAe,YAAY;AAClC,SAAO,OAAO,KAAK,cAAc,CAAC,CAAC,EAAE,OAAO,SAAU,KAAK,eAAe;AACxE,WAAO,MAAM,GAAG,OAAO,eAAe,IAAK,EAAE,OAAO,WAAW,WAAW,aAAa,CAAC,GAAG,IAAK;AAAA,EAClG,GAAG,EAAE,EAAE,KAAK;AACd;AACA,SAAS,WAAWS,SAAQ;AAC1B,SAAO,OAAO,KAAKA,WAAU,CAAC,CAAC,EAAE,OAAO,SAAU,KAAK,WAAW;AAChE,WAAO,MAAM,GAAG,OAAO,WAAW,IAAI,EAAE,OAAOA,QAAO,SAAS,EAAE,KAAK,GAAG,GAAG;AAAA,EAC9E,GAAG,EAAE;AACP;AACA,SAAS,sBAAsB,WAAW;AACxC,SAAO,UAAU,SAAS,qBAAqB,QAAQ,UAAU,MAAM,qBAAqB,KAAK,UAAU,MAAM,qBAAqB,KAAK,UAAU,WAAW,qBAAqB,UAAU,UAAU,SAAS,UAAU;AAC9N;AACA,SAAS,gBAAgBJ,OAAM;AAC7B,MAAI,YAAYA,MAAK,WACnB,iBAAiBA,MAAK,gBACtB,YAAYA,MAAK;AACnB,MAAI,QAAQ;AAAA,IACV,WAAW,aAAa,OAAO,iBAAiB,GAAG,OAAO;AAAA,EAC5D;AACA,MAAI,iBAAiB,aAAa,OAAO,UAAU,IAAI,IAAI,IAAI,EAAE,OAAO,UAAU,IAAI,IAAI,IAAI;AAC9F,MAAI,aAAa,SAAS,OAAO,UAAU,OAAO,MAAM,UAAU,QAAQ,KAAK,IAAI,IAAI,EAAE,OAAO,UAAU,OAAO,MAAM,UAAU,QAAQ,KAAK,IAAI,IAAI;AACtJ,MAAI,cAAc,UAAU,OAAO,UAAU,QAAQ,OAAO;AAC5D,MAAI,QAAQ;AAAA,IACV,WAAW,GAAG,OAAO,gBAAgB,GAAG,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,WAAW;AAAA,EACtF;AACA,MAAI,OAAO;AAAA,IACT,WAAW,aAAa,OAAO,YAAY,IAAI,IAAI,QAAQ;AAAA,EAC7D;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,YAAY,MAAM,WACpB,cAAc,MAAM,OACpB,QAAQ,gBAAgB,SAAS,gBAAgB,aACjD,eAAe,MAAM,QACrB,SAAS,iBAAiB,SAAS,gBAAgB,cACnD,sBAAsB,MAAM,eAC5B,gBAAgB,wBAAwB,SAAS,QAAQ;AAC3D,MAAI,MAAM;AACV,MAAI,iBAAiB,OAAO;AAC1B,WAAO,aAAa,OAAO,UAAU,IAAI,MAAM,QAAQ,GAAG,MAAM,EAAE,OAAO,UAAU,IAAI,MAAM,SAAS,GAAG,MAAM;AAAA,EACjH,WAAW,eAAe;AACxB,WAAO,yBAAyB,OAAO,UAAU,IAAI,KAAK,mBAAmB,EAAE,OAAO,UAAU,IAAI,KAAK,OAAO;AAAA,EAClH,OAAO;AACL,WAAO,aAAa,OAAO,UAAU,IAAI,KAAK,MAAM,EAAE,OAAO,UAAU,IAAI,KAAK,MAAM;AAAA,EACxF;AACA,SAAO,SAAS,OAAO,UAAU,OAAO,OAAO,UAAU,QAAQ,KAAK,IAAI,IAAI,EAAE,OAAO,UAAU,OAAO,OAAO,UAAU,QAAQ,KAAK,IAAI,IAAI;AAC9I,SAAO,UAAU,OAAO,UAAU,QAAQ,OAAO;AACjD,SAAO;AACT;AAEA,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEjB,SAAS,MAAM;AACb,MAAI,MAAM;AACV,MAAI,MAAM;AACV,MAAI,KAAK,OAAO;AAChB,MAAI,KAAK,OAAO;AAChB,MAAIK,KAAI;AACR,MAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,QAAI,QAAQ,IAAI,OAAO,MAAM,OAAO,KAAK,KAAK,GAAG,GAAG;AACpD,QAAI,iBAAiB,IAAI,OAAO,OAAO,OAAO,KAAK,KAAK,GAAG,GAAG;AAC9D,QAAI,QAAQ,IAAI,OAAO,MAAM,OAAO,GAAG,GAAG,GAAG;AAC7C,IAAAA,KAAIA,GAAE,QAAQ,OAAO,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,QAAQ,gBAAgB,KAAK,OAAO,IAAI,GAAG,CAAC,EAAE,QAAQ,OAAO,IAAI,OAAO,EAAE,CAAC;AAAA,EACvH;AACA,SAAOA;AACT;AACA,IAAI,eAAe;AACnB,SAAS,YAAY;AACnB,MAAI,OAAO,cAAc,CAAC,cAAc;AACtC,cAAU,IAAI,CAAC;AACf,mBAAe;AAAA,EACjB;AACF;AACA,IAAI,YAAY;AAAA,EACd,QAAQ,SAAS,SAAS;AACxB,WAAO;AAAA,MACL,KAAK;AAAA,QACH;AAAA,QACA,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ;AACtB,WAAO;AAAA,MACL,0BAA0B,SAAS,2BAA2B;AAC5D,kBAAU;AAAA,MACZ;AAAA,MACA,aAAa,SAAS,cAAc;AAClC,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,MAAM,UAAU,CAAC;AACrB,IAAI,CAAC,IAAI,oBAAoB,EAAG,KAAI,oBAAoB,IAAI,CAAC;AAC7D,IAAI,CAAC,IAAI,oBAAoB,EAAE,OAAQ,KAAI,oBAAoB,EAAE,SAAS,CAAC;AAC3E,IAAI,CAAC,IAAI,oBAAoB,EAAE,MAAO,KAAI,oBAAoB,EAAE,QAAQ,CAAC;AACzE,IAAI,CAAC,IAAI,oBAAoB,EAAE,MAAO,KAAI,oBAAoB,EAAE,QAAQ,CAAC;AACzE,IAAI,YAAY,IAAI,oBAAoB;AAExC,IAAI,YAAY,CAAC;AACjB,IAAI,YAAY,SAAS,WAAW;AAClC,WAAS,oBAAoB,oBAAoB,SAAS;AAC1D,WAAS;AACT,YAAU,IAAI,SAAU,IAAI;AAC1B,WAAO,GAAG;AAAA,EACZ,CAAC;AACH;AACA,IAAI,SAAS;AACb,IAAI,QAAQ;AACV,YAAU,SAAS,gBAAgB,WAAW,eAAe,iBAAiB,KAAK,SAAS,UAAU;AACtG,MAAI,CAAC,OAAQ,UAAS,iBAAiB,oBAAoB,SAAS;AACtE;AACA,SAAS,SAAU,IAAI;AACrB,MAAI,CAAC,OAAQ;AACb,WAAS,WAAW,IAAI,CAAC,IAAI,UAAU,KAAK,EAAE;AAChD;AAEA,SAAS,OAAO,eAAe;AAC7B,MAAI,MAAM,cAAc,KACtB,wBAAwB,cAAc,YACtC,aAAa,0BAA0B,SAAS,CAAC,IAAI,uBACrD,wBAAwB,cAAc,UACtC,WAAW,0BAA0B,SAAS,CAAC,IAAI;AACrD,MAAI,OAAO,kBAAkB,UAAU;AACrC,WAAO,WAAW,aAAa;AAAA,EACjC,OAAO;AACL,WAAO,IAAI,OAAO,KAAK,GAAG,EAAE,OAAO,eAAe,UAAU,GAAG,GAAG,EAAE,OAAO,SAAS,IAAI,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,OAAO,KAAK,GAAG;AAAA,EACjI;AACF;AAEA,SAAS,gBAAgB,SAAS,QAAQ,UAAU;AAClD,MAAI,WAAW,QAAQ,MAAM,KAAK,QAAQ,MAAM,EAAE,QAAQ,GAAG;AAC3D,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,MAAM,QAAQ,MAAM,EAAE,QAAQ;AAAA,IAChC;AAAA,EACF;AACF;AAMA,IAAI,gBAAgB,SAASC,eAAc,MAAM,aAAa;AAC5D,SAAO,SAAUnB,IAAGoB,IAAGC,IAAGC,IAAG;AAC3B,WAAO,KAAK,KAAK,aAAatB,IAAGoB,IAAGC,IAAGC,EAAC;AAAA,EAC1C;AACF;AAaA,IAAI,SAAS,SAAS,iBAAiB,SAAS,IAAI,cAAc,aAAa;AAC7E,MAAI,OAAO,OAAO,KAAK,OAAO,GAC5B,SAAS,KAAK,QACd,WAAW,gBAAgB,SAAY,cAAc,IAAI,WAAW,IAAI,IACxEd,IACA,KACA;AACF,MAAI,iBAAiB,QAAW;AAC9B,IAAAA,KAAI;AACJ,aAAS,QAAQ,KAAK,CAAC,CAAC;AAAA,EAC1B,OAAO;AACL,IAAAA,KAAI;AACJ,aAAS;AAAA,EACX;AACA,SAAOA,KAAI,QAAQA,MAAK;AACtB,UAAM,KAAKA,EAAC;AACZ,aAAS,SAAS,QAAQ,QAAQ,GAAG,GAAG,KAAK,OAAO;AAAA,EACtD;AACA,SAAO;AACT;AAOA,SAAS,MAAM,SAAS;AACtB,MAAI,mBAAmB,OAAO,EAAE,WAAW,EAAG,QAAO;AACrD,SAAO,QAAQ,YAAY,CAAC,EAAE,SAAS,EAAE;AAC3C;AAEA,SAAS,eAAe,OAAO;AAC7B,SAAO,OAAO,KAAK,KAAK,EAAE,OAAO,SAAU,KAAK,UAAU;AACxD,QAAIe,QAAO,MAAM,QAAQ;AACzB,QAAI,WAAW,CAAC,CAACA,MAAK;AACtB,QAAI,UAAU;AACZ,UAAIA,MAAK,QAAQ,IAAIA,MAAK;AAAA,IAC5B,OAAO;AACL,UAAI,QAAQ,IAAIA;AAAA,IAClB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,SAAS,YAAY,QAAQ,OAAO;AAClC,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,MAAI,oBAAoB,OAAO,WAC7B,YAAY,sBAAsB,SAAS,QAAQ;AACrD,MAAI,aAAa,eAAe,KAAK;AACrC,MAAI,OAAO,UAAU,MAAM,YAAY,cAAc,CAAC,WAAW;AAC/D,cAAU,MAAM,QAAQ,QAAQ,eAAe,KAAK,CAAC;AAAA,EACvD,OAAO;AACL,cAAU,OAAO,MAAM,IAAI,eAAe,eAAe,CAAC,GAAG,UAAU,OAAO,MAAM,KAAK,CAAC,CAAC,GAAG,UAAU;AAAA,EAC1G;AAQA,MAAI,WAAW,OAAO;AACpB,gBAAY,MAAM,KAAK;AAAA,EACzB;AACF;AAEA,IAAI,OAAO;AACX,IAAI,MAAM;AAGV,IAAI,gBAAgB,CAAC,CAAC,KAAkB,YAAY,0DAA0D;AAAA,EAC5G,IAAI;AAAA,EACJ,IAAI;AACN,CAAC,CAAC,GAAG,CAAC,KAAkB,YAAY,uCAAuC;AAAA,EACzE,IAAI;AACN,CAAC,CAAC,GAAG,CAAC,MAAmB,YAAY,yCAAyC;AAAA,EAC5E,IAAI;AACN,CAAC,CAAC,CAAC;AAQH,IAAI,gBAAgB,CAAC,CAAC,KAAkB,YAAY,kGAAkG;AAAA,EACpJ,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,IAAI;AACN,CAAC,CAAC,GAAG,CAAC,KAAkB,YAAY,kGAAkG;AAAA,EACpI,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,IAAI;AACN,CAAC,CAAC,GAAG,CAAC,KAAkB,YAAY,iDAAiD;AAAA,EACnF,OAAO;AAAA,EACP,IAAI;AACN,CAAC,CAAC,GAAG,CAAC,KAAkB,YAAY,mDAAmD;AAAA,EACrF,OAAO;AAAA,EACP,IAAI;AACN,CAAC,CAAC,GAAG,CAAC,KAAkB,YAAY,kGAAkG;AAAA,EACpI,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,OAAO;AACT,CAAC,CAAC,GAAG,CAAC,KAAkB,YAAY,kGAAkG;AAAA,EACpI,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,OAAO;AACT,CAAC,CAAC,GAAG,CAAC,KAAkB,YAAY,iDAAiD;AAAA,EACnF,IAAI;AAAA,EACJ,OAAO;AACT,CAAC,CAAC,GAAG,CAAC,KAAkB,YAAY,mDAAmD;AAAA,EACrF,IAAI;AAAA,EACJ,OAAO;AACT,CAAC,CAAC,GAAG,CAAC,KAAkB,YAAY,sCAAsC;AAAA,EACxE,IAAI;AAAA,EACJ,IAAI;AACN,CAAC,CAAC,GAAG,CAAC,MAAmB,YAAY,oBAAoB;AAAA,EACvD,IAAI;AACN,CAAC,CAAC,GAAG,CAAC,MAAmB,YAAY,qCAAqC;AAAA,EACxE,OAAO;AAAA,EACP,IAAI;AACN,CAAC,CAAC,CAAC;AAEH,IAAI,SAAS,UAAU;AAAvB,IACE,QAAQ,UAAU;AACpB,IAAI,eAAe,OAAO,KAAK,oBAAoB;AACnD,IAAI,sBAAsB,aAAa,OAAO,SAAU,KAAK,UAAU;AACrE,MAAI,QAAQ,IAAI,OAAO,KAAK,qBAAqB,QAAQ,CAAC;AAC1D,SAAO;AACT,GAAG,CAAC,CAAC;AACL,IAAI,uBAAuB;AAC3B,IAAI,aAAa,CAAC;AAClB,IAAI,cAAc,CAAC;AACnB,IAAI,aAAa,CAAC;AAClB,IAAI,gBAAgB,CAAC;AACrB,IAAI,WAAW,CAAC;AAChB,SAAS,WAAW,MAAM;AACxB,SAAO,CAAC,iBAAiB,QAAQ,IAAI;AACvC;AACA,SAAS,YAAY,WAAW,KAAK;AACnC,MAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,MAAI,SAAS,MAAM,CAAC;AACpB,MAAI,WAAW,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AACtC,MAAI,WAAW,aAAa,aAAa,MAAM,CAAC,WAAW,QAAQ,GAAG;AACpE,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAI,QAAQ,SAASC,SAAQ;AAC3B,MAAI,SAAS,SAASC,QAAO,SAAS;AACpC,WAAO,OAAO,QAAQ,SAAU,MAAM,OAAO,QAAQ;AACnD,WAAK,MAAM,IAAI,OAAO,OAAO,SAAS,CAAC,CAAC;AACxC,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AACA,eAAa,OAAO,SAAU,KAAKF,OAAM,UAAU;AACjD,QAAIA,MAAK,CAAC,GAAG;AACX,UAAIA,MAAK,CAAC,CAAC,IAAI;AAAA,IACjB;AACA,QAAIA,MAAK,CAAC,GAAG;AACX,UAAI,UAAUA,MAAK,CAAC,EAAE,OAAO,SAAU,MAAM;AAC3C,eAAO,OAAO,SAAS;AAAA,MACzB,CAAC;AACD,cAAQ,QAAQ,SAAU,OAAO;AAC/B,YAAI,MAAM,SAAS,EAAE,CAAC,IAAI;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC;AACD,gBAAc,OAAO,SAAU,KAAKA,OAAM,UAAU;AAClD,QAAI,QAAQ,IAAI;AAChB,QAAIA,MAAK,CAAC,GAAG;AACX,UAAI,UAAUA,MAAK,CAAC,EAAE,OAAO,SAAU,MAAM;AAC3C,eAAO,OAAO,SAAS;AAAA,MACzB,CAAC;AACD,cAAQ,QAAQ,SAAU,OAAO;AAC/B,YAAI,KAAK,IAAI;AAAA,MACf,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC;AACD,aAAW,OAAO,SAAU,KAAKA,OAAM,UAAU;AAC/C,QAAI,UAAUA,MAAK,CAAC;AACpB,QAAI,QAAQ,IAAI;AAChB,YAAQ,QAAQ,SAAU,OAAO;AAC/B,UAAI,KAAK,IAAI;AAAA,IACf,CAAC;AACD,WAAO;AAAA,EACT,CAAC;AAID,MAAI,aAAa,SAAS,UAAU,OAAO;AAC3C,MAAI,cAAc,OAAO,OAAO,SAAU,KAAK,MAAM;AACnD,QAAI,wBAAwB,KAAK,CAAC;AAClC,QAAI,SAAS,KAAK,CAAC;AACnB,QAAI,WAAW,KAAK,CAAC;AACrB,QAAI,WAAW,SAAS,CAAC,YAAY;AACnC,eAAS;AAAA,IACX;AACA,QAAI,OAAO,0BAA0B,UAAU;AAC7C,UAAI,MAAM,qBAAqB,IAAI;AAAA,QACjC;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,0BAA0B,UAAU;AAC7C,UAAI,SAAS,sBAAsB,SAAS,EAAE,CAAC,IAAI;AAAA,QACjD;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,IACR,UAAU,CAAC;AAAA,EACb,CAAC;AACD,eAAa,YAAY;AACzB,kBAAgB,YAAY;AAC5B,yBAAuB,mBAAmB,OAAO,cAAc;AAAA,IAC7D,QAAQ,OAAO;AAAA,EACjB,CAAC;AACH;AACA,SAAS,SAAU,MAAM;AACvB,yBAAuB,mBAAmB,KAAK,cAAc;AAAA,IAC3D,QAAQ,OAAO;AAAA,EACjB,CAAC;AACH,CAAC;AACD,MAAM;AACN,SAAS,UAAU,QAAQ,SAAS;AAClC,UAAQ,WAAW,MAAM,KAAK,CAAC,GAAG,OAAO;AAC3C;AACA,SAAS,WAAW,QAAQ,UAAU;AACpC,UAAQ,YAAY,MAAM,KAAK,CAAC,GAAG,QAAQ;AAC7C;AACA,SAAS,QAAQ,QAAQ,OAAO;AAC9B,UAAQ,SAAS,MAAM,KAAK,CAAC,GAAG,KAAK;AACvC;AACA,SAAS,UAAU,MAAM;AACvB,SAAO,WAAW,IAAI,KAAK;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AACF;AACA,SAAS,aAAa,SAAS;AAC7B,MAAI,aAAa,cAAc,OAAO;AACtC,MAAI,aAAa,UAAU,OAAO,OAAO;AACzC,SAAO,eAAe,aAAa;AAAA,IACjC,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ,IAAI,SAAS;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AACF;AACA,SAAS,yBAAyB;AAChC,SAAO;AACT;AACA,IAAI,qBAAqB,SAASG,sBAAqB;AACrD,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,MAAM,CAAC;AAAA,EACT;AACF;AACA,SAAS,YAAY,QAAQ;AAC3B,MAAI,SAAS;AACb,MAAI,WAAW,aAAa,OAAO,SAAU,KAAK,UAAU;AAC1D,QAAI,QAAQ,IAAI,GAAG,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,QAAQ;AAChE,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,KAAG,QAAQ,SAAU,UAAU;AAC7B,QAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,KAAK,OAAO,KAAK,SAAU,MAAM;AACrE,aAAO,oBAAoB,QAAQ,EAAE,SAAS,IAAI;AAAA,IACpD,CAAC,GAAG;AACF,eAAS;AAAA,IACX;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,mBAAmB,eAAe;AACzC,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,MAAI,iBAAiB,OAAO,QAC1B,SAAS,mBAAmB,SAAS,IAAI;AAC3C,MAAI,QAAQ,gBAAgB,MAAM,EAAE,aAAa;AAGjD,MAAI,WAAW,KAAK,CAAC,eAAe;AAClC,WAAO;AAAA,EACT;AACA,MAAI,SAAS,gBAAgB,MAAM,EAAE,aAAa,KAAK,gBAAgB,MAAM,EAAE,KAAK;AACpF,MAAI,UAAU,iBAAiB,UAAU,SAAS,gBAAgB;AAClE,MAAI,SAAS,UAAU,WAAW;AAClC,SAAO;AACT;AACA,SAAS,uBAAuB,YAAY;AAC1C,MAAI,OAAO,CAAC;AACZ,MAAI,WAAW;AACf,aAAW,QAAQ,SAAU,KAAK;AAChC,QAAI,SAAS,YAAY,OAAO,WAAW,GAAG;AAC9C,QAAI,QAAQ;AACV,iBAAW;AAAA,IACb,WAAW,KAAK;AACd,WAAK,KAAK,GAAG;AAAA,IACf;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,IAAI,KAAK,EAAE,OAAO,SAAU,OAAO,OAAOC,MAAK;AACpD,WAAOA,KAAI,QAAQ,KAAK,MAAM;AAAA,EAChC,CAAC;AACH;AACA,IAAI,qBAAqB,KAAK,OAAO,EAAE;AACvC,SAAS,iBAAiB,QAAQ;AAChC,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,MAAI,sBAAsB,OAAO,aAC/B,cAAc,wBAAwB,SAAS,QAAQ;AACzD,MAAI,cAAc;AAClB,MAAI,yBAAyB,mBAAmB,OAAO,OAAO,SAAU,KAAK;AAC3E,WAAO,mBAAmB,SAAS,GAAG;AAAA,EACxC,CAAC,CAAC;AACF,MAAI,0BAA0B,mBAAmB,OAAO,OAAO,SAAU,KAAK;AAC5E,WAAO,CAAC,mBAAmB,SAAS,GAAG;AAAA,EACzC,CAAC,CAAC;AACF,MAAI,WAAW,uBAAuB,OAAO,SAAU,KAAK;AAC1D,kBAAc;AACd,WAAO,CAAC,EAAE,SAAS,GAAG;AAAA,EACxB,CAAC;AACD,MAAI,YAAY,eAAe,UAAU,CAAC,GACxC,aAAa,UAAU,CAAC,GACxB,kBAAkB,eAAe,SAAS,OAAO;AACnD,MAAI,SAAS,YAAY,sBAAsB;AAC/C,MAAI,YAAY,eAAe,eAAe,CAAC,GAAG,uBAAuB,uBAAuB,CAAC,GAAG,CAAC,GAAG;AAAA,IACtG,QAAQ,mBAAmB,iBAAiB;AAAA,MAC1C;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO,eAAe,eAAe,eAAe,CAAC,GAAG,SAAS,GAAG,0BAA0B;AAAA,IAC5F;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC,GAAG,kBAAkB,aAAa,aAAa,SAAS,CAAC;AAC7D;AACA,SAAS,kBAAkB,aAAa,aAAa,WAAW;AAC9D,MAAI,SAAS,UAAU,QACrB,WAAW,UAAU;AACvB,MAAI,eAAe,CAAC,UAAU,CAAC,UAAU;AACvC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,MAAI,OAAO,gBAAgB,OAAO,UAAU,QAAQ,IAAI,CAAC;AACzD,MAAI,gBAAgB,QAAQ,QAAQ,QAAQ;AAC5C,aAAW,KAAK,YAAY,iBAAiB;AAC7C,WAAS,KAAK,UAAU;AACxB,MAAI,WAAW,SAAS,CAAC,OAAO,KAAK,KAAK,OAAO,KAAK,KAAK,CAAC,OAAO,cAAc;AAG/E,aAAS;AAAA,EACX;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI,uBAAuB,GAAG,OAAO,SAAU,UAAU;AACvD,SAAO,aAAa,KAAK,aAAa;AACxC,CAAC;AACD,IAAI,qBAAqB,OAAO,KAAK,IAAI,EAAE,OAAO,SAAU,KAAK;AAC/D,SAAO,QAAQ;AACjB,CAAC,EAAE,IAAI,SAAU,KAAK;AACpB,SAAO,OAAO,KAAK,KAAK,GAAG,CAAC;AAC9B,CAAC,EAAE,KAAK;AACR,SAAS,0BAA0B,eAAe;AAChD,MAAI,SAAS,cAAc,QACzB,SAAS,cAAc,QACvB,YAAY,cAAc,WAC1B,wBAAwB,cAAc,aACtC,cAAc,0BAA0B,SAAS,KAAK,uBACtD,wBAAwB,cAAc,QACtCV,UAAS,0BAA0B,SAAS,CAAC,IAAI,uBACjD,wBAAwB,cAAc,QACtC,YAAY,0BAA0B,SAAS,CAAC,IAAI;AACtD,MAAI,kBAAkB,WAAW;AACjC,MAAI,mBAAmB,OAAO,SAAS,YAAY,KAAK,OAAO,SAAS,KAAK;AAC7E,MAAI,yBAAyB,UAAU,kBAAkB;AACzD,MAAI,2BAA2B,UAAU,WAAW,SAAS,UAAU,WAAW;AAClF,MAAI,CAAC,oBAAoB,oBAAoB,0BAA0B,2BAA2B;AAChG,cAAU,SAAS;AAAA,EACrB;AACA,MAAI,OAAO,SAAS,WAAW,KAAK,OAAO,SAAS,KAAK,GAAG;AAC1D,cAAU,SAAS;AAAA,EACrB;AACA,MAAI,CAAC,UAAU,UAAU,qBAAqB,SAAS,MAAM,GAAG;AAC9D,QAAI,cAAc,OAAO,KAAKA,OAAM,EAAE,KAAK,SAAU,KAAK;AACxD,aAAO,mBAAmB,SAAS,GAAG;AAAA,IACxC,CAAC;AACD,QAAI,eAAe,UAAU,cAAc;AACzC,UAAI,gBAAgB,GAAG,IAAI,MAAM,EAAE;AACnC,gBAAU,SAAS;AACnB,gBAAU,WAAW,QAAQ,UAAU,QAAQ,UAAU,QAAQ,KAAK,UAAU;AAAA,IAClF;AAAA,EACF;AACA,MAAI,UAAU,WAAW,QAAQ,gBAAgB,MAAM;AAGrD,cAAU,SAAS,uBAAuB,KAAK;AAAA,EACjD;AACA,SAAO;AACT;AAEA,IAAI,UAAuB,WAAY;AACrC,WAASW,WAAU;AACjB,oBAAgB,MAAMA,QAAO;AAC7B,SAAK,cAAc,CAAC;AAAA,EACtB;AACA,SAAO,aAAaA,UAAS,CAAC;AAAA,IAC5B,KAAK;AAAA,IACL,OAAO,SAAS,MAAM;AACpB,UAAI,QAAQ;AACZ,eAAS,OAAO,UAAU,QAAQ,cAAc,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC9F,oBAAY,IAAI,IAAI,UAAU,IAAI;AAAA,MACpC;AACA,UAAI,YAAY,YAAY,OAAO,KAAK,kBAAkB,CAAC,CAAC;AAC5D,aAAO,KAAK,SAAS,EAAE,QAAQ,SAAU,KAAK;AAC5C,cAAM,YAAY,GAAG,IAAI,eAAe,eAAe,CAAC,GAAG,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,GAAG,UAAU,GAAG,CAAC;AACxG,oBAAY,KAAK,UAAU,GAAG,CAAC;AAC/B,cAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,WAAK,cAAc,CAAC;AAAA,IACtB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB,WAAW,YAAY;AACtD,UAAI,aAAa,WAAW,UAAU,WAAW,YAAY,WAAW,OAAO;AAAA,QAC7E,GAAG;AAAA,MACL,IAAI;AACJ,aAAO,KAAK,UAAU,EAAE,IAAI,SAAU,KAAK;AACzC,YAAI,kBAAkB,WAAW,GAAG,GAClC,SAAS,gBAAgB,QACzB,WAAW,gBAAgB,UAC3BL,QAAO,gBAAgB;AACzB,YAAI,UAAUA,MAAK,CAAC;AACpB,YAAI,CAAC,UAAU,MAAM,EAAG,WAAU,MAAM,IAAI,CAAC;AAC7C,YAAI,QAAQ,SAAS,GAAG;AACtB,kBAAQ,QAAQ,SAAU,OAAO;AAC/B,gBAAI,OAAO,UAAU,UAAU;AAC7B,wBAAU,MAAM,EAAE,KAAK,IAAIA;AAAA,YAC7B;AAAA,UACF,CAAC;AAAA,QACH;AACA,kBAAU,MAAM,EAAE,QAAQ,IAAIA;AAAA,MAChC,CAAC;AACD,aAAO;AAAA,IACT;AAAA,EACF,CAAC,CAAC;AACJ,EAAE;AAEF,IAAI,WAAW,CAAC;AAChB,IAAI,SAAS,CAAC;AACd,IAAI,YAAY,CAAC;AACjB,IAAI,sBAAsB,OAAO,KAAK,SAAS;AAC/C,SAAS,gBAAgB,aAAaV,OAAM;AAC1C,MAAI,MAAMA,MAAK;AACf,aAAW;AACX,WAAS,CAAC;AACV,SAAO,KAAK,SAAS,EAAE,QAAQ,SAAUgB,IAAG;AAC1C,QAAI,oBAAoB,QAAQA,EAAC,MAAM,IAAI;AACzC,aAAO,UAAUA,EAAC;AAAA,IACpB;AAAA,EACF,CAAC;AACD,WAAS,QAAQ,SAAU,QAAQ;AACjC,QAAIC,UAAS,OAAO,SAAS,OAAO,OAAO,IAAI,CAAC;AAChD,WAAO,KAAKA,OAAM,EAAE,QAAQ,SAAU,IAAI;AACxC,UAAI,OAAOA,QAAO,EAAE,MAAM,YAAY;AACpC,YAAI,EAAE,IAAIA,QAAO,EAAE;AAAA,MACrB;AACA,UAAI,QAAQA,QAAO,EAAE,CAAC,MAAM,UAAU;AACpC,eAAO,KAAKA,QAAO,EAAE,CAAC,EAAE,QAAQ,SAAU,IAAI;AAC5C,cAAI,CAAC,IAAI,EAAE,GAAG;AACZ,gBAAI,EAAE,IAAI,CAAC;AAAA,UACb;AACA,cAAI,EAAE,EAAE,EAAE,IAAIA,QAAO,EAAE,EAAE,EAAE;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,QAAI,OAAO,OAAO;AAChB,UAAIC,SAAQ,OAAO,MAAM;AACzB,aAAO,KAAKA,MAAK,EAAE,QAAQ,SAAU,MAAM;AACzC,YAAI,CAAC,OAAO,IAAI,GAAG;AACjB,iBAAO,IAAI,IAAI,CAAC;AAAA,QAClB;AACA,eAAO,IAAI,EAAE,KAAKA,OAAM,IAAI,CAAC;AAAA,MAC/B,CAAC;AAAA,IACH;AACA,QAAI,OAAO,UAAU;AACnB,aAAO,SAAS,SAAS;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,WAAW,MAAM,aAAa;AACrC,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,SAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACjC;AACA,MAAI,UAAU,OAAO,IAAI,KAAK,CAAC;AAC/B,UAAQ,QAAQ,SAAU,QAAQ;AAChC,kBAAc,OAAO,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC;AAAA,EAC7D,CAAC;AACD,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,WAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,SAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,EACnC;AACA,MAAI,UAAU,OAAO,IAAI,KAAK,CAAC;AAC/B,UAAQ,QAAQ,SAAU,QAAQ;AAChC,WAAO,MAAM,MAAM,IAAI;AAAA,EACzB,CAAC;AACD,SAAO;AACT;AACA,SAAS,eAAe;AACtB,MAAI,OAAO,UAAU,CAAC;AACtB,MAAI,OAAO,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAClD,SAAO,UAAU,IAAI,IAAI,UAAU,IAAI,EAAE,MAAM,MAAM,IAAI,IAAI;AAC/D;AAEA,SAAS,mBAAmB,YAAY;AACtC,MAAI,WAAW,WAAW,MAAM;AAC9B,eAAW,SAAS;AAAA,EACtB;AACA,MAAI,WAAW,WAAW;AAC1B,MAAI,SAAS,WAAW,UAAU,uBAAuB;AACzD,MAAI,CAAC,SAAU;AACf,aAAW,QAAQ,QAAQ,QAAQ,KAAK;AACxC,SAAO,gBAAgB,QAAQ,aAAa,QAAQ,QAAQ,KAAK,gBAAgB,UAAU,QAAQ,QAAQ,QAAQ;AACrH;AACA,IAAI,UAAU,IAAI,QAAQ;AAC1B,IAAI,SAAS,SAASC,UAAS;AAC7B,SAAO,iBAAiB;AACxB,SAAO,mBAAmB;AAC1B,YAAU,QAAQ;AACpB;AACA,IAAI,MAAM;AAAA,EACR,OAAO,SAAS,QAAQ;AACtB,QAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAI,QAAQ;AACV,gBAAU,eAAe,MAAM;AAC/B,mBAAa,sBAAsB,MAAM;AACzC,aAAO,aAAa,SAAS,MAAM;AAAA,IACrC,OAAO;AACL,aAAO,QAAQ,OAAO,IAAI,MAAM,wCAAwC,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ;AACtB,QAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAI,qBAAqB,OAAO;AAChC,QAAI,OAAO,mBAAmB,OAAO;AACnC,aAAO,iBAAiB;AAAA,IAC1B;AACA,WAAO,mBAAmB;AAC1B,aAAS,WAAY;AACnB,kBAAY;AAAA,QACV;AAAA,MACF,CAAC;AACD,gBAAU,SAAS,MAAM;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AACA,IAAI,QAAQ;AAAA,EACV,MAAM,SAAS,KAAK,OAAO;AACzB,QAAI,UAAU,MAAM;AAClB,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,KAAK,MAAM,YAAY,MAAM,UAAU,MAAM,UAAU;AACjE,aAAO;AAAA,QACL,QAAQ,MAAM;AAAA,QACd,UAAU,QAAQ,MAAM,QAAQ,MAAM,QAAQ,KAAK,MAAM;AAAA,MAC3D;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW,GAAG;AAC9C,UAAI,WAAW,MAAM,CAAC,EAAE,QAAQ,KAAK,MAAM,IAAI,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC;AAC1E,UAAI,SAAS,mBAAmB,MAAM,CAAC,CAAC;AACxC,aAAO;AAAA,QACL;AAAA,QACA,UAAU,QAAQ,QAAQ,QAAQ,KAAK;AAAA,MACzC;AAAA,IACF;AACA,QAAI,OAAO,UAAU,aAAa,MAAM,QAAQ,GAAG,OAAO,OAAO,WAAW,GAAG,CAAC,IAAI,MAAM,MAAM,MAAM,6BAA6B,IAAI;AACrI,UAAI,gBAAgB,iBAAiB,MAAM,MAAM,GAAG,GAAG;AAAA,QACrD,aAAa;AAAA,MACf,CAAC;AACD,aAAO;AAAA,QACL,QAAQ,cAAc,UAAU,uBAAuB;AAAA,QACvD,UAAU,QAAQ,cAAc,QAAQ,cAAc,QAAQ,KAAK,cAAc;AAAA,MACnF;AAAA,IACF;AACA,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,UAAU,uBAAuB;AACrC,aAAO;AAAA,QACL,QAAQ;AAAA,QACR,UAAU,QAAQ,SAAS,KAAK,KAAK;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,MAAM;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,cAAc,SAASC,eAAc;AACvC,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,MAAI,wBAAwB,OAAO,oBACjC,qBAAqB,0BAA0B,SAAS,WAAW;AACrE,OAAK,OAAO,KAAK,UAAU,MAAM,EAAE,SAAS,KAAK,OAAO,iBAAiB,UAAU,OAAO,eAAgB,KAAI,IAAI,MAAM;AAAA,IACtH,MAAM;AAAA,EACR,CAAC;AACH;AAEA,SAAS,YAAY,KAAK,iBAAiB;AACzC,SAAO,eAAe,KAAK,YAAY;AAAA,IACrC,KAAK;AAAA,EACP,CAAC;AACD,SAAO,eAAe,KAAK,QAAQ;AAAA,IACjC,KAAK,SAASrB,OAAM;AAClB,aAAO,IAAI,SAAS,IAAI,SAAUZ,IAAG;AACnC,eAAO,OAAOA,EAAC;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAO,eAAe,KAAK,QAAQ;AAAA,IACjC,KAAK,SAASY,OAAM;AAClB,UAAI,CAAC,OAAQ,QAAO;AACpB,UAAI,YAAY,SAAS,cAAc,KAAK;AAC5C,gBAAU,YAAY,IAAI;AAC1B,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,SAAS,OAAQC,OAAM;AACrB,MAAI,WAAWA,MAAK,UAClB,OAAOA,MAAK,MACZ,OAAOA,MAAK,MACZ,aAAaA,MAAK,YAClBI,UAASJ,MAAK,QACd,YAAYA,MAAK;AACnB,MAAI,sBAAsB,SAAS,KAAK,KAAK,SAAS,CAAC,KAAK,OAAO;AACjE,QAAI,QAAQ,KAAK,OACf,SAAS,KAAK;AAChB,QAAI,SAAS;AAAA,MACX,GAAG,QAAQ,SAAS;AAAA,MACpB,GAAG;AAAA,IACL;AACA,eAAW,OAAO,IAAI,WAAW,eAAe,eAAe,CAAC,GAAGI,OAAM,GAAG,CAAC,GAAG;AAAA,MAC9E,oBAAoB,GAAG,OAAO,OAAO,IAAI,UAAU,IAAI,IAAI,KAAK,EAAE,OAAO,OAAO,IAAI,UAAU,IAAI,IAAI,IAAI;AAAA,IAC5G,CAAC,CAAC;AAAA,EACJ;AACA,SAAO,CAAC;AAAA,IACN,KAAK;AAAA,IACL;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,SAAS,SAAUJ,OAAM;AACvB,MAAI,SAASA,MAAK,QAChB,WAAWA,MAAK,UAChB,WAAWA,MAAK,UAChB,aAAaA,MAAK,YAClB,SAASA,MAAK;AAChB,MAAI,KAAK,WAAW,OAAO,GAAG,OAAO,QAAQ,GAAG,EAAE,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,QAAQ,IAAI;AACnG,SAAO,CAAC;AAAA,IACN,KAAK;AAAA,IACL,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU,CAAC;AAAA,MACT,KAAK;AAAA,MACL,YAAY,eAAe,eAAe,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG;AAAA,QAC7D;AAAA,MACF,CAAC;AAAA,MACD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAGA,SAAS,UAAU,YAAY;AAC7B,MAAI,SAAS,CAAC,cAAc,mBAAmB,SAAS,MAAM;AAC9D,SAAO,OAAO,KAAK,SAAU,OAAO;AAClC,WAAO,SAAS;AAAA,EAClB,CAAC;AACH;AACA,SAAS,sBAAsB,QAAQ;AACrC,MAAI,gBAAgB,OAAO,OACzB,OAAO,cAAc,MACrB,OAAO,cAAc,MACrB,SAAS,OAAO,QAChB,WAAW,OAAO,UAClB,YAAY,OAAO,WACnB,SAAS,OAAO,QAChB,SAAS,OAAO,QAChB,QAAQ,OAAO,OACf,oBAAoB,OAAO,WAC3B,YAAY,sBAAsB,SAAS,QAAQ;AACrD,MAAIA,QAAO,KAAK,QAAQ,OAAO,MAC7B,QAAQA,MAAK,OACb,SAASA,MAAK;AAChB,MAAI,YAAY,CAAC,OAAO,kBAAkB,WAAW,GAAG,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,QAAQ,IAAI,EAAE,EAAE,OAAO,SAAUQ,IAAG;AAC/H,WAAO,MAAM,QAAQ,QAAQA,EAAC,MAAM;AAAA,EACtC,CAAC,EAAE,OAAO,SAAUA,IAAG;AACrB,WAAOA,OAAM,MAAM,CAAC,CAACA;AAAA,EACvB,CAAC,EAAE,OAAO,MAAM,OAAO,EAAE,KAAK,GAAG;AACjC,MAAI,UAAU;AAAA,IACZ,UAAU,CAAC;AAAA,IACX,YAAY,eAAe,eAAe,CAAC,GAAG,MAAM,UAAU,GAAG,CAAC,GAAG;AAAA,MACnE,eAAe;AAAA,MACf,aAAa;AAAA,MACb,SAAS;AAAA,MACT,QAAQ,MAAM,WAAW,QAAQ;AAAA,MACjC,WAAW,OAAO,OAAO,OAAO,GAAG,EAAE,OAAO,MAAM;AAAA,IACpD,CAAC;AAAA,EACH;AACA,MAAI,CAAC,UAAU,MAAM,UAAU,KAAK,CAAC,MAAM,WAAW,aAAa,GAAG;AACpE,YAAQ,WAAW,aAAa,IAAI;AAAA,EACtC;AACA,MAAI,WAAW;AACb,YAAQ,WAAW,aAAa,IAAI;AAAA,EACtC;AACA,MAAI,OAAO,eAAe,eAAe,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG;AAAA,IACzD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,eAAe,CAAC,GAAG,MAAM,MAAM;AAAA,EACzC,CAAC;AACD,MAAIP,SAAQ,KAAK,SAAS,KAAK,QAAQ,aAAa,wBAAwB,IAAI,KAAK;AAAA,IACjF,UAAU,CAAC;AAAA,IACX,YAAY,CAAC;AAAA,EACf,IAAI,aAAa,wBAAwB,IAAI,KAAK;AAAA,IAChD,UAAU,CAAC;AAAA,IACX,YAAY,CAAC;AAAA,EACf,GACA,WAAWA,OAAM,UACjB,aAAaA,OAAM;AACrB,OAAK,WAAW;AAChB,OAAK,aAAa;AAClB,MAAI,QAAQ;AACV,WAAO,SAAS,IAAI;AAAA,EACtB,OAAO;AACL,WAAO,OAAO,IAAI;AAAA,EACpB;AACF;AACA,SAAS,uBAAuB,QAAQ;AACtC,MAAI,UAAU,OAAO,SACnB,QAAQ,OAAO,OACf,SAAS,OAAO,QAChB,YAAY,OAAO,WACnB,QAAQ,OAAO,OACf,qBAAqB,OAAO,WAC5B,YAAY,uBAAuB,SAAS,QAAQ;AACtD,MAAI,aAAa,eAAe,eAAe,CAAC,GAAG,MAAM,UAAU,GAAG,CAAC,GAAG;AAAA,IACxE,OAAO,MAAM,QAAQ,KAAK,GAAG;AAAA,EAC/B,CAAC;AACD,MAAI,WAAW;AACb,eAAW,aAAa,IAAI;AAAA,EAC9B;AACA,MAAIG,UAAS,eAAe,CAAC,GAAG,MAAM,MAAM;AAC5C,MAAI,sBAAsB,SAAS,GAAG;AACpC,IAAAA,QAAO,WAAW,IAAI,gBAAgB;AAAA,MACpC;AAAA,MACA,eAAe;AAAA,MACf;AAAA,MACA;AAAA,IACF,CAAC;AACD,IAAAA,QAAO,mBAAmB,IAAIA,QAAO,WAAW;AAAA,EAClD;AACA,MAAI,cAAc,WAAWA,OAAM;AACnC,MAAI,YAAY,SAAS,GAAG;AAC1B,eAAW,OAAO,IAAI;AAAA,EACxB;AACA,MAAI,MAAM,CAAC;AACX,MAAI,KAAK;AAAA,IACP,KAAK;AAAA,IACL;AAAA,IACA,UAAU,CAAC,OAAO;AAAA,EACpB,CAAC;AACD,SAAO;AACT;AACA,SAAS,0BAA0B,QAAQ;AACzC,MAAI,UAAU,OAAO,SACnB,QAAQ,OAAO;AACjB,MAAI,aAAa,eAAe,eAAe,CAAC,GAAG,MAAM,UAAU,GAAG,CAAC,GAAG;AAAA,IACxE,OAAO,MAAM,QAAQ,KAAK,GAAG;AAAA,EAC/B,CAAC;AACD,MAAI,cAAc,WAAW,MAAM,MAAM;AACzC,MAAI,YAAY,SAAS,GAAG;AAC1B,eAAW,OAAO,IAAI;AAAA,EACxB;AACA,MAAI,MAAM,CAAC;AACX,MAAI,KAAK;AAAA,IACP,KAAK;AAAA,IACL;AAAA,IACA,UAAU,CAAC,OAAO;AAAA,EACpB,CAAC;AACD,SAAO;AACT;AAEA,IAAI,WAAW,UAAU;AACzB,SAAS,YAAYM,OAAM;AACzB,MAAI,QAAQA,MAAK,CAAC;AAClB,MAAI,SAASA,MAAK,CAAC;AACnB,MAAI,cAAcA,MAAK,MAAM,CAAC,GAC5B,eAAe,eAAe,aAAa,CAAC,GAC5C,aAAa,aAAa,CAAC;AAC7B,MAAI,UAAU;AACd,MAAI,MAAM,QAAQ,UAAU,GAAG;AAC7B,cAAU;AAAA,MACR,KAAK;AAAA,MACL,YAAY;AAAA,QACV,OAAO,GAAG,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,gBAAgB,KAAK;AAAA,MACtE;AAAA,MACA,UAAU,CAAC;AAAA,QACT,KAAK;AAAA,QACL,YAAY;AAAA,UACV,OAAO,GAAG,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,gBAAgB,SAAS;AAAA,UACxE,MAAM;AAAA,UACN,GAAG,WAAW,CAAC;AAAA,QACjB;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,YAAY;AAAA,UACV,OAAO,GAAG,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,gBAAgB,OAAO;AAAA,UACtE,MAAM;AAAA,UACN,GAAG,WAAW,CAAC;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,OAAO;AACL,cAAU;AAAA,MACR,KAAK;AAAA,MACL,YAAY;AAAA,QACV,MAAM;AAAA,QACN,GAAG;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR;AACF;AACA,IAAI,6BAA6B;AAAA,EAC/B,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AACV;AACA,SAAS,mBAAmB,UAAU,QAAQ;AAC5C,MAAI,CAAC,cAAc,CAAC,OAAO,oBAAoB,UAAU;AACvD,YAAQ,MAAM,mBAAoB,OAAO,UAAU,gBAAkB,EAAE,OAAO,QAAQ,eAAgB,CAAC;AAAA,EACzG;AACF;AACA,SAAS,SAAS,UAAU,QAAQ;AAClC,MAAI,cAAc;AAClB,MAAI,WAAW,QAAQ,OAAO,iBAAiB,MAAM;AACnD,aAAS,uBAAuB;AAAA,EAClC;AACA,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,QAAI,gBAAgB,MAAM;AACxB,UAAI,OAAO,UAAU,QAAQ,KAAK,CAAC;AACnC,iBAAW,KAAK,YAAY;AAC5B,eAAS,KAAK,UAAU;AAAA,IAC1B;AACA,QAAI,YAAY,UAAU,SAAS,MAAM,KAAK,SAAS,MAAM,EAAE,QAAQ,GAAG;AACxE,UAAIA,QAAO,SAAS,MAAM,EAAE,QAAQ;AACpC,aAAO,QAAQ,YAAYA,KAAI,CAAC;AAAA,IAClC;AACA,uBAAmB,UAAU,MAAM;AACnC,YAAQ,eAAe,eAAe,CAAC,GAAG,0BAA0B,GAAG,CAAC,GAAG;AAAA,MACzE,MAAM,OAAO,oBAAoB,WAAW,aAAa,qBAAqB,KAAK,CAAC,IAAI,CAAC;AAAA,IAC3F,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;AAEA,IAAI,SAAS,SAASZ,QAAO;AAAC;AAC9B,IAAI,MAAM,OAAO,sBAAsB,eAAe,YAAY,QAAQ,YAAY,UAAU,cAAc;AAAA,EAC5G,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAI,WAAW;AACf,IAAI,QAAQ,SAASuB,OAAM,MAAM;AAC/B,MAAI,KAAK,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,MAAM,SAAS,CAAC;AACzD,SAAO,WAAY;AACjB,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;AACA,IAAI,MAAM,SAASC,KAAI,MAAM;AAC3B,MAAI,KAAK,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,MAAM,OAAO,CAAC;AACvD,MAAI,QAAQ,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,IAAI,GAAG,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,MAAM,SAAS,GAAG,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,MAAM,OAAO,CAAC;AACrJ;AACA,IAAI,OAAO;AAAA,EACT;AAAA,EACA;AACF;AAEA,IAAI,SAAS,SAASxB,QAAO;AAAC;AAC9B,SAAS,UAAU,MAAM;AACvB,MAAIyB,SAAQ,KAAK,eAAe,KAAK,aAAa,aAAa,IAAI;AACnE,SAAO,OAAOA,WAAU;AAC1B;AACA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,SAAS,KAAK,eAAe,KAAK,aAAa,WAAW,IAAI;AAClE,MAAIb,QAAO,KAAK,eAAe,KAAK,aAAa,SAAS,IAAI;AAC9D,SAAO,UAAUA;AACnB;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,QAAQ,KAAK,aAAa,KAAK,UAAU,YAAY,KAAK,UAAU,SAAS,OAAO,gBAAgB;AAC7G;AACA,SAAS,aAAa;AACpB,MAAI,OAAO,mBAAmB,MAAM;AAClC,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,UAAU,SAAS,OAAO,cAAc;AAC5C,SAAO,WAAW,SAAS;AAC7B;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,SAAS,gBAAgB,8BAA8B,GAAG;AACnE;AACA,SAAS,cAAc,KAAK;AAC1B,SAAO,SAAS,cAAc,GAAG;AACnC;AACA,SAAS,WAAW,aAAa;AAC/B,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,MAAI,eAAe,OAAO,MACxB,OAAO,iBAAiB,SAAS,YAAY,QAAQ,QAAQ,kBAAkB,gBAAgB;AACjG,MAAI,OAAO,gBAAgB,UAAU;AACnC,WAAO,SAAS,eAAe,WAAW;AAAA,EAC5C;AACA,MAAI,MAAM,KAAK,YAAY,GAAG;AAC9B,SAAO,KAAK,YAAY,cAAc,CAAC,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC/D,QAAI,aAAa,KAAK,YAAY,WAAW,GAAG,CAAC;AAAA,EACnD,CAAC;AACD,MAAI,WAAW,YAAY,YAAY,CAAC;AACxC,WAAS,QAAQ,SAAU,OAAO;AAChC,QAAI,YAAY,WAAW,OAAO;AAAA,MAChC;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,CAAC;AACD,SAAO;AACT;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,UAAU,IAAI,OAAO,KAAK,WAAW,GAAG;AAE5C,YAAU,GAAG,OAAO,SAAS,+BAA+B;AAE5D,SAAO;AACT;AACA,IAAI,WAAW;AAAA,EACb,SAAS,SAAS,QAAQ,UAAU;AAClC,QAAI,OAAO,SAAS,CAAC;AACrB,QAAI,KAAK,YAAY;AACnB,eAAS,CAAC,EAAE,QAAQ,SAAU,UAAU;AACtC,aAAK,WAAW,aAAa,WAAW,QAAQ,GAAG,IAAI;AAAA,MACzD,CAAC;AACD,UAAI,KAAK,aAAa,aAAa,MAAM,QAAQ,OAAO,oBAAoB;AAC1E,YAAI,UAAU,SAAS,cAAc,cAAc,IAAI,CAAC;AACxD,aAAK,WAAW,aAAa,SAAS,IAAI;AAAA,MAC5C,OAAO;AACL,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,SAAS,KAAK,UAAU;AAC5B,QAAI,OAAO,SAAS,CAAC;AACrB,QAAI,WAAW,SAAS,CAAC;AAIzB,QAAI,CAAC,WAAW,IAAI,EAAE,QAAQ,OAAO,gBAAgB,GAAG;AACtD,aAAO,SAAS,QAAQ,QAAQ;AAAA,IAClC;AACA,QAAI,SAAS,IAAI,OAAO,GAAG,OAAO,OAAO,WAAW,KAAK,CAAC;AAC1D,WAAO,SAAS,CAAC,EAAE,WAAW;AAC9B,QAAI,SAAS,CAAC,EAAE,WAAW,OAAO;AAChC,UAAI,eAAe,SAAS,CAAC,EAAE,WAAW,MAAM,MAAM,GAAG,EAAE,OAAO,SAAU,KAAK,KAAK;AACpF,YAAI,QAAQ,OAAO,oBAAoB,IAAI,MAAM,MAAM,GAAG;AACxD,cAAI,MAAM,KAAK,GAAG;AAAA,QACpB,OAAO;AACL,cAAI,OAAO,KAAK,GAAG;AAAA,QACrB;AACA,eAAO;AAAA,MACT,GAAG;AAAA,QACD,QAAQ,CAAC;AAAA,QACT,OAAO,CAAC;AAAA,MACV,CAAC;AACD,eAAS,CAAC,EAAE,WAAW,QAAQ,aAAa,MAAM,KAAK,GAAG;AAC1D,UAAI,aAAa,OAAO,WAAW,GAAG;AACpC,aAAK,gBAAgB,OAAO;AAAA,MAC9B,OAAO;AACL,aAAK,aAAa,SAAS,aAAa,OAAO,KAAK,GAAG,CAAC;AAAA,MAC1D;AAAA,IACF;AACA,QAAI,eAAe,SAAS,IAAI,SAAUvB,IAAG;AAC3C,aAAO,OAAOA,EAAC;AAAA,IACjB,CAAC,EAAE,KAAK,IAAI;AACZ,SAAK,aAAa,eAAe,EAAE;AACnC,SAAK,YAAY;AAAA,EACnB;AACF;AACA,SAAS,qBAAqB,IAAI;AAChC,KAAG;AACL;AACA,SAAS,QAAQ,WAAW,UAAU;AACpC,MAAI,mBAAmB,OAAO,aAAa,aAAa,WAAW;AACnE,MAAI,UAAU,WAAW,GAAG;AAC1B,qBAAiB;AAAA,EACnB,OAAO;AACL,QAAI,QAAQ;AACZ,QAAI,OAAO,mBAAmB,yBAAyB;AACrD,cAAQ,OAAO,yBAAyB;AAAA,IAC1C;AACA,UAAM,WAAY;AAChB,UAAI,UAAU,WAAW;AACzB,UAAI,OAAO,KAAK,MAAM,QAAQ;AAC9B,gBAAU,IAAI,OAAO;AACrB,WAAK;AACL,uBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,IAAI,WAAW;AACf,SAAS,qBAAqB;AAC5B,aAAW;AACb;AACA,SAAS,oBAAoB;AAC3B,aAAW;AACb;AACA,IAAI,KAAK;AACT,SAAS,QAAQ,SAAS;AACxB,MAAI,CAAC,mBAAmB;AACtB;AAAA,EACF;AACA,MAAI,CAAC,OAAO,kBAAkB;AAC5B;AAAA,EACF;AACA,MAAI,wBAAwB,QAAQ,cAClC,eAAe,0BAA0B,SAAS,SAAS,uBAC3D,wBAAwB,QAAQ,cAChC,eAAe,0BAA0B,SAAS,SAAS,uBAC3D,wBAAwB,QAAQ,wBAChC,yBAAyB,0BAA0B,SAAS,SAAS,uBACrE,wBAAwB,QAAQ,sBAChC,uBAAuB,0BAA0B,SAAS,WAAW;AACvE,OAAK,IAAI,kBAAkB,SAAU,SAAS;AAC5C,QAAI,SAAU;AACd,QAAI,gBAAgB,uBAAuB;AAC3C,YAAQ,OAAO,EAAE,QAAQ,SAAU,gBAAgB;AACjD,UAAI,eAAe,SAAS,eAAe,eAAe,WAAW,SAAS,KAAK,CAAC,UAAU,eAAe,WAAW,CAAC,CAAC,GAAG;AAC3H,YAAI,OAAO,sBAAsB;AAC/B,iCAAuB,eAAe,MAAM;AAAA,QAC9C;AACA,qBAAa,eAAe,MAAM;AAAA,MACpC;AACA,UAAI,eAAe,SAAS,gBAAgB,eAAe,OAAO,cAAc,OAAO,sBAAsB;AAC3G,+BAAuB,CAAC,eAAe,MAAM,GAAG,IAAI;AAAA,MACtD;AACA,UAAI,eAAe,SAAS,gBAAgB,UAAU,eAAe,MAAM,KAAK,CAAC,gCAAgC,QAAQ,eAAe,aAAa,GAAG;AACtJ,YAAI,eAAe,kBAAkB,WAAW,iBAAiB,eAAe,MAAM,GAAG;AACvF,cAAI,oBAAoB,iBAAiB,WAAW,eAAe,MAAM,CAAC,GACxE,SAAS,kBAAkB,QAC3B,WAAW,kBAAkB;AAC/B,yBAAe,OAAO,aAAa,aAAa,UAAU,aAAa;AACvE,cAAI,SAAU,gBAAe,OAAO,aAAa,WAAW,QAAQ;AAAA,QACtE,WAAW,gBAAgB,eAAe,MAAM,GAAG;AACjD,uBAAa,eAAe,MAAM;AAAA,QACpC;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,MAAI,CAAC,OAAQ;AACb,KAAG,QAAQ,sBAAsB;AAAA,IAC/B,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,SAAS;AAAA,EACX,CAAC;AACH;AACA,SAAS,aAAa;AACpB,MAAI,CAAC,GAAI;AACT,KAAG,WAAW;AAChB;AAEA,SAAS,YAAa,MAAM;AAC1B,MAAI,QAAQ,KAAK,aAAa,OAAO;AACrC,MAAI,MAAM,CAAC;AACX,MAAI,OAAO;AACT,UAAM,MAAM,MAAM,GAAG,EAAE,OAAO,SAAU,KAAKqC,QAAO;AAClD,UAAIpB,UAASoB,OAAM,MAAM,GAAG;AAC5B,UAAI,OAAOpB,QAAO,CAAC;AACnB,UAAI,QAAQA,QAAO,MAAM,CAAC;AAC1B,UAAI,QAAQ,MAAM,SAAS,GAAG;AAC5B,YAAI,IAAI,IAAI,MAAM,KAAK,GAAG,EAAE,KAAK;AAAA,MACnC;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AACA,SAAO;AACT;AAEA,SAAS,YAAa,MAAM;AAC1B,MAAI,iBAAiB,KAAK,aAAa,aAAa;AACpD,MAAI,mBAAmB,KAAK,aAAa,WAAW;AACpD,MAAI,YAAY,KAAK,cAAc,SAAY,KAAK,UAAU,KAAK,IAAI;AACvE,MAAI,MAAM,iBAAiB,WAAW,IAAI,CAAC;AAC3C,MAAI,CAAC,IAAI,QAAQ;AACf,QAAI,SAAS,uBAAuB;AAAA,EACtC;AACA,MAAI,kBAAkB,kBAAkB;AACtC,QAAI,SAAS;AACb,QAAI,WAAW;AAAA,EACjB;AACA,MAAI,IAAI,YAAY,IAAI,QAAQ;AAC9B,WAAO;AAAA,EACT;AACA,MAAI,IAAI,UAAU,UAAU,SAAS,GAAG;AACtC,QAAI,WAAW,WAAW,IAAI,QAAQ,KAAK,SAAS,KAAK,UAAU,IAAI,QAAQ,MAAM,KAAK,SAAS,CAAC;AAAA,EACtG;AACA,MAAI,CAAC,IAAI,YAAY,OAAO,gBAAgB,KAAK,cAAc,KAAK,WAAW,aAAa,KAAK,WAAW;AAC1G,QAAI,WAAW,KAAK,WAAW;AAAA,EACjC;AACA,SAAO;AACT;AAEA,SAAS,iBAAkB,MAAM;AAC/B,MAAI,kBAAkB,QAAQ,KAAK,UAAU,EAAE,OAAO,SAAU,KAAK,MAAM;AACzE,QAAI,IAAI,SAAS,WAAW,IAAI,SAAS,SAAS;AAChD,UAAI,KAAK,IAAI,IAAI,KAAK;AAAA,IACxB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO;AACT;AAEA,SAAS,YAAY;AACnB,SAAO;AAAA,IACL,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,MAAM;AAAA,MACJ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM,CAAC;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,IACR,OAAO;AAAA,MACL,SAAS,CAAC;AAAA,MACV,QAAQ,CAAC;AAAA,MACT,YAAY,CAAC;AAAA,IACf;AAAA,EACF;AACF;AACA,SAAS,UAAU,MAAM;AACvB,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,IAC/E,aAAa;AAAA,EACf;AACA,MAAI,eAAe,YAAY,IAAI,GACjC,WAAW,aAAa,UACxB,SAAS,aAAa,QACtB,eAAe,aAAa;AAC9B,MAAI,kBAAkB,iBAAiB,IAAI;AAC3C,MAAI,aAAa,WAAW,uBAAuB,CAAC,GAAG,IAAI;AAC3D,MAAI,cAAc,OAAO,cAAc,YAAY,IAAI,IAAI,CAAC;AAC5D,SAAO,eAAe;AAAA,IACpB;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX,MAAM;AAAA,MACJ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM,CAAC;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,YAAY;AAAA,IACd;AAAA,EACF,GAAG,UAAU;AACf;AAEA,IAAI,WAAW,UAAU;AACzB,SAAS,iBAAiB,MAAM;AAC9B,MAAI,WAAW,OAAO,mBAAmB,SAAS,UAAU,MAAM;AAAA,IAChE,aAAa;AAAA,EACf,CAAC,IAAI,UAAU,IAAI;AACnB,MAAI,CAAC,SAAS,MAAM,QAAQ,QAAQ,qBAAqB,GAAG;AAC1D,WAAO,aAAa,sBAAsB,MAAM,QAAQ;AAAA,EAC1D,OAAO;AACL,WAAO,aAAa,kCAAkC,MAAM,QAAQ;AAAA,EACtE;AACF;AACA,SAAS,mBAAmB;AAC1B,SAAO,CAAC,EAAE,OAAO,mBAAmB,EAAE,GAAG,mBAAmB,IAAI,CAAC;AACnE;AACA,SAAS,OAAO,MAAM;AACpB,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,MAAI,CAAC,OAAQ,QAAO,QAAQ,QAAQ;AACpC,MAAI,gBAAgB,SAAS,gBAAgB;AAC7C,MAAI,SAAS,SAASqB,QAAO,QAAQ;AACnC,WAAO,cAAc,IAAI,GAAG,OAAO,6BAA6B,GAAG,EAAE,OAAO,MAAM,CAAC;AAAA,EACrF;AACA,MAAI,YAAY,SAASC,WAAU,QAAQ;AACzC,WAAO,cAAc,OAAO,GAAG,OAAO,6BAA6B,GAAG,EAAE,OAAO,MAAM,CAAC;AAAA,EACxF;AACA,MAAI,WAAW,OAAO,eAAe,iBAAiB,IAAI,EAAE,OAAO,OAAO,KAAK,QAAQ,CAAC;AACxF,MAAI,CAAC,SAAS,SAAS,IAAI,GAAG;AAC5B,aAAS,KAAK,IAAI;AAAA,EACpB;AACA,MAAI,mBAAmB,CAAC,IAAI,OAAO,uBAAuB,QAAQ,EAAE,OAAO,eAAe,IAAI,CAAC,EAAE,OAAO,SAAS,IAAI,SAAU,MAAM;AACnI,WAAO,IAAI,OAAO,MAAM,QAAQ,EAAE,OAAO,eAAe,IAAI;AAAA,EAC9D,CAAC,CAAC,EAAE,KAAK,IAAI;AACb,MAAI,iBAAiB,WAAW,GAAG;AACjC,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,MAAI,aAAa,CAAC;AAClB,MAAI;AACF,iBAAa,QAAQ,KAAK,iBAAiB,gBAAgB,CAAC;AAAA,EAC9D,SAAS,MAAM;AAAA,EAEf;AACA,MAAI,WAAW,SAAS,GAAG;AACzB,WAAO,SAAS;AAChB,cAAU,UAAU;AAAA,EACtB,OAAO;AACL,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,MAAI,OAAO,KAAK,MAAM,QAAQ;AAC9B,MAAI,YAAY,WAAW,OAAO,SAAU,KAAK,MAAM;AACrD,QAAI;AACF,UAAI,WAAW,iBAAiB,IAAI;AACpC,UAAI,UAAU;AACZ,YAAI,KAAK,QAAQ;AAAA,MACnB;AAAA,IACF,SAAS,MAAM;AACb,UAAI,CAAC,YAAY;AACf,YAAI,KAAK,SAAS,eAAe;AAC/B,kBAAQ,MAAM,IAAI;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,YAAQ,IAAI,SAAS,EAAE,KAAK,SAAU,mBAAmB;AACvD,cAAQ,mBAAmB,WAAY;AACrC,eAAO,QAAQ;AACf,eAAO,UAAU;AACjB,kBAAU,SAAS;AACnB,YAAI,OAAO,aAAa,WAAY,UAAS;AAC7C,aAAK;AACL,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC,EAAE,MAAM,SAAU,MAAM;AACvB,WAAK;AACL,aAAO,IAAI;AAAA,IACb,CAAC;AAAA,EACH,CAAC;AACH;AACA,SAAS,OAAO,MAAM;AACpB,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,mBAAiB,IAAI,EAAE,KAAK,SAAU,UAAU;AAC9C,QAAI,UAAU;AACZ,cAAQ,CAAC,QAAQ,GAAG,QAAQ;AAAA,IAC9B;AAAA,EACF,CAAC;AACH;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,SAAU,qBAAqB;AACpC,QAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAI,kBAAkB,uBAAuB,CAAC,GAAG,OAAO,sBAAsB,mBAAmB,uBAAuB,CAAC,CAAC;AAC1H,QAAI,OAAO,OAAO;AAClB,QAAI,MAAM;AACR,cAAQ,QAAQ,CAAC,GAAG,OAAO,OAAO,mBAAmB,QAAQ,CAAC,CAAC;AAAA,IACjE;AACA,WAAO,KAAK,gBAAgB,eAAe,eAAe,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG;AAAA,MACzE;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AACF;AACA,IAAI,SAAS,SAASC,QAAO,gBAAgB;AAC3C,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,MAAI,oBAAoB,OAAO,WAC7B,YAAY,sBAAsB,SAAS,uBAAuB,mBAClE,iBAAiB,OAAO,QACxB,SAAS,mBAAmB,SAAS,QAAQ,gBAC7C,eAAe,OAAO,MACtB,OAAO,iBAAiB,SAAS,OAAO,cACxC,iBAAiB,OAAO,QACxB,SAAS,mBAAmB,SAAS,OAAO,gBAC5C,kBAAkB,OAAO,SACzB,UAAU,oBAAoB,SAAS,CAAC,IAAI,iBAC5C,qBAAqB,OAAO,YAC5B,aAAa,uBAAuB,SAAS,CAAC,IAAI,oBAClD,iBAAiB,OAAO,QACxBvB,UAAS,mBAAmB,SAAS,CAAC,IAAI;AAC5C,MAAI,CAAC,eAAgB;AACrB,MAAI,SAAS,eAAe,QAC1B,WAAW,eAAe,UAC1BM,QAAO,eAAe;AACxB,SAAO,YAAY,eAAe;AAAA,IAChC,MAAM;AAAA,EACR,GAAG,cAAc,GAAG,WAAY;AAC9B,cAAU,4BAA4B;AAAA,MACpC;AAAA,MACA;AAAA,IACF,CAAC;AACD,WAAO,sBAAsB;AAAA,MAC3B,OAAO;AAAA,QACL,MAAM,YAAYA,KAAI;AAAA,QACtB,MAAM,OAAO,YAAY,KAAK,IAAI,IAAI;AAAA,UACpC,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,MAAM,CAAC;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,eAAe,eAAe,CAAC,GAAG,oBAAoB,GAAG,SAAS;AAAA,MAC7E;AAAA,MACA;AAAA,MACA,OAAO;AAAA,QACL;AAAA,QACA,QAAQN;AAAA,QACR;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ,SAASa,UAAS;AACxB,WAAO;AAAA,MACL,MAAM,aAAa,MAAM;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,OAAO,SAASC,SAAQ;AACtB,WAAO;AAAA,MACL,2BAA2B,SAAS,0BAA0B,aAAa;AACzE,oBAAY,eAAe;AAC3B,oBAAY,eAAe;AAC3B,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,SAAS,SAAS,cAAc;AACxC,iBAAa,QAAQ,SAAU,QAAQ;AACrC,UAAI,eAAe,OAAO,MACxB,OAAO,iBAAiB,SAAS,WAAW,cAC5C,mBAAmB,OAAO,UAC1B,WAAW,qBAAqB,SAAS,WAAY;AAAA,MAAC,IAAI;AAC5D,aAAO,OAAO,MAAM,QAAQ;AAAA,IAC9B;AACA,iBAAa,iCAAiC,SAAU,MAAM,UAAU;AACtE,UAAI,WAAW,SAAS,UACtB,SAAS,SAAS,QAClB,YAAY,SAAS,WACrB,SAAS,SAAS,QAClB,OAAO,SAAS,MAChB,SAAS,SAAS,QAClB,QAAQ,SAAS;AACnB,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,gBAAQ,IAAI,CAAC,SAAS,UAAU,MAAM,GAAG,KAAK,WAAW,SAAS,KAAK,UAAU,KAAK,MAAM,IAAI,QAAQ,QAAQ;AAAA,UAC9G,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,MAAM,CAAC;AAAA,QACT,CAAC,CAAC,CAAC,EAAE,KAAK,SAAUlB,OAAM;AACxB,cAAIC,SAAQ,eAAeD,OAAM,CAAC,GAChC,OAAOC,OAAM,CAAC,GACd2B,QAAO3B,OAAM,CAAC;AAChB,kBAAQ,CAAC,MAAM,sBAAsB;AAAA,YACnC,OAAO;AAAA,cACL;AAAA,cACA,MAAM2B;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,WAAW;AAAA,UACb,CAAC,CAAC,CAAC;AAAA,QACL,CAAC,EAAE,MAAM,MAAM;AAAA,MACjB,CAAC;AAAA,IACH;AACA,iBAAa,uBAAuB,SAAU,OAAO;AACnD,UAAI,WAAW,MAAM,UACnB,aAAa,MAAM,YACnB,OAAO,MAAM,MACb,YAAY,MAAM,WAClBxB,UAAS,MAAM;AACjB,UAAI,cAAc,WAAWA,OAAM;AACnC,UAAI,YAAY,SAAS,GAAG;AAC1B,mBAAW,OAAO,IAAI;AAAA,MACxB;AACA,UAAI;AACJ,UAAI,sBAAsB,SAAS,GAAG;AACpC,oBAAY,aAAa,qCAAqC;AAAA,UAC5D;AAAA,UACA;AAAA,UACA,gBAAgB,KAAK;AAAA,UACrB,WAAW,KAAK;AAAA,QAClB,CAAC;AAAA,MACH;AACA,eAAS,KAAK,aAAa,KAAK,IAAI;AACpC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,SAAS;AAAA,EACX,QAAQ,SAASa,UAAS;AACxB,WAAO;AAAA,MACL,OAAO,SAASY,OAAM,WAAW;AAC/B,YAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,YAAI,kBAAkB,OAAO,SAC3B,UAAU,oBAAoB,SAAS,CAAC,IAAI;AAC9C,eAAO,YAAY;AAAA,UACjB,MAAM;AAAA,QACR,GAAG,WAAY;AACb,oBAAU,4BAA4B;AAAA,YACpC;AAAA,YACA;AAAA,UACF,CAAC;AACD,cAAI,WAAW,CAAC;AAChB,oBAAU,SAAU,MAAM;AACxB,kBAAM,QAAQ,IAAI,IAAI,KAAK,IAAI,SAAU1C,IAAG;AAC1C,yBAAW,SAAS,OAAOA,GAAE,QAAQ;AAAA,YACvC,CAAC,IAAI,WAAW,SAAS,OAAO,KAAK,QAAQ;AAAA,UAC/C,CAAC;AACD,iBAAO,CAAC;AAAA,YACN,KAAK;AAAA,YACL,YAAY;AAAA,cACV,OAAO,CAAC,GAAG,OAAO,OAAO,WAAW,SAAS,CAAC,EAAE,OAAO,mBAAmB,OAAO,CAAC,EAAE,KAAK,GAAG;AAAA,YAC9F;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,SAAS8B,UAAS;AACxB,WAAO;AAAA,MACL,SAAS,SAASa,SAAQ,SAAS;AACjC,YAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,YAAI,gBAAgB,OAAO,OACzB,QAAQ,kBAAkB,SAAS,OAAO,eAC1C,kBAAkB,OAAO,SACzB,UAAU,oBAAoB,SAAS,CAAC,IAAI,iBAC5C,qBAAqB,OAAO,YAC5B,aAAa,uBAAuB,SAAS,CAAC,IAAI,oBAClD,iBAAiB,OAAO,QACxB1B,UAAS,mBAAmB,SAAS,CAAC,IAAI;AAC5C,eAAO,YAAY;AAAA,UACjB,MAAM;AAAA,UACN;AAAA,QACF,GAAG,WAAY;AACb,oBAAU,4BAA4B;AAAA,YACpC;AAAA,YACA;AAAA,UACF,CAAC;AACD,iBAAO,0BAA0B;AAAA,YAC/B,SAAS,QAAQ,SAAS;AAAA,YAC1B;AAAA,YACA,OAAO;AAAA,cACL;AAAA,cACA,QAAQA;AAAA,cACR,SAAS,CAAC,GAAG,OAAO,OAAO,WAAW,iBAAiB,CAAC,EAAE,OAAO,mBAAmB,OAAO,CAAC;AAAA,YAC9F;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,aAAa;AAAA,EACf,QAAQ,SAASa,UAAS;AACxB,WAAO;AAAA,MACL,MAAM,SAASc,MAAK,SAAS;AAC3B,YAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,YAAI,oBAAoB,OAAO,WAC7B,YAAY,sBAAsB,SAAS,uBAAuB,mBAClE,kBAAkB,OAAO,SACzB,UAAU,oBAAoB,SAAS,CAAC,IAAI,iBAC5C,qBAAqB,OAAO,YAC5B,aAAa,uBAAuB,SAAS,CAAC,IAAI,oBAClD,iBAAiB,OAAO,QACxB3B,UAAS,mBAAmB,SAAS,CAAC,IAAI;AAC5C,eAAO,YAAY;AAAA,UACjB,MAAM;AAAA,UACN;AAAA,QACF,GAAG,WAAY;AACb,oBAAU,4BAA4B;AAAA,YACpC;AAAA,YACA;AAAA,UACF,CAAC;AACD,iBAAO,uBAAuB;AAAA,YAC5B;AAAA,YACA,WAAW,eAAe,eAAe,CAAC,GAAG,oBAAoB,GAAG,SAAS;AAAA,YAC7E,OAAO;AAAA,cACL;AAAA,cACA,QAAQA;AAAA,cACR,SAAS,CAAC,GAAG,OAAO,OAAO,WAAW,cAAc,CAAC,EAAE,OAAO,mBAAmB,OAAO,CAAC;AAAA,YAC3F;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,SAAS4B,UAAS,cAAc;AACxC,iBAAa,qBAAqB,SAAU,MAAM,UAAU;AAC1D,UAAI,YAAY,SAAS,WACvB,QAAQ,SAAS;AACnB,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb,UAAI,OAAO;AACT,YAAI,mBAAmB,SAAS,iBAAiB,IAAI,EAAE,UAAU,EAAE;AACnE,YAAI,qBAAqB,KAAK,sBAAsB;AACpD,gBAAQ,mBAAmB,QAAQ;AACnC,iBAAS,mBAAmB,SAAS;AAAA,MACvC;AACA,aAAO,QAAQ,QAAQ,CAAC,MAAM,uBAAuB;AAAA,QACnD,SAAS,KAAK;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACb,CAAC,CAAC,CAAC;AAAA,IACL;AAAA,EACF;AACF;AAEA,IAAI,wBAAwB,IAAI,OAAO,KAAM,IAAI;AACjD,IAAI,0BAA0B,CAAC,SAAS,OAAO;AAC/C,IAAI,gCAAgC,eAAe,eAAe,eAAe,eAAe,CAAC,GAAG;AAAA,EAClG,aAAa;AAAA,IACX,QAAQ;AAAA,IACR,KAAK;AAAA,EACP;AACF,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChB,IAAI,+BAA+B,OAAO,KAAK,6BAA6B,EAAE,OAAO,SAAU,KAAK,KAAK;AACvG,MAAI,IAAI,YAAY,CAAC,IAAI,8BAA8B,GAAG;AAC1D,SAAO;AACT,GAAG,CAAC,CAAC;AACL,IAAI,8BAA8B,OAAO,KAAK,4BAA4B,EAAE,OAAO,SAAU,KAAK,YAAY;AAC5G,MAAI,UAAU,6BAA6B,UAAU;AACrD,MAAI,UAAU,IAAI,QAAQ,GAAG,KAAK,mBAAmB,OAAO,QAAQ,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;AAClF,SAAO;AACT,GAAG,CAAC,CAAC;AAGL,SAAS,oBAAoB,SAAS;AACpC,MAAI,UAAU,QAAQ,QAAQ,uBAAuB,EAAE;AACvD,SAAO,MAAM,mBAAmB,OAAO,EAAE,CAAC,KAAK,EAAE;AACnD;AAMA,SAAS,iBAAiB5B,SAAQ;AAChC,MAAI,kBAAkBA,QAAO,iBAAiB,uBAAuB,EAAE,SAAS,MAAM;AACtF,MAAI,UAAUA,QAAO,iBAAiB,SAAS;AAC/C,MAAI,UAAU,QAAQ,QAAQ,uBAAuB,EAAE;AACvD,MAAI,YAAY,QAAQ,YAAY,CAAC;AACrC,MAAI,eAAe,aAAa,wBAAwB,CAAC,KAAK,aAAa,wBAAwB,CAAC;AACpG,MAAI,YAAY,QAAQ,WAAW,IAAI,QAAQ,CAAC,MAAM,QAAQ,CAAC,IAAI;AACnE,SAAO,gBAAgB,aAAa;AACtC;AACA,SAAS,UAAU,YAAY,YAAY;AACzC,MAAI,sBAAsB,WAAW,QAAQ,gBAAgB,EAAE,EAAE,YAAY;AAC7E,MAAI,oBAAoB,SAAS,UAAU;AAC3C,MAAI,sBAAsB,MAAM,iBAAiB,IAAI,WAAW;AAChE,UAAQ,6BAA6B,mBAAmB,KAAK,CAAC,GAAG,mBAAmB,KAAK,4BAA4B,mBAAmB;AAC1I;AACA,SAAS,mBAAmB,MAAM,UAAU;AAC1C,MAAI,mBAAmB,GAAG,OAAO,8BAA8B,EAAE,OAAO,SAAS,QAAQ,KAAK,GAAG,CAAC;AAClG,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,QAAI,KAAK,aAAa,gBAAgB,MAAM,MAAM;AAEhD,aAAO,QAAQ;AAAA,IACjB;AACA,QAAI,WAAW,QAAQ,KAAK,QAAQ;AACpC,QAAI,gCAAgC,SAAS,OAAO,SAAU,MAAM;AAClE,aAAO,KAAK,aAAa,sBAAsB,MAAM;AAAA,IACvD,CAAC,EAAE,CAAC;AACJ,QAAIA,UAAS,OAAO,iBAAiB,MAAM,QAAQ;AACnD,QAAI,aAAaA,QAAO,iBAAiB,aAAa;AACtD,QAAI,kBAAkB,WAAW,MAAM,mBAAmB;AAC1D,QAAI,aAAaA,QAAO,iBAAiB,aAAa;AACtD,QAAI,UAAUA,QAAO,iBAAiB,SAAS;AAC/C,QAAI,iCAAiC,CAAC,iBAAiB;AAIrD,WAAK,YAAY,6BAA6B;AAC9C,aAAO,QAAQ;AAAA,IACjB,WAAW,mBAAmB,YAAY,UAAU,YAAY,IAAI;AAClE,UAAI,WAAWA,QAAO,iBAAiB,SAAS;AAChD,UAAI,SAAS,UAAU,YAAY,UAAU;AAC7C,UAAI,WAAW,oBAAoB,QAAQ;AAC3C,UAAI,OAAO,gBAAgB,CAAC,EAAE,WAAW,aAAa;AACtD,UAAI,cAAc,iBAAiBA,OAAM;AACzC,UAAI,WAAW,UAAU,QAAQ,QAAQ;AACzC,UAAI,iBAAiB;AACrB,UAAI,MAAM;AACR,YAAI,YAAY,aAAa,QAAQ;AACrC,YAAI,UAAU,YAAY,UAAU,QAAQ;AAC1C,qBAAW,UAAU;AACrB,mBAAS,UAAU;AAAA,QACrB;AAAA,MACF;AAIA,UAAI,YAAY,CAAC,gBAAgB,CAAC,iCAAiC,8BAA8B,aAAa,WAAW,MAAM,UAAU,8BAA8B,aAAa,SAAS,MAAM,iBAAiB;AAClN,aAAK,aAAa,kBAAkB,cAAc;AAClD,YAAI,+BAA+B;AAEjC,eAAK,YAAY,6BAA6B;AAAA,QAChD;AACA,YAAI,OAAO,UAAU;AACrB,YAAI,QAAQ,KAAK;AACjB,cAAM,WAAW,sBAAsB,IAAI;AAC3C,iBAAS,UAAU,MAAM,EAAE,KAAK,SAAU,MAAM;AAC9C,cAAI,WAAW,sBAAsB,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,YAChF,OAAO;AAAA,cACL;AAAA,cACA,MAAM,mBAAmB;AAAA,YAC3B;AAAA,YACA;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA,WAAW;AAAA,UACb,CAAC,CAAC;AACF,cAAI,UAAU,SAAS,gBAAgB,8BAA8B,KAAK;AAC1E,cAAI,aAAa,YAAY;AAC3B,iBAAK,aAAa,SAAS,KAAK,UAAU;AAAA,UAC5C,OAAO;AACL,iBAAK,YAAY,OAAO;AAAA,UAC1B;AACA,kBAAQ,YAAY,SAAS,IAAI,SAAU,MAAM;AAC/C,mBAAO,OAAO,IAAI;AAAA,UACpB,CAAC,EAAE,KAAK,IAAI;AACZ,eAAK,gBAAgB,gBAAgB;AACrC,kBAAQ;AAAA,QACV,CAAC,EAAE,MAAM,MAAM;AAAA,MACjB,OAAO;AACL,gBAAQ;AAAA,MACV;AAAA,IACF,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACH;AACA,SAAS6B,SAAQ,MAAM;AACrB,SAAO,QAAQ,IAAI,CAAC,mBAAmB,MAAM,UAAU,GAAG,mBAAmB,MAAM,SAAS,CAAC,CAAC;AAChG;AACA,SAAS,YAAY,MAAM;AACzB,SAAO,KAAK,eAAe,SAAS,QAAQ,CAAC,CAAC,oCAAoC,QAAQ,KAAK,QAAQ,YAAY,CAAC,KAAK,CAAC,KAAK,aAAa,sBAAsB,MAAM,CAAC,KAAK,cAAc,KAAK,WAAW,YAAY;AAC1N;AACA,IAAI,mBAAmB,SAASC,kBAAiB,UAAU;AACzD,SAAO,CAAC,CAAC,YAAY,gBAAgB,KAAK,SAAU,gBAAgB;AAClE,WAAO,SAAS,SAAS,cAAc;AAAA,EACzC,CAAC;AACH;AAIA,IAAI,yBAAyB,SAASC,wBAAuB,cAAc;AACzE,MAAI,CAAC,aAAc,QAAO,CAAC;AAC3B,MAAI,cAAc,oBAAI,IAAI;AAC1B,MAAI,YAAY,CAAC,YAAY;AAI7B,MAAI,YAAY,CAAC,WAAW,WAAC,oBAAiB;AAC9C,MAAI,QAAQ,SAASC,SAAQ;AAC3B,QAAI,WAAW,WAAW,EAAE;AAC5B,gBAAY,UAAU,QAAQ,SAAUC,WAAU;AAChD,aAAOA,UAAS,MAAM,QAAQ,EAAE,IAAI,SAAU,MAAM;AAClD,eAAO,KAAK,QAAQ,SAAS,EAAE,EAAE,KAAK;AAAA,MACxC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,WAAS,KAAK,GAAG,aAAa,WAAW,KAAK,WAAW,QAAQ,MAAM;AACrE,UAAM;AAAA,EACR;AACA,cAAY,UAAU,QAAQ,SAAUA,WAAU;AAChD,WAAOA,UAAS,SAAS,GAAG,IAAIA,YAAWA,UAAS,MAAM,GAAG,EAAE,IAAI,SAAU,MAAM;AACjF,aAAO,KAAK,KAAK;AAAA,IACnB,CAAC;AAAA,EACH,CAAC;AACD,MAAI,YAAY,2BAA2B,SAAS,GAClD;AACF,MAAI;AACF,SAAK,UAAU,EAAE,GAAG,EAAE,QAAQ,UAAU,EAAE,GAAG,QAAO;AAClD,UAAI,WAAW,MAAM;AACrB,UAAI,iBAAiB,QAAQ,GAAG;AAE9B,YAAI,wBAAwB,gBAAgB,OAAO,SAAU,KAAK,gBAAgB;AAChF,iBAAO,IAAI,QAAQ,gBAAgB,EAAE;AAAA,QACvC,GAAG,QAAQ;AACX,YAAI,0BAA0B,MAAM,0BAA0B,KAAK;AACjE,sBAAY,IAAI,qBAAqB;AAAA,QACvC;AAAA,MACF;AAAA,IACF;AAAA,EACF,SAAS,KAAK;AACZ,cAAU,EAAE,GAAG;AAAA,EACjB,UAAE;AACA,cAAU,EAAE;AAAA,EACd;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,MAAM;AAClC,MAAI,gBAAgB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACxF,MAAI,CAAC,OAAQ;AACb,MAAI;AACJ,MAAI,eAAe;AACjB,eAAW;AAAA,EACb,WAAW,OAAO,8BAA8B;AAC9C,eAAW,KAAK,iBAAiB,GAAG;AAAA,EACtC,OAAO;AAEL,QAAI,cAAc,oBAAI,IAAI;AAC1B,QAAI,aAAa,2BAA2B,SAAS,WAAW,GAC9D;AACF,QAAI;AACF,WAAK,WAAW,EAAE,GAAG,EAAE,SAAS,WAAW,EAAE,GAAG,QAAO;AACrD,YAAI,aAAa,OAAO;AACxB,YAAI;AACF,cAAI,aAAa,2BAA2B,WAAW,QAAQ,GAC7D;AACF,cAAI;AACF,iBAAK,WAAW,EAAE,GAAG,EAAE,SAAS,WAAW,EAAE,GAAG,QAAO;AACrD,kBAAI,OAAO,OAAO;AAClB,kBAAI,kBAAkB,uBAAuB,KAAK,YAAY;AAC9D,kBAAI,aAAa,2BAA2B,eAAe,GACzD;AACF,kBAAI;AACF,qBAAK,WAAW,EAAE,GAAG,EAAE,SAAS,WAAW,EAAE,GAAG,QAAO;AACrD,sBAAI,WAAW,OAAO;AACtB,8BAAY,IAAI,QAAQ;AAAA,gBAC1B;AAAA,cACF,SAAS,KAAK;AACZ,2BAAW,EAAE,GAAG;AAAA,cAClB,UAAE;AACA,2BAAW,EAAE;AAAA,cACf;AAAA,YACF;AAAA,UACF,SAAS,KAAK;AACZ,uBAAW,EAAE,GAAG;AAAA,UAClB,UAAE;AACA,uBAAW,EAAE;AAAA,UACf;AAAA,QACF,SAAS,MAAM;AACb,cAAI,OAAO,8BAA8B;AACvC,oBAAQ,KAAK,0CAA0C,OAAO,WAAW,MAAM,IAAI,EAAE,OAAO,KAAK,SAAS,qSAAuS,CAAC;AAAA,UACpZ;AAAA,QACF;AAAA,MACF;AAAA,IACF,SAAS,KAAK;AACZ,iBAAW,EAAE,GAAG;AAAA,IAClB,UAAE;AACA,iBAAW,EAAE;AAAA,IACf;AACA,QAAI,CAAC,YAAY,KAAM;AACvB,QAAI,iBAAiB,MAAM,KAAK,WAAW,EAAE,KAAK,IAAI;AACtD,QAAI;AACF,iBAAW,KAAK,iBAAiB,cAAc;AAAA,IACjD,SAAS,SAAS;AAAA,IAAC;AAAA,EACrB;AACA,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,QAAI,aAAa,QAAQ,QAAQ,EAAE,OAAO,WAAW,EAAE,IAAIJ,QAAO;AAClE,QAAIX,OAAM,KAAK,MAAM,sBAAsB;AAC3C,uBAAmB;AACnB,YAAQ,IAAI,UAAU,EAAE,KAAK,WAAY;AACvC,MAAAA,KAAI;AACJ,wBAAkB;AAClB,cAAQ;AAAA,IACV,CAAC,EAAE,MAAM,WAAY;AACnB,MAAAA,KAAI;AACJ,wBAAkB;AAClB,aAAO;AAAA,IACT,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAI,iBAAiB;AAAA,EACnB,OAAO,SAASJ,SAAQ;AACtB,WAAO;AAAA,MACL,2BAA2B,SAAS,0BAA0B,aAAa;AACzE,oBAAY,yBAAyB;AACrC,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,SAASc,UAASM,YAAW;AACrC,IAAAA,WAAU,qBAAqB,SAAU,QAAQ;AAC/C,UAAI,eAAe,OAAO,MACxB,OAAO,iBAAiB,SAAS,WAAW;AAC9C,UAAI,OAAO,sBAAsB;AAC/B,6BAAqB,IAAI;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,aAAa;AACjB,IAAI,qBAAqB;AAAA,EACvB,QAAQ,SAASrB,UAAS;AACxB,WAAO;AAAA,MACL,KAAK;AAAA,QACH,SAAS,SAAS,UAAU;AAC1B,6BAAmB;AACnB,uBAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,SAASC,SAAQ;AACtB,WAAO;AAAA,MACL,WAAW,SAAS,YAAY;AAC9B,gBAAQ,WAAW,6BAA6B,CAAC,CAAC,CAAC;AAAA,MACrD;AAAA,MACA,QAAQ,SAASC,UAAS;AACxB,mBAAW;AAAA,MACb;AAAA,MACA,OAAO,SAASoB,OAAM,QAAQ;AAC5B,YAAI,uBAAuB,OAAO;AAClC,YAAI,YAAY;AACd,4BAAkB;AAAA,QACpB,OAAO;AACL,kBAAQ,WAAW,6BAA6B;AAAA,YAC9C;AAAA,UACF,CAAC,CAAC;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,uBAAuB,SAASC,sBAAqB,iBAAiB;AACxE,MAAI,YAAY;AAAA,IACd,MAAM;AAAA,IACN,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACA,SAAO,gBAAgB,YAAY,EAAE,MAAM,GAAG,EAAE,OAAO,SAAU,KAAKnD,IAAG;AACvE,QAAI,QAAQA,GAAE,YAAY,EAAE,MAAM,GAAG;AACrC,QAAI,QAAQ,MAAM,CAAC;AACnB,QAAI,OAAO,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AAClC,QAAI,SAAS,SAAS,KAAK;AACzB,UAAI,QAAQ;AACZ,aAAO;AAAA,IACT;AACA,QAAI,SAAS,SAAS,KAAK;AACzB,UAAI,QAAQ;AACZ,aAAO;AAAA,IACT;AACA,WAAO,WAAW,IAAI;AACtB,QAAI,MAAM,IAAI,GAAG;AACf,aAAO;AAAA,IACT;AACA,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,YAAI,OAAO,IAAI,OAAO;AACtB;AAAA,MACF,KAAK;AACH,YAAI,OAAO,IAAI,OAAO;AACtB;AAAA,MACF,KAAK;AACH,YAAI,IAAI,IAAI,IAAI;AAChB;AAAA,MACF,KAAK;AACH,YAAI,IAAI,IAAI,IAAI;AAChB;AAAA,MACF,KAAK;AACH,YAAI,IAAI,IAAI,IAAI;AAChB;AAAA,MACF,KAAK;AACH,YAAI,IAAI,IAAI,IAAI;AAChB;AAAA,MACF,KAAK;AACH,YAAI,SAAS,IAAI,SAAS;AAC1B;AAAA,IACJ;AACA,WAAO;AAAA,EACT,GAAG,SAAS;AACd;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ,SAAS4B,UAAS;AACxB,WAAO;AAAA,MACL,OAAO;AAAA,QACL,WAAW,SAAS,UAAU,iBAAiB;AAC7C,iBAAO,qBAAqB,eAAe;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,SAASC,SAAQ;AACtB,WAAO;AAAA,MACL,qBAAqB,SAAS,oBAAoB,aAAa,MAAM;AACnE,YAAI,kBAAkB,KAAK,aAAa,mBAAmB;AAC3D,YAAI,iBAAiB;AACnB,sBAAY,YAAY,qBAAqB,eAAe;AAAA,QAC9D;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,SAASc,UAASM,YAAW;AACrC,IAAAA,WAAU,oCAAoC,SAAUtC,OAAM;AAC5D,UAAI,OAAOA,MAAK,MACd,YAAYA,MAAK,WACjB,iBAAiBA,MAAK,gBACtB,YAAYA,MAAK;AACnB,UAAI,QAAQ;AAAA,QACV,WAAW,aAAa,OAAO,iBAAiB,GAAG,OAAO;AAAA,MAC5D;AACA,UAAI,iBAAiB,aAAa,OAAO,UAAU,IAAI,IAAI,IAAI,EAAE,OAAO,UAAU,IAAI,IAAI,IAAI;AAC9F,UAAI,aAAa,SAAS,OAAO,UAAU,OAAO,MAAM,UAAU,QAAQ,KAAK,IAAI,IAAI,EAAE,OAAO,UAAU,OAAO,MAAM,UAAU,QAAQ,KAAK,IAAI,IAAI;AACtJ,UAAI,cAAc,UAAU,OAAO,UAAU,QAAQ,OAAO;AAC5D,UAAI,QAAQ;AAAA,QACV,WAAW,GAAG,OAAO,gBAAgB,GAAG,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,WAAW;AAAA,MACtF;AACA,UAAI,OAAO;AAAA,QACT,WAAW,aAAa,OAAO,YAAY,IAAI,IAAI,QAAQ;AAAA,MAC7D;AACA,UAAI,aAAa;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,QACL,KAAK;AAAA,QACL,YAAY,eAAe,CAAC,GAAG,WAAW,KAAK;AAAA,QAC/C,UAAU,CAAC;AAAA,UACT,KAAK;AAAA,UACL,YAAY,eAAe,CAAC,GAAG,WAAW,KAAK;AAAA,UAC/C,UAAU,CAAC;AAAA,YACT,KAAK,KAAK,KAAK;AAAA,YACf,UAAU,KAAK,KAAK;AAAA,YACpB,YAAY,eAAe,eAAe,CAAC,GAAG,KAAK,KAAK,UAAU,GAAG,WAAW,IAAI;AAAA,UACtF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,YAAY;AAAA,EACd,GAAG;AAAA,EACH,GAAG;AAAA,EACH,OAAO;AAAA,EACP,QAAQ;AACV;AACA,SAAS,UAAU,UAAU;AAC3B,MAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAChF,MAAI,SAAS,eAAe,SAAS,WAAW,QAAQ,QAAQ;AAC9D,aAAS,WAAW,OAAO;AAAA,EAC7B;AACA,SAAO;AACT;AACA,SAAS,QAAQ,UAAU;AACzB,MAAI,SAAS,QAAQ,KAAK;AACxB,WAAO,SAAS;AAAA,EAClB,OAAO;AACL,WAAO,CAAC,QAAQ;AAAA,EAClB;AACF;AACA,IAAI,QAAQ;AAAA,EACV,OAAO,SAASkB,SAAQ;AACtB,WAAO;AAAA,MACL,qBAAqB,SAAS,oBAAoB,aAAa,MAAM;AACnE,YAAI,WAAW,KAAK,aAAa,cAAc;AAC/C,YAAI,OAAO,CAAC,WAAW,mBAAmB,IAAI,iBAAiB,SAAS,MAAM,GAAG,EAAE,IAAI,SAAUvB,IAAG;AAClG,iBAAOA,GAAE,KAAK;AAAA,QAChB,CAAC,CAAC;AACF,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,SAAS,uBAAuB;AAAA,QACvC;AACA,oBAAY,OAAO;AACnB,oBAAY,SAAS,KAAK,aAAa,iBAAiB;AACxD,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,SAASqC,UAASM,YAAW;AACrC,IAAAA,WAAU,uBAAuB,SAAUtC,OAAM;AAC/C,UAAI,WAAWA,MAAK,UAClB,aAAaA,MAAK,YAClB,OAAOA,MAAK,MACZ,OAAOA,MAAK,MACZ,iBAAiBA,MAAK,QACtB,YAAYA,MAAK;AACnB,UAAI,YAAY,KAAK,OACnB,WAAW,KAAK;AAClB,UAAI,YAAY,KAAK,OACnB,WAAW,KAAK;AAClB,UAAI,QAAQ,gBAAgB;AAAA,QAC1B;AAAA,QACA,gBAAgB;AAAA,QAChB,WAAW;AAAA,MACb,CAAC;AACD,UAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,YAAY,eAAe,eAAe,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG;AAAA,UAC5D,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,UAAI,8BAA8B,SAAS,WAAW;AAAA,QACpD,UAAU,SAAS,SAAS,IAAI,SAAS;AAAA,MAC3C,IAAI,CAAC;AACL,UAAI,iBAAiB;AAAA,QACnB,KAAK;AAAA,QACL,YAAY,eAAe,CAAC,GAAG,MAAM,KAAK;AAAA,QAC1C,UAAU,CAAC,UAAU,eAAe;AAAA,UAClC,KAAK,SAAS;AAAA,UACd,YAAY,eAAe,eAAe,CAAC,GAAG,SAAS,UAAU,GAAG,MAAM,IAAI;AAAA,QAChF,GAAG,2BAA2B,CAAC,CAAC;AAAA,MAClC;AACA,UAAI,iBAAiB;AAAA,QACnB,KAAK;AAAA,QACL,YAAY,eAAe,CAAC,GAAG,MAAM,KAAK;AAAA,QAC1C,UAAU,CAAC,cAAc;AAAA,MAC3B;AACA,UAAI,SAAS,QAAQ,OAAO,kBAAkB,aAAa,CAAC;AAC5D,UAAI,SAAS,QAAQ,OAAO,kBAAkB,aAAa,CAAC;AAC5D,UAAI,UAAU;AAAA,QACZ,KAAK;AAAA,QACL,YAAY,eAAe,eAAe,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG;AAAA,UAC5D,IAAI;AAAA,UACJ,WAAW;AAAA,UACX,kBAAkB;AAAA,QACpB,CAAC;AAAA,QACD,UAAU,CAAC,UAAU,cAAc;AAAA,MACrC;AACA,UAAI,OAAO;AAAA,QACT,KAAK;AAAA,QACL,UAAU,CAAC;AAAA,UACT,KAAK;AAAA,UACL,YAAY;AAAA,YACV,IAAI;AAAA,UACN;AAAA,UACA,UAAU,QAAQ,QAAQ;AAAA,QAC5B,GAAG,OAAO;AAAA,MACZ;AACA,eAAS,KAAK,MAAM;AAAA,QAClB,KAAK;AAAA,QACL,YAAY,eAAe;AAAA,UACzB,QAAQ;AAAA,UACR,aAAa,QAAQ,OAAO,QAAQ,GAAG;AAAA,UACvC,QAAQ,QAAQ,OAAO,QAAQ,GAAG;AAAA,QACpC,GAAG,SAAS;AAAA,MACd,CAAC;AACD,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,uBAAuB;AAAA,EACzB,UAAU,SAASgC,UAASM,YAAW;AACrC,QAAI,eAAe;AACnB,QAAI,OAAO,YAAY;AACrB,qBAAe,OAAO,WAAW,kCAAkC,EAAE;AAAA,IACvE;AACA,IAAAA,WAAU,sBAAsB,WAAY;AAC1C,UAAI,YAAY,CAAC;AACjB,UAAI,OAAO;AAAA,QACT,MAAM;AAAA,MACR;AACA,UAAI,iBAAiB;AAAA,QACnB,eAAe;AAAA,QACf,aAAa;AAAA,QACb,KAAK;AAAA,MACP;AAGA,gBAAU,KAAK;AAAA,QACb,KAAK;AAAA,QACL,YAAY,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,UACvD,GAAG;AAAA,QACL,CAAC;AAAA,MACH,CAAC;AACD,UAAI,kBAAkB,eAAe,eAAe,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG;AAAA,QAC3E,eAAe;AAAA,MACjB,CAAC;AACD,UAAI,MAAM;AAAA,QACR,KAAK;AAAA,QACL,YAAY,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,UACvD,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,GAAG;AAAA,QACL,CAAC;AAAA,QACD,UAAU,CAAC;AAAA,MACb;AACA,UAAI,CAAC,cAAc;AACjB,YAAI,SAAS,KAAK;AAAA,UAChB,KAAK;AAAA,UACL,YAAY,eAAe,eAAe,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG;AAAA,YACjE,eAAe;AAAA,YACf,QAAQ;AAAA,UACV,CAAC;AAAA,QACH,GAAG;AAAA,UACD,KAAK;AAAA,UACL,YAAY,eAAe,eAAe,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG;AAAA,YAClE,QAAQ;AAAA,UACV,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,gBAAU,KAAK,GAAG;AAClB,gBAAU,KAAK;AAAA,QACb,KAAK;AAAA,QACL,YAAY,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,UACvD,SAAS;AAAA,UACT,GAAG;AAAA,QACL,CAAC;AAAA,QACD,UAAU,eAAe,CAAC,IAAI,CAAC;AAAA,UAC7B,KAAK;AAAA,UACL,YAAY,eAAe,eAAe,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG;AAAA,YAClE,QAAQ;AAAA,UACV,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AACD,UAAI,CAAC,cAAc;AAEjB,kBAAU,KAAK;AAAA,UACb,KAAK;AAAA,UACL,YAAY,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,YACvD,SAAS;AAAA,YACT,GAAG;AAAA,UACL,CAAC;AAAA,UACD,UAAU,CAAC;AAAA,YACT,KAAK;AAAA,YACL,YAAY,eAAe,eAAe,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG;AAAA,cAClE,QAAQ;AAAA,YACV,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,aAAO;AAAA,QACL,KAAK;AAAA,QACL,YAAY;AAAA,UACV,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,aAAa;AAAA,EACf,OAAO,SAASpB,SAAQ;AACtB,WAAO;AAAA,MACL,qBAAqB,SAAS,oBAAoB,aAAa,MAAM;AACnE,YAAI,aAAa,KAAK,aAAa,gBAAgB;AACnD,YAAI,SAAS,eAAe,OAAO,QAAQ,eAAe,KAAK,OAAO;AACtE,oBAAY,QAAQ,IAAI;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,UAAU,CAAC,WAAW,iBAAiB,QAAQ,eAAe,YAAY,gBAAgB,oBAAoB,iBAAiB,OAAO,sBAAsB,UAAU;AAE1K,gBAAgB,SAAS;AAAA,EACvB,WAAW;AACb,CAAC;AACD,IAAI,WAAW,IAAI;AACnB,IAAI,WAAW,IAAI;AACnB,IAAI,YAAY,IAAI;AACpB,IAAI,QAAQ,IAAI;AAChB,IAAI,UAAU,IAAI;AAClB,IAAI,uBAAuB,IAAI;AAC/B,IAAI,WAAW,IAAI;AACnB,IAAIR,QAAO,IAAI;AACf,IAAI,QAAQ,IAAI;AAChB,IAAI,OAAO,IAAI;AACf,IAAI,UAAU,IAAI;", "names": ["r", "a", "e", "n", "t", "o", "F", "u", "l", "i", "f", "p", "noop", "get", "_ref", "_ref2", "set", "css", "styles", "s", "bindInternal4", "b", "c", "d", "icon", "build", "lookup", "emptyCanonicalIcon", "arr", "Library", "k", "mixout", "hooks", "noAuto", "autoReplace", "begin", "end", "i2svg", "style", "hclAdd", "hclRemove", "render", "mask", "layer", "counter", "text", "provides", "replace", "hasPseudoElement", "parseCSSRuleForPseudos", "_loop", "selector", "providers", "watch", "parseTransformString"]}