{"clientTcpRtt": 277, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 9808, "clientAcceptEncoding": "gzip, deflate, br", "verifiedBotCategory": "", "country": "CN", "isEUCountry": false, "region": "Shanghai", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "/2ssCjD41wWOjM4of4BdvNYevyiPAn8xhCDOfje2T2I=", "tlsExportedAuthenticator": {"clientFinished": "61647a58456d42a9f1740b49fc06bc708c1578012596e5684f3b84149bda5d24341d76248b3154c5506aa35b261162e7", "clientHandshake": "98d99a588bb12262b731d64c9c2eaa0bede4118c142e29686545a1c0cf833198829301b1fc7b5207eea992f4007c5695", "serverHandshake": "543269cbb2aefa6a4abd848675785def6b787730d5e67022ce5d2b22d4ecc1da1ddfd09f50df035903830fdd564644f7", "serverFinished": "faec5abd52fb226736f2f5729415215267968546ad962809824430ff2a6e622f45909d0f5f5c742b1cfc3eee5678d6cd"}, "tlsClientHelloLength": "386", "colo": "SJC", "timezone": "Asia/Shanghai", "longitude": "121.45806", "latitude": "31.22222", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "200000", "city": "Shanghai", "tlsVersion": "TLSv1.3", "regionCode": "SH", "asOrganization": "China Mobile Communications Corporation", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}