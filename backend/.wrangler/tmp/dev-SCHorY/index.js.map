{"version": 3, "sources": ["../bundle-rlXE1v/checked-fetch.js", "../../../src/types/index.ts", "../../../src/modules/auth/jwt.service.ts", "../../../src/modules/auth/auth.service.ts", "../../../src/modules/shared/database.service.ts", "../../../src/middleware/turnstile.middleware.ts", "../../../src/middleware/auth.middleware.ts", "../../../src/handlers/auth.handler.ts", "../../../../node_modules/postal-mime/src/decode-strings.js", "../../../../node_modules/postal-mime/src/pass-through-decoder.js", "../../../../node_modules/postal-mime/src/base64-decoder.js", "../../../../node_modules/postal-mime/src/qp-decoder.js", "../../../../node_modules/postal-mime/src/mime-node.js", "../../../../node_modules/postal-mime/src/html-entities.js", "../../../../node_modules/postal-mime/src/text-format.js", "../../../../node_modules/postal-mime/src/address-parser.js", "../../../../node_modules/postal-mime/src/base64-encoder.js", "../../../../node_modules/postal-mime/src/postal-mime.js", "../../../src/modules/email/parser.service.ts", "../../../src/modules/email/email.service.ts", "../../../src/handlers/email.handler.ts", "../../../src/index.ts", "../../../../../../../../../usr/local/lib/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../../../../../../../usr/local/lib/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-rlXE1v/middleware-insertion-facade.js", "../../../../../../../../../usr/local/lib/node_modules/wrangler/templates/middleware/common.ts", "../bundle-rlXE1v/middleware-loader.entry.ts"], "sourceRoot": "/Users/<USER>/Desktop/code/temp_mail/backend/.wrangler/tmp/dev-SCHorY", "sourcesContent": ["const urls = new Set();\n\nfunction checkURL(request, init) {\n\tconst url =\n\t\trequest instanceof URL\n\t\t\t? request\n\t\t\t: new URL(\n\t\t\t\t\t(typeof request === \"string\"\n\t\t\t\t\t\t? new Request(request, init)\n\t\t\t\t\t\t: request\n\t\t\t\t\t).url\n\t\t\t\t);\n\tif (url.port && url.port !== \"443\" && url.protocol === \"https:\") {\n\t\tif (!urls.has(url.toString())) {\n\t\t\turls.add(url.toString());\n\t\t\tconsole.warn(\n\t\t\t\t`WARNING: known issue with \\`fetch()\\` requests to custom HTTPS ports in published Workers:\\n` +\n\t\t\t\t\t` - ${url.toString()} - the custom port will be ignored when the Worker is published using the \\`wrangler deploy\\` command.\\n`\n\t\t\t);\n\t\t}\n\t}\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\tconst [request, init] = argArray;\n\t\tcheckURL(request, init);\n\t\treturn Reflect.apply(target, thisArg, argArray);\n\t},\n});\n", "// 环境变量类型\nexport interface Env {\n  DB: D1Database\n  JWT_SECRET: string\n  BASE_DOMAIN: string\n  TURNSTILE_SECRET_KEY: string\n  TURNSTILE_SITE_KEY: string\n  ENVIRONMENT: 'development' | 'production'\n}\n\n// 用户相关类型\nexport interface User {\n  id: number\n  email: string\n  password_hash: string\n  quota: number\n  role: 'user' | 'admin'\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface CreateUserData {\n  email: string\n  password_hash: string\n  quota?: number\n  role?: 'user' | 'admin'\n}\n\n// 临时邮箱类型\nexport interface TempEmail {\n  id: number\n  user_id: number\n  email: string\n  domain_id: number\n  created_at: string\n  active: boolean\n}\n\n// 邮件类型\nexport interface Email {\n  id: number\n  temp_email_id: number\n  sender: string\n  subject: string | null\n  content: string | null\n  html_content: string | null\n  verification_code: string | null\n  is_read: boolean\n  received_at: string\n}\n\n// 域名类型\nexport interface Domain {\n  id: number\n  domain: string\n  status: number\n  created_at: string\n}\n\n// 兑换码类型\nexport interface RedeemCode {\n  code: string\n  quota: number\n  valid_until: string\n  used: boolean\n  used_by: number | null\n  used_at: string | null\n  created_at: string\n}\n\n// JWT相关类型\nexport interface JWTPayload {\n  userId: number\n  email: string\n  role: 'user' | 'admin'\n  type: 'access' | 'refresh'\n  iat: number\n  exp: number\n}\n\nexport interface TokenPair {\n  accessToken: string\n  refreshToken: string\n}\n\nexport interface RefreshToken {\n  id: number\n  user_id: number\n  token_hash: string\n  expires_at: string\n  created_at: string\n  is_revoked: boolean\n}\n\n// API请求/响应类型\nexport interface ApiResponse<T = any> {\n  success: boolean\n  data?: T\n  message?: string\n  error?: string\n}\n\nexport interface LoginRequest {\n  email: string\n  password: string\n  turnstileToken: string\n}\n\nexport interface RegisterRequest {\n  email: string\n  password: string\n  confirmPassword: string\n  turnstileToken: string\n}\n\nexport interface CreateEmailRequest {\n  domainId: number\n  turnstileToken: string\n}\n\nexport interface RedeemRequest {\n  code: string\n  turnstileToken: string\n}\n\n// 分页类型\nexport interface PaginationParams {\n  page: number\n  limit: number\n  offset: number\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[]\n  total: number\n  page: number\n  limit: number\n  totalPages: number\n}\n\n// 操作日志类型\nexport interface OperationLog {\n  id: number\n  user_id: number | null\n  action: string\n  ip_address: string | null\n  user_agent: string | null\n  details: string | null\n  timestamp: string\n}\n\n// 限流类型\nexport interface RateLimit {\n  id: number\n  identifier: string\n  endpoint: string\n  request_count: number\n  window_start: string\n}\n\nexport interface RateLimitRule {\n  endpoint: string\n  windowMs: number\n  maxRequests: number\n  requireAuth: boolean\n  requireTurnstile: boolean\n}\n\n// 邮件解析类型\nexport interface ParsedEmail {\n  from: {\n    address: string\n    name?: string\n  }\n  to: string\n  subject: string\n  text: string\n  html: string\n  verificationCode?: string\n}\n\n// 错误类型\nexport class AppError extends Error {\n  constructor(\n    public override message: string,\n    public statusCode: number = 500,\n    public code?: string\n  ) {\n    super(message)\n    this.name = 'AppError'\n  }\n}\n\nexport class ValidationError extends AppError {\n  constructor(message: string) {\n    super(message, 400, 'VALIDATION_ERROR')\n  }\n}\n\nexport class AuthenticationError extends AppError {\n  constructor(message: string = '认证失败') {\n    super(message, 401, 'AUTH_ERROR')\n  }\n}\n\nexport class AuthorizationError extends AppError {\n  constructor(message: string = '权限不足') {\n    super(message, 403, 'AUTHORIZATION_ERROR')\n  }\n}\n\nexport class NotFoundError extends AppError {\n  constructor(message: string = '资源不存在') {\n    super(message, 404, 'NOT_FOUND_ERROR')\n  }\n}\n\nexport class RateLimitError extends AppError {\n  constructor(message: string = '请求过于频繁') {\n    super(message, 429, 'RATE_LIMIT_ERROR')\n  }\n}\n\n// Turnstile验证响应类型\nexport interface TurnstileResponse {\n  success: boolean\n  'error-codes'?: string[]\n  challenge_ts?: string\n  hostname?: string\n}\n", "import type { Env, User, JWTPayload, TokenPair } from '@/types'\nimport { DatabaseService } from '@/modules/shared/database.service'\n\nexport class JWTService {\n  private readonly ACCESS_TOKEN_EXPIRES = 15 * 24 * 60 * 60 // 15天\n  private readonly REFRESH_TOKEN_EXPIRES = 30 * 24 * 60 * 60 // 30天\n\n  constructor(\n    private env: Env,\n    private dbService: DatabaseService\n  ) {}\n\n  async generateTokenPair(user: User): Promise<TokenPair> {\n    const accessToken = await this.generateAccessToken(user)\n    const refreshToken = await this.generateRefreshToken(user)\n\n    // 存储 refresh token 到数据库\n    await this.storeRefreshToken(user.id, refreshToken)\n\n    return { accessToken, refreshToken }\n  }\n\n  private async generateAccessToken(user: User): Promise<string> {\n    const payload: JWTPayload = {\n      userId: user.id,\n      email: user.email,\n      role: user.role,\n      type: 'access',\n      iat: Math.floor(Date.now() / 1000),\n      exp: Math.floor(Date.now() / 1000) + this.ACCESS_TOKEN_EXPIRES\n    }\n\n    return await this.signJWT(payload)\n  }\n\n  private async generateRefreshToken(user: User): Promise<string> {\n    const payload: JWTPayload = {\n      userId: user.id,\n      email: user.email,\n      role: user.role,\n      type: 'refresh',\n      iat: Math.floor(Date.now() / 1000),\n      exp: Math.floor(Date.now() / 1000) + this.REFRESH_TOKEN_EXPIRES\n    }\n\n    return await this.signJWT(payload)\n  }\n\n  private async signJWT(payload: JWTPayload): Promise<string> {\n    if (!this.env.JWT_SECRET) {\n      throw new Error('JWT_SECRET is not configured')\n    }\n\n    const encoder = new TextEncoder()\n    const keyData = encoder.encode(this.env.JWT_SECRET)\n    \n    const key = await crypto.subtle.importKey(\n      'raw',\n      keyData,\n      { name: 'HMAC', hash: 'SHA-256' },\n      false,\n      ['sign']\n    )\n\n    const header = {\n      alg: 'HS256',\n      typ: 'JWT'\n    }\n\n    const encodedHeader = this.base64UrlEncode(JSON.stringify(header))\n    const encodedPayload = this.base64UrlEncode(JSON.stringify(payload))\n    \n    const data = encoder.encode(`${encodedHeader}.${encodedPayload}`)\n    const signature = await crypto.subtle.sign('HMAC', key, data)\n    const encodedSignature = this.base64UrlEncode(signature)\n\n    return `${encodedHeader}.${encodedPayload}.${encodedSignature}`\n  }\n\n  async verifyJWT(token: string): Promise<JWTPayload | null> {\n    try {\n      const parts = token.split('.')\n      if (parts.length !== 3) {\n        return null\n      }\n\n      const [encodedHeader, encodedPayload, encodedSignature] = parts\n\n      // 检查所有部分是否存在\n      if (!encodedHeader || !encodedPayload || !encodedSignature) {\n        return null\n      }\n\n      // 验证签名\n      if (!this.env.JWT_SECRET) {\n        throw new Error('JWT_SECRET is not configured')\n      }\n\n      const encoder = new TextEncoder()\n      const keyData = encoder.encode(this.env.JWT_SECRET)\n      \n      const key = await crypto.subtle.importKey(\n        'raw',\n        keyData,\n        { name: 'HMAC', hash: 'SHA-256' },\n        false,\n        ['verify']\n      )\n\n      const data = encoder.encode(`${encodedHeader}.${encodedPayload}`)\n      const signature = this.base64UrlDecode(encodedSignature)\n      \n      const isValid = await crypto.subtle.verify('HMAC', key, signature, data)\n      if (!isValid) {\n        return null\n      }\n\n      // 解析payload\n      const payload: JWTPayload = JSON.parse(this.base64UrlDecodeString(encodedPayload))\n      \n      // 检查过期时间\n      if (payload.exp < Math.floor(Date.now() / 1000)) {\n        return null\n      }\n\n      return payload\n    } catch (error) {\n      console.error('JWT verification error:', error)\n      return null\n    }\n  }\n\n  async refreshTokens(refreshToken: string): Promise<TokenPair | null> {\n    // 验证 refresh token\n    const payload = await this.verifyJWT(refreshToken)\n    if (!payload || payload.type !== 'refresh') {\n      return null\n    }\n\n    // 检查数据库中的 refresh token\n    const tokenHash = await this.hashToken(refreshToken)\n    const storedToken = await this.dbService.getRefreshToken(tokenHash)\n    if (!storedToken) {\n      return null\n    }\n\n    // 获取用户信息\n    const user = await this.dbService.getUserById(payload.userId)\n    if (!user) {\n      return null\n    }\n\n    // 撤销旧的 refresh token\n    await this.dbService.revokeRefreshToken(tokenHash)\n\n    // 生成新的 token 对\n    return await this.generateTokenPair(user)\n  }\n\n  private async storeRefreshToken(userId: number, refreshToken: string): Promise<void> {\n    const tokenHash = await this.hashToken(refreshToken)\n    const expiresAt = new Date(Date.now() + this.REFRESH_TOKEN_EXPIRES * 1000).toISOString()\n    \n    await this.dbService.storeRefreshToken(userId, tokenHash, expiresAt)\n  }\n\n  async revokeRefreshToken(refreshToken: string): Promise<void> {\n    const tokenHash = await this.hashToken(refreshToken)\n    await this.dbService.revokeRefreshToken(tokenHash)\n  }\n\n  private async hashToken(token: string): Promise<string> {\n    const encoder = new TextEncoder()\n    const data = encoder.encode(token)\n    const hashBuffer = await crypto.subtle.digest('SHA-256', data)\n    const hashArray = Array.from(new Uint8Array(hashBuffer))\n    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')\n  }\n\n  private base64UrlEncode(data: string | ArrayBuffer): string {\n    let base64: string\n    \n    if (typeof data === 'string') {\n      base64 = btoa(data)\n    } else {\n      const bytes = new Uint8Array(data)\n      const binary = Array.from(bytes, byte => String.fromCharCode(byte)).join('')\n      base64 = btoa(binary)\n    }\n    \n    return base64.replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=/g, '')\n  }\n\n  private base64UrlDecode(data: string): ArrayBuffer {\n    const base64 = data.replace(/-/g, '+').replace(/_/g, '/')\n    const padded = base64.padEnd(base64.length + (4 - base64.length % 4) % 4, '=')\n    const binary = atob(padded)\n    const bytes = new Uint8Array(binary.length)\n    for (let i = 0; i < binary.length; i++) {\n      bytes[i] = binary.charCodeAt(i)\n    }\n    return bytes.buffer\n  }\n\n  private base64UrlDecodeString(data: string): string {\n    const base64 = data.replace(/-/g, '+').replace(/_/g, '/')\n    const padded = base64.padEnd(base64.length + (4 - base64.length % 4) % 4, '=')\n    return atob(padded)\n  }\n}\n", "import type {\n  Env,\n  User,\n  LoginRequest,\n  RegisterRequest,\n  TokenPair,\n  CreateUserData\n} from '@/types'\nimport { ValidationError, AuthenticationError } from '@/types'\nimport { DatabaseService } from '@/modules/shared/database.service'\nimport { JWTService } from './jwt.service'\n\nexport class AuthService {\n  private jwtService: JWTService\n\n  constructor(\n    private env: Env,\n    private dbService: DatabaseService\n  ) {\n    this.jwtService = new JWTService(env, dbService)\n  }\n\n  async register(data: RegisterRequest): Promise<{ user: User; tokens: TokenPair }> {\n    // 1. 验证输入数据\n    this.validateRegisterData(data)\n\n    // 2. 检查邮箱是否已存在\n    const existingUser = await this.dbService.getUserByEmail(data.email)\n    if (existingUser) {\n      throw new ValidationError('邮箱已被注册')\n    }\n\n    // 3. 验证密码一致性\n    if (data.password !== data.confirmPassword) {\n      throw new ValidationError('两次输入的密码不一致')\n    }\n\n    // 4. 创建用户\n    const passwordHash = await this.hashPassword(data.password)\n    const userData: CreateUserData = {\n      email: data.email,\n      password_hash: passwordHash,\n      quota: 5, // 注册赠送5个配额\n      role: 'user'\n    }\n\n    const user = await this.dbService.createUser(userData)\n\n    // 5. 生成JWT token对\n    const tokens = await this.jwtService.generateTokenPair(user)\n\n    // 6. 记录日志\n    await this.dbService.createLog({\n      userId: user.id,\n      action: 'REGISTER',\n      details: `User registered: ${user.email}`\n    })\n\n    // 7. 返回用户信息（不包含密码）\n    const { password_hash, ...userWithoutPassword } = user\n    return {\n      user: userWithoutPassword as User,\n      tokens\n    }\n  }\n\n  async login(data: LoginRequest): Promise<{ user: User; tokens: TokenPair }> {\n    // 1. 验证输入数据\n    this.validateLoginData(data)\n\n    // 2. 查找用户\n    const user = await this.dbService.getUserByEmail(data.email)\n    if (!user) {\n      throw new AuthenticationError('邮箱或密码错误')\n    }\n\n    // 3. 验证密码\n    const isPasswordValid = await this.verifyPassword(data.password, user.password_hash)\n    if (!isPasswordValid) {\n      throw new AuthenticationError('邮箱或密码错误')\n    }\n\n    // 4. 检查用户状态\n    if (!user.is_active) {\n      throw new AuthenticationError('账户已被禁用')\n    }\n\n    // 5. 生成JWT token对\n    const tokens = await this.jwtService.generateTokenPair(user)\n\n    // 6. 记录日志\n    await this.dbService.createLog({\n      userId: user.id,\n      action: 'LOGIN',\n      details: `User logged in: ${user.email}`\n    })\n\n    // 7. 返回用户信息（不包含密码）\n    const { password_hash, ...userWithoutPassword } = user\n    return {\n      user: userWithoutPassword as User,\n      tokens\n    }\n  }\n\n  async refreshTokens(refreshToken: string): Promise<TokenPair> {\n    const tokens = await this.jwtService.refreshTokens(refreshToken)\n    if (!tokens) {\n      throw new AuthenticationError('无效的刷新令牌')\n    }\n    return tokens\n  }\n\n  async logout(refreshToken: string): Promise<void> {\n    await this.jwtService.revokeRefreshToken(refreshToken)\n  }\n\n  async getCurrentUser(userId: number): Promise<User> {\n    const user = await this.dbService.getUserById(userId)\n    if (!user) {\n      throw new AuthenticationError('用户不存在')\n    }\n\n    const { password_hash, ...userWithoutPassword } = user\n    return userWithoutPassword as User\n  }\n\n  async changePassword(\n    userId: number, \n    currentPassword: string, \n    newPassword: string\n  ): Promise<void> {\n    // 1. 获取用户信息\n    const user = await this.dbService.getUserById(userId)\n    if (!user) {\n      throw new AuthenticationError('用户不存在')\n    }\n\n    // 2. 验证当前密码\n    const isCurrentPasswordValid = await this.verifyPassword(currentPassword, user.password_hash)\n    if (!isCurrentPasswordValid) {\n      throw new AuthenticationError('当前密码错误')\n    }\n\n    // 3. 验证新密码\n    this.validatePassword(newPassword)\n\n    // 4. 更新密码\n    const newPasswordHash = await this.hashPassword(newPassword)\n    await this.dbService.updateUserPassword(userId, newPasswordHash)\n\n    // 5. 记录日志\n    await this.dbService.createLog({\n      userId,\n      action: 'CHANGE_PASSWORD',\n      details: 'User changed password'\n    })\n  }\n\n  private validateRegisterData(data: RegisterRequest): void {\n    if (!this.isValidEmail(data.email)) {\n      throw new ValidationError('邮箱格式不正确')\n    }\n\n    this.validatePassword(data.password)\n\n    if (!data.turnstileToken) {\n      throw new ValidationError('请完成人机验证')\n    }\n  }\n\n  private validateLoginData(data: LoginRequest): void {\n    if (!data.email || !data.password) {\n      throw new ValidationError('邮箱和密码不能为空')\n    }\n\n    if (!data.turnstileToken) {\n      throw new ValidationError('请完成人机验证')\n    }\n  }\n\n  private validatePassword(password: string): void {\n    if (!password || password.length < 6) {\n      throw new ValidationError('密码长度至少6位')\n    }\n\n    if (password.length > 128) {\n      throw new ValidationError('密码长度不能超过128位')\n    }\n\n    // 可以添加更多密码复杂度验证\n    // if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/.test(password)) {\n    //   throw new ValidationError('密码必须包含大小写字母和数字')\n    // }\n  }\n\n  private isValidEmail(email: string): boolean {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n    return emailRegex.test(email) && email.length <= 254\n  }\n\n  private async hashPassword(password: string): Promise<string> {\n    // 使用 Web Crypto API 实现 bcrypt 类似的功能\n    const encoder = new TextEncoder()\n    const salt = crypto.getRandomValues(new Uint8Array(16))\n    const passwordData = encoder.encode(password)\n    \n    // 合并密码和盐\n    const combined = new Uint8Array(passwordData.length + salt.length)\n    combined.set(passwordData)\n    combined.set(salt, passwordData.length)\n    \n    // 多次哈希以增加安全性\n    let hash = await crypto.subtle.digest('SHA-256', combined)\n    for (let i = 0; i < 10000; i++) {\n      hash = await crypto.subtle.digest('SHA-256', hash)\n    }\n    \n    // 将盐和哈希组合并编码为base64\n    const result = new Uint8Array(salt.length + hash.byteLength)\n    result.set(salt)\n    result.set(new Uint8Array(hash), salt.length)\n    \n    return btoa(String.fromCharCode(...result))\n  }\n\n  private async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n    try {\n      const encoder = new TextEncoder()\n      const passwordData = encoder.encode(password)\n      \n      // 解码存储的哈希\n      const stored = Uint8Array.from(atob(hashedPassword), c => c.charCodeAt(0))\n      const salt = stored.slice(0, 16)\n      const storedHash = stored.slice(16)\n      \n      // 重新计算哈希\n      const combined = new Uint8Array(passwordData.length + salt.length)\n      combined.set(passwordData)\n      combined.set(salt, passwordData.length)\n      \n      let hash = await crypto.subtle.digest('SHA-256', combined)\n      for (let i = 0; i < 10000; i++) {\n        hash = await crypto.subtle.digest('SHA-256', hash)\n      }\n      \n      // 比较哈希\n      const computedHash = new Uint8Array(hash)\n      if (computedHash.length !== storedHash.length) {\n        return false\n      }\n      \n      for (let i = 0; i < computedHash.length; i++) {\n        if (computedHash[i] !== storedHash[i]) {\n          return false\n        }\n      }\n      \n      return true\n    } catch (error) {\n      console.error('Password verification error:', error)\n      return false\n    }\n  }\n}\n", "import type { \n  Env, \n  User, \n  TempEmail, \n  Email, \n  Domain, \n  RedeemCode, \n  RefreshToken,\n  OperationLog,\n  RateLimit,\n  CreateUserData,\n  PaginationParams,\n  PaginatedResponse\n} from '@/types'\n\nexport class DatabaseService {\n  constructor(private db: D1Database) {}\n\n  // 用户相关操作\n  async createUser(userData: CreateUserData): Promise<User> {\n    const result = await this.db.prepare(`\n      INSERT INTO users (email, password_hash, quota, role)\n      VALUES (?, ?, ?, ?)\n      RETURNING *\n    `).bind(\n      userData.email,\n      userData.password_hash,\n      userData.quota || 5,\n      userData.role || 'user'\n    ).first<User>()\n\n    if (!result) {\n      throw new Error('Failed to create user')\n    }\n\n    return result\n  }\n\n  async getUserByEmail(email: string): Promise<User | null> {\n    return await this.db.prepare(`\n      SELECT * FROM users WHERE email = ? AND is_active = 1\n    `).bind(email).first<User>()\n  }\n\n  async getUserById(id: number): Promise<User | null> {\n    return await this.db.prepare(`\n      SELECT * FROM users WHERE id = ? AND is_active = 1\n    `).bind(id).first<User>()\n  }\n\n  async updateUserQuota(userId: number, quota: number): Promise<void> {\n    await this.db.prepare(`\n      UPDATE users SET quota = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?\n    `).bind(quota, userId).run()\n  }\n\n  async decrementUserQuota(userId: number): Promise<boolean> {\n    const result = await this.db.prepare(`\n      UPDATE users SET quota = quota - 1, updated_at = CURRENT_TIMESTAMP\n      WHERE id = ? AND quota > 0\n    `).bind(userId).run()\n\n    return (result.meta?.changes ?? 0) > 0\n  }\n\n  async updateUserPassword(userId: number, passwordHash: string): Promise<boolean> {\n    const result = await this.db.prepare(`\n      UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?\n    `).bind(passwordHash, userId).run()\n\n    return (result.meta?.changes ?? 0) > 0\n  }\n\n  // 临时邮箱相关操作\n  async createTempEmail(userId: number, email: string, domainId: number): Promise<TempEmail> {\n    const result = await this.db.prepare(`\n      INSERT INTO temp_emails (user_id, email, domain_id)\n      VALUES (?, ?, ?)\n      RETURNING *\n    `).bind(userId, email, domainId).first<TempEmail>()\n\n    if (!result) {\n      throw new Error('Failed to create temp email')\n    }\n\n    return result\n  }\n\n  async getTempEmailsByUserId(userId: number): Promise<TempEmail[]> {\n    const result = await this.db.prepare(`\n      SELECT * FROM temp_emails \n      WHERE user_id = ? AND active = 1 \n      ORDER BY created_at DESC\n    `).bind(userId).all<TempEmail>()\n\n    return result.results || []\n  }\n\n  async getTempEmailByEmail(email: string): Promise<TempEmail | null> {\n    return await this.db.prepare(`\n      SELECT * FROM temp_emails WHERE email = ? AND active = 1\n    `).bind(email).first<TempEmail>()\n  }\n\n  async deleteTempEmail(id: number, userId: number): Promise<boolean> {\n    const result = await this.db.prepare(`\n      UPDATE temp_emails SET active = 0 WHERE id = ? AND user_id = ?\n    `).bind(id, userId).run()\n\n    return (result.meta?.changes ?? 0) > 0\n  }\n\n  // 邮件相关操作\n  async createEmail(emailData: {\n    tempEmailId: number\n    sender: string\n    subject?: string\n    content?: string\n    htmlContent?: string\n    verificationCode?: string\n  }): Promise<Email> {\n    const result = await this.db.prepare(`\n      INSERT INTO emails (temp_email_id, sender, subject, content, html_content, verification_code)\n      VALUES (?, ?, ?, ?, ?, ?)\n      RETURNING *\n    `).bind(\n      emailData.tempEmailId,\n      emailData.sender,\n      emailData.subject || null,\n      emailData.content || null,\n      emailData.htmlContent || null,\n      emailData.verificationCode || null\n    ).first<Email>()\n\n    if (!result) {\n      throw new Error('Failed to create email')\n    }\n\n    return result\n  }\n\n  async getEmailsForTempEmail(\n    tempEmailId: number, \n    pagination: PaginationParams\n  ): Promise<PaginatedResponse<Email>> {\n    // 获取总数\n    const countResult = await this.db.prepare(`\n      SELECT COUNT(*) as total FROM emails WHERE temp_email_id = ?\n    `).bind(tempEmailId).first<{ total: number }>()\n\n    const total = countResult?.total || 0\n\n    // 获取分页数据\n    const result = await this.db.prepare(`\n      SELECT * FROM emails \n      WHERE temp_email_id = ? \n      ORDER BY received_at DESC \n      LIMIT ? OFFSET ?\n    `).bind(tempEmailId, pagination.limit, pagination.offset).all<Email>()\n\n    return {\n      data: result.results || [],\n      total,\n      page: pagination.page,\n      limit: pagination.limit,\n      totalPages: Math.ceil(total / pagination.limit)\n    }\n  }\n\n  async deleteEmail(id: number): Promise<boolean> {\n    const result = await this.db.prepare(`\n      DELETE FROM emails WHERE id = ?\n    `).bind(id).run()\n\n    return (result.meta?.changes ?? 0) > 0\n  }\n\n  // 域名相关操作\n  async getActiveDomains(): Promise<Domain[]> {\n    const result = await this.db.prepare(`\n      SELECT * FROM domains WHERE status = 1 ORDER BY domain\n    `).bind().all<Domain>()\n\n    return result.results || []\n  }\n\n  async getDomainById(id: number): Promise<Domain | null> {\n    return await this.db.prepare(`\n      SELECT * FROM domains WHERE id = ?\n    `).bind(id).first<Domain>()\n  }\n\n  // 兑换码相关操作\n  async getRedeemCode(code: string): Promise<RedeemCode | null> {\n    return await this.db.prepare(`\n      SELECT * FROM redeem_codes WHERE code = ?\n    `).bind(code).first<RedeemCode>()\n  }\n\n  async useRedeemCode(code: string, userId: number): Promise<boolean> {\n    const result = await this.db.prepare(`\n      UPDATE redeem_codes \n      SET used = 1, used_by = ?, used_at = CURRENT_TIMESTAMP \n      WHERE code = ? AND used = 0 AND valid_until > CURRENT_TIMESTAMP\n    `).bind(userId, code).run()\n\n    return (result.meta?.changes ?? 0) > 0\n  }\n\n  // 刷新令牌相关操作\n  async storeRefreshToken(userId: number, tokenHash: string, expiresAt: string): Promise<void> {\n    await this.db.prepare(`\n      INSERT INTO refresh_tokens (user_id, token_hash, expires_at)\n      VALUES (?, ?, ?)\n    `).bind(userId, tokenHash, expiresAt).run()\n  }\n\n  async getRefreshToken(tokenHash: string): Promise<RefreshToken | null> {\n    return await this.db.prepare(`\n      SELECT * FROM refresh_tokens \n      WHERE token_hash = ? AND is_revoked = 0 AND expires_at > CURRENT_TIMESTAMP\n    `).bind(tokenHash).first<RefreshToken>()\n  }\n\n  async revokeRefreshToken(tokenHash: string): Promise<void> {\n    await this.db.prepare(`\n      UPDATE refresh_tokens SET is_revoked = 1 WHERE token_hash = ?\n    `).bind(tokenHash).run()\n  }\n\n  // 日志相关操作\n  async createLog(logData: {\n    userId?: number\n    action: string\n    ipAddress?: string\n    userAgent?: string\n    details?: string\n  }): Promise<void> {\n    await this.db.prepare(`\n      INSERT INTO logs (user_id, action, ip_address, user_agent, details)\n      VALUES (?, ?, ?, ?, ?)\n    `).bind(\n      logData.userId || null,\n      logData.action,\n      logData.ipAddress || null,\n      logData.userAgent || null,\n      logData.details || null\n    ).run()\n  }\n\n  // 限流相关操作\n  async getRateLimit(identifier: string, endpoint: string): Promise<RateLimit | null> {\n    return await this.db.prepare(`\n      SELECT * FROM rate_limits \n      WHERE identifier = ? AND endpoint = ? \n      AND datetime(window_start, '+1 hour') > datetime('now')\n    `).bind(identifier, endpoint).first<RateLimit>()\n  }\n\n  async createOrUpdateRateLimit(identifier: string, endpoint: string): Promise<number> {\n    // 尝试更新现有记录\n    const updateResult = await this.db.prepare(`\n      UPDATE rate_limits \n      SET request_count = request_count + 1 \n      WHERE identifier = ? AND endpoint = ? \n      AND datetime(window_start, '+1 hour') > datetime('now')\n    `).bind(identifier, endpoint).run()\n\n    if ((updateResult.meta?.changes ?? 0) > 0) {\n      // 获取更新后的计数\n      const result = await this.getRateLimit(identifier, endpoint)\n      return result?.request_count || 1\n    } else {\n      // 创建新记录\n      await this.db.prepare(`\n        INSERT INTO rate_limits (identifier, endpoint, request_count)\n        VALUES (?, ?, 1)\n      `).bind(identifier, endpoint).run()\n      return 1\n    }\n  }\n}\n", "import type { Env, TurnstileResponse, ValidationError } from '@/types'\n\nexport class TurnstileService {\n  constructor(private env: Env) {}\n\n  async verifyToken(token: string, remoteIP?: string): Promise<boolean> {\n    // 开发环境跳过验证\n    if (this.env.ENVIRONMENT === 'development') {\n      return true\n    }\n\n    try {\n      const response = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          secret: this.env.TURNSTILE_SECRET_KEY,\n          response: token,\n          remoteip: remoteIP\n        })\n      })\n\n      if (!response.ok) {\n        console.error('Turnstile API error:', response.status, response.statusText)\n        return false\n      }\n\n      const result: TurnstileResponse = await response.json()\n      \n      if (!result.success) {\n        console.error('Turnstile verification failed:', result['error-codes'])\n        return false\n      }\n\n      return true\n    } catch (error) {\n      console.error('Turnstile verification error:', error)\n      return false\n    }\n  }\n}\n\nexport function createTurnstileMiddleware(env: Env) {\n  const turnstileService = new TurnstileService(env)\n\n  return async function turnstileMiddleware(\n    request: Request,\n    next: () => Promise<Response>\n  ): Promise<Response> {\n    // 只对需要验证的端点进行检查\n    const url = new URL(request.url)\n    const needsVerification = [\n      '/api/auth/register',\n      '/api/auth/login',\n      '/api/email/create',\n      '/api/redeem'\n    ].some(path => url.pathname === path)\n\n    if (!needsVerification) {\n      return next()\n    }\n\n    // 只对POST请求进行验证\n    if (request.method !== 'POST') {\n      return next()\n    }\n\n    try {\n      const body = await request.json() as any\n      const turnstileToken = body.turnstileToken\n\n      if (!turnstileToken) {\n        return new Response(JSON.stringify({\n          success: false,\n          error: '缺少人机验证令牌'\n        }), {\n          status: 400,\n          headers: { 'Content-Type': 'application/json' }\n        })\n      }\n\n      // 获取客户端IP\n      const clientIP = request.headers.get('CF-Connecting-IP') || \n                      request.headers.get('X-Forwarded-For') || \n                      request.headers.get('X-Real-IP')\n\n      const isValid = await turnstileService.verifyToken(turnstileToken, clientIP || undefined)\n\n      if (!isValid) {\n        return new Response(JSON.stringify({\n          success: false,\n          error: '人机验证失败，请重试'\n        }), {\n          status: 400,\n          headers: { 'Content-Type': 'application/json' }\n        })\n      }\n\n      // 验证通过，继续处理请求\n      // 重新构造请求，因为body已经被读取\n      const newRequest = new Request(request.url, {\n        method: request.method,\n        headers: request.headers,\n        body: JSON.stringify(body)\n      })\n\n      return next()\n    } catch (error) {\n      console.error('Turnstile middleware error:', error)\n      return new Response(JSON.stringify({\n        success: false,\n        error: '验证过程中发生错误'\n      }), {\n        status: 500,\n        headers: { 'Content-Type': 'application/json' }\n      })\n    }\n  }\n}\n", "import type { Env, JWTPayload, AuthenticationError, AuthorizationError } from '@/types'\nimport { JWTService } from '@/modules/auth/jwt.service'\nimport { DatabaseService } from '@/modules/shared/database.service'\n\nexport interface AuthenticatedRequest extends Request {\n  user?: JWTPayload\n}\n\nexport function createAuthMiddleware(env: Env) {\n  const dbService = new DatabaseService(env.DB)\n  const jwtService = new JWTService(env, dbService)\n\n  return {\n    // 验证JWT token\n    async authenticate(request: Request): Promise<{ request: AuthenticatedRequest; user: JWTPayload }> {\n      const authHeader = request.headers.get('Authorization')\n      \n      if (!authHeader || !authHeader.startsWith('Bearer ')) {\n        throw new Response(JSON.stringify({\n          success: false,\n          error: '缺少认证令牌'\n        }), {\n          status: 401,\n          headers: { 'Content-Type': 'application/json' }\n        })\n      }\n\n      const token = authHeader.substring(7) // 移除 'Bearer ' 前缀\n      const payload = await jwtService.verifyJWT(token)\n\n      if (!payload || payload.type !== 'access') {\n        throw new Response(JSON.stringify({\n          success: false,\n          error: '无效的认证令牌'\n        }), {\n          status: 401,\n          headers: { 'Content-Type': 'application/json' }\n        })\n      }\n\n      // 检查用户是否仍然存在且活跃\n      const user = await dbService.getUserById(payload.userId)\n      if (!user || !user.is_active) {\n        throw new Response(JSON.stringify({\n          success: false,\n          error: '用户账户不存在或已被禁用'\n        }), {\n          status: 401,\n          headers: { 'Content-Type': 'application/json' }\n        })\n      }\n\n      // 将用户信息附加到请求对象\n      const authenticatedRequest = request as AuthenticatedRequest\n      authenticatedRequest.user = payload\n\n      return { request: authenticatedRequest, user: payload }\n    },\n\n    // 验证管理员权限\n    async requireAdmin(request: Request): Promise<{ request: AuthenticatedRequest; user: JWTPayload }> {\n      const { request: authRequest, user } = await this.authenticate(request)\n\n      if (user.role !== 'admin') {\n        throw new Response(JSON.stringify({\n          success: false,\n          error: '需要管理员权限'\n        }), {\n          status: 403,\n          headers: { 'Content-Type': 'application/json' }\n        })\n      }\n\n      return { request: authRequest, user }\n    },\n\n    // 可选认证（不强制要求登录）\n    async optionalAuth(request: Request): Promise<{ request: AuthenticatedRequest; user?: JWTPayload }> {\n      try {\n        const { request: authRequest, user } = await this.authenticate(request)\n        return { request: authRequest, user }\n      } catch (error) {\n        // 认证失败时不抛出错误，只是不设置用户信息\n        const authenticatedRequest = request as AuthenticatedRequest\n        return { request: authenticatedRequest, user: undefined }\n      }\n    }\n  }\n}\n\n// 创建认证装饰器函数\nexport function withAuth(env: Env) {\n  const authMiddleware = createAuthMiddleware(env)\n\n  return function authDecorator(\n    handler: (request: AuthenticatedRequest, user: JWTPayload, env: Env) => Promise<Response>\n  ) {\n    return async function(request: Request): Promise<Response> {\n      try {\n        const { request: authRequest, user } = await authMiddleware.authenticate(request)\n        return await handler(authRequest, user, env)\n      } catch (error) {\n        if (error instanceof Response) {\n          return error\n        }\n        \n        console.error('Auth middleware error:', error)\n        return new Response(JSON.stringify({\n          success: false,\n          error: '认证过程中发生错误'\n        }), {\n          status: 500,\n          headers: { 'Content-Type': 'application/json' }\n        })\n      }\n    }\n  }\n}\n\n// 创建管理员权限装饰器函数\nexport function withAdminAuth(env: Env) {\n  const authMiddleware = createAuthMiddleware(env)\n\n  return function adminAuthDecorator(\n    handler: (request: AuthenticatedRequest, user: JWTPayload, env: Env) => Promise<Response>\n  ) {\n    return async function(request: Request): Promise<Response> {\n      try {\n        const { request: authRequest, user } = await authMiddleware.requireAdmin(request)\n        return await handler(authRequest, user, env)\n      } catch (error) {\n        if (error instanceof Response) {\n          return error\n        }\n        \n        console.error('Admin auth middleware error:', error)\n        return new Response(JSON.stringify({\n          success: false,\n          error: '权限验证过程中发生错误'\n        }), {\n          status: 500,\n          headers: { 'Content-Type': 'application/json' }\n        })\n      }\n    }\n  }\n}\n\n// 创建可选认证装饰器函数\nexport function withOptionalAuth(env: Env) {\n  const authMiddleware = createAuthMiddleware(env)\n\n  return function optionalAuthDecorator(\n    handler: (request: AuthenticatedRequest, user: JWTPayload | undefined, env: Env) => Promise<Response>\n  ) {\n    return async function(request: Request): Promise<Response> {\n      try {\n        const { request: authRequest, user } = await authMiddleware.optionalAuth(request)\n        return await handler(authRequest, user, env)\n      } catch (error) {\n        console.error('Optional auth middleware error:', error)\n        return new Response(JSON.stringify({\n          success: false,\n          error: '认证过程中发生错误'\n        }), {\n          status: 500,\n          headers: { 'Content-Type': 'application/json' }\n        })\n      }\n    }\n  }\n}\n", "import type { Env, LoginRequest, RegisterRequest, ApiResponse } from '@/types'\nimport { AuthService } from '@/modules/auth/auth.service'\nimport { DatabaseService } from '@/modules/shared/database.service'\nimport { TurnstileService } from '@/middleware/turnstile.middleware'\nimport { withAuth, type AuthenticatedRequest } from '@/middleware/auth.middleware'\nimport type { JWTPayload } from '@/types'\n\nexport class AuthHandler {\n  private authService: AuthService\n  private turnstileService: TurnstileService\n  public getCurrentUser: (request: Request) => Promise<Response>\n  public changePassword: (request: Request) => Promise<Response>\n\n  constructor(private env: Env) {\n    const dbService = new DatabaseService(env.DB)\n    this.authService = new AuthService(env, dbService)\n    this.turnstileService = new TurnstileService(env)\n\n    // 初始化需要认证的方法\n    this.getCurrentUser = withAuth(this.env)((request: AuthenticatedRequest, user: JWTPayload) => {\n      return this.handleGetCurrentUser(request, user)\n    })\n\n    this.changePassword = withAuth(this.env)((request: AuthenticatedRequest, user: JWTPayload) => {\n      return this.handleChangePassword(request, user)\n    })\n  }\n\n  async register(request: Request): Promise<Response> {\n    try {\n      const data: RegisterRequest = await request.json()\n\n      // 验证Turnstile\n      const clientIP = request.headers.get('CF-Connecting-IP') || \n                      request.headers.get('X-Forwarded-For')\n      \n      const isTurnstileValid = await this.turnstileService.verifyToken(\n        data.turnstileToken, \n        clientIP || undefined\n      )\n\n      if (!isTurnstileValid) {\n        return this.errorResponse('人机验证失败', 400)\n      }\n\n      // 注册用户\n      const result = await this.authService.register(data)\n\n      return this.successResponse(result, '注册成功')\n    } catch (error: any) {\n      console.error('Register error:', error)\n      return this.errorResponse(error.message || '注册失败', error.statusCode || 500)\n    }\n  }\n\n  async login(request: Request): Promise<Response> {\n    try {\n      const data: LoginRequest = await request.json()\n\n      // 验证Turnstile\n      const clientIP = request.headers.get('CF-Connecting-IP') || \n                      request.headers.get('X-Forwarded-For')\n      \n      const isTurnstileValid = await this.turnstileService.verifyToken(\n        data.turnstileToken, \n        clientIP || undefined\n      )\n\n      if (!isTurnstileValid) {\n        return this.errorResponse('人机验证失败', 400)\n      }\n\n      // 用户登录\n      const result = await this.authService.login(data)\n\n      return this.successResponse(result, '登录成功')\n    } catch (error: any) {\n      console.error('Login error:', error)\n      return this.errorResponse(error.message || '登录失败', error.statusCode || 500)\n    }\n  }\n\n  async refreshToken(request: Request): Promise<Response> {\n    try {\n      const { refreshToken } = await request.json() as any\n\n      if (!refreshToken) {\n        return this.errorResponse('缺少刷新令牌', 400)\n      }\n\n      const tokens = await this.authService.refreshTokens(refreshToken)\n\n      return this.successResponse(tokens, '令牌刷新成功')\n    } catch (error: any) {\n      console.error('Refresh token error:', error)\n      return this.errorResponse(error.message || '令牌刷新失败', error.statusCode || 401)\n    }\n  }\n\n  async logout(request: Request): Promise<Response> {\n    try {\n      const { refreshToken } = await request.json() as any\n\n      if (refreshToken) {\n        await this.authService.logout(refreshToken)\n      }\n\n      return this.successResponse(null, '登出成功')\n    } catch (error: any) {\n      console.error('Logout error:', error)\n      return this.errorResponse(error.message || '登出失败', error.statusCode || 500)\n    }\n  }\n\n  // 需要认证的路由处理器已在构造函数中初始化\n\n  private async handleGetCurrentUser(request: AuthenticatedRequest, user: JWTPayload): Promise<Response> {\n    try {\n      const currentUser = await this.authService.getCurrentUser(user.userId)\n      return this.successResponse(currentUser)\n    } catch (error: any) {\n      console.error('Get current user error:', error)\n      return this.errorResponse(error.message || '获取用户信息失败', error.statusCode || 500)\n    }\n  }\n\n  private async handleChangePassword(request: AuthenticatedRequest, user: JWTPayload): Promise<Response> {\n    try {\n      const { currentPassword, newPassword, confirmPassword } = await request.json() as any\n\n      if (!currentPassword || !newPassword || !confirmPassword) {\n        return this.errorResponse('缺少必要参数', 400)\n      }\n\n      if (newPassword !== confirmPassword) {\n        return this.errorResponse('新密码确认不一致', 400)\n      }\n\n      await this.authService.changePassword(user.userId, currentPassword, newPassword)\n\n      return this.successResponse(null, '密码修改成功')\n    } catch (error: any) {\n      console.error('Change password error:', error)\n      return this.errorResponse(error.message || '密码修改失败', error.statusCode || 500)\n    }\n  }\n\n  private successResponse<T>(data: T, message?: string): Response {\n    const response: ApiResponse<T> = {\n      success: true,\n      data,\n      message\n    }\n\n    return new Response(JSON.stringify(response), {\n      status: 200,\n      headers: {\n        'Content-Type': 'application/json',\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n      }\n    })\n  }\n\n  private errorResponse(error: string, status: number = 500): Response {\n    const response: ApiResponse = {\n      success: false,\n      error\n    }\n\n    return new Response(JSON.stringify(response), {\n      status,\n      headers: {\n        'Content-Type': 'application/json',\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n      }\n    })\n  }\n}\n", "export const textEncoder = new TextEncoder();\n\nconst base64Chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\n// Use a lookup table to find the index.\nconst base64Lookup = new Uint8Array(256);\nfor (var i = 0; i < base64Chars.length; i++) {\n    base64Lookup[base64Chars.charCodeAt(i)] = i;\n}\n\nexport function decodeBase64(base64) {\n    let bufferLength = Math.ceil(base64.length / 4) * 3;\n    const len = base64.length;\n\n    let p = 0;\n\n    if (base64.length % 4 === 3) {\n        bufferLength--;\n    } else if (base64.length % 4 === 2) {\n        bufferLength -= 2;\n    } else if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n\n    const arrayBuffer = new ArrayBuffer(bufferLength);\n    const bytes = new Uint8Array(arrayBuffer);\n\n    for (let i = 0; i < len; i += 4) {\n        let encoded1 = base64Lookup[base64.charCodeAt(i)];\n        let encoded2 = base64Lookup[base64.charCodeAt(i + 1)];\n        let encoded3 = base64Lookup[base64.charCodeAt(i + 2)];\n        let encoded4 = base64Lookup[base64.charCodeAt(i + 3)];\n\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n\n    return arrayBuffer;\n}\n\nexport function getDecoder(charset) {\n    charset = charset || 'utf8';\n    let decoder;\n\n    try {\n        decoder = new TextDecoder(charset);\n    } catch (err) {\n        decoder = new TextDecoder('windows-1252');\n    }\n\n    return decoder;\n}\n\n/**\n * Converts a Blob into an ArrayBuffer\n * @param {Blob} blob Blob to convert\n * @returns {ArrayBuffer} Converted value\n */\nexport async function blobToArrayBuffer(blob) {\n    if ('arrayBuffer' in blob) {\n        return await blob.arrayBuffer();\n    }\n\n    const fr = new FileReader();\n\n    return new Promise((resolve, reject) => {\n        fr.onload = function (e) {\n            resolve(e.target.result);\n        };\n\n        fr.onerror = function (e) {\n            reject(fr.error);\n        };\n\n        fr.readAsArrayBuffer(blob);\n    });\n}\n\nexport function getHex(c) {\n    if ((c >= 0x30 /* 0 */ && c <= 0x39) /* 9 */ || (c >= 0x61 /* a */ && c <= 0x66) /* f */ || (c >= 0x41 /* A */ && c <= 0x46) /* F */) {\n        return String.fromCharCode(c);\n    }\n    return false;\n}\n\n/**\n * Decode a complete mime word encoded string\n *\n * @param {String} str Mime word encoded string\n * @return {String} Decoded unicode string\n */\nexport function decodeWord(charset, encoding, str) {\n    // RFC2231 added language tag to the encoding\n    // see: https://tools.ietf.org/html/rfc2231#section-5\n    // this implementation silently ignores this tag\n    let splitPos = charset.indexOf('*');\n    if (splitPos >= 0) {\n        charset = charset.substr(0, splitPos);\n    }\n\n    encoding = encoding.toUpperCase();\n\n    let byteStr;\n\n    if (encoding === 'Q') {\n        str = str\n            // remove spaces between = and hex char, this might indicate invalidly applied line splitting\n            .replace(/=\\s+([0-9a-fA-F])/g, '=$1')\n            // convert all underscores to spaces\n            .replace(/[_\\s]/g, ' ');\n\n        let buf = textEncoder.encode(str);\n        let encodedBytes = [];\n        for (let i = 0, len = buf.length; i < len; i++) {\n            let c = buf[i];\n            if (i <= len - 2 && c === 0x3d /* = */) {\n                let c1 = getHex(buf[i + 1]);\n                let c2 = getHex(buf[i + 2]);\n                if (c1 && c2) {\n                    let c = parseInt(c1 + c2, 16);\n                    encodedBytes.push(c);\n                    i += 2;\n                    continue;\n                }\n            }\n            encodedBytes.push(c);\n        }\n        byteStr = new ArrayBuffer(encodedBytes.length);\n        let dataView = new DataView(byteStr);\n        for (let i = 0, len = encodedBytes.length; i < len; i++) {\n            dataView.setUint8(i, encodedBytes[i]);\n        }\n    } else if (encoding === 'B') {\n        byteStr = decodeBase64(str.replace(/[^a-zA-Z0-9\\+\\/=]+/g, ''));\n    } else {\n        // keep as is, convert ArrayBuffer to unicode string, assume utf8\n        byteStr = textEncoder.encode(str);\n    }\n\n    return getDecoder(charset).decode(byteStr);\n}\n\nexport function decodeWords(str) {\n    let joinString = true;\n    let done = false;\n\n    while (!done) {\n        let result = (str || '')\n            .toString()\n            // find base64 words that can be joined\n            .replace(/(=\\?([^?]+)\\?[Bb]\\?([^?]*)\\?=)\\s*(?==\\?([^?]+)\\?[Bb]\\?[^?]*\\?=)/g, (match, left, chLeft, encodedLeftStr, chRight) => {\n                if (!joinString) {\n                    return match;\n                }\n                // only mark b64 chunks to be joined if charsets match and left side does not end with =\n                if (chLeft === chRight && encodedLeftStr.length % 4 === 0 && !/=$/.test(encodedLeftStr)) {\n                    // set a joiner marker\n                    return left + '__\\x00JOIN\\x00__';\n                }\n\n                return match;\n            })\n            // find QP words that can be joined\n            .replace(/(=\\?([^?]+)\\?[Qq]\\?[^?]*\\?=)\\s*(?==\\?([^?]+)\\?[Qq]\\?[^?]*\\?=)/g, (match, left, chLeft, chRight) => {\n                if (!joinString) {\n                    return match;\n                }\n                // only mark QP chunks to be joined if charsets match\n                if (chLeft === chRight) {\n                    // set a joiner marker\n                    return left + '__\\x00JOIN\\x00__';\n                }\n                return match;\n            })\n            // join base64 encoded words\n            .replace(/(\\?=)?__\\x00JOIN\\x00__(=\\?([^?]+)\\?[QqBb]\\?)?/g, '')\n            // remove spaces between mime encoded words\n            .replace(/(=\\?[^?]+\\?[QqBb]\\?[^?]*\\?=)\\s+(?==\\?[^?]+\\?[QqBb]\\?[^?]*\\?=)/g, '$1')\n            // decode words\n            .replace(/=\\?([\\w_\\-*]+)\\?([QqBb])\\?([^?]*)\\?=/g, (m, charset, encoding, text) => decodeWord(charset, encoding, text));\n\n        if (joinString && result.indexOf('\\ufffd') >= 0) {\n            // text contains \\ufffd (EF BF BD), so unicode conversion failed, retry without joining strings\n            joinString = false;\n        } else {\n            return result;\n        }\n    }\n}\n\nexport function decodeURIComponentWithCharset(encodedStr, charset) {\n    charset = charset || 'utf-8';\n\n    let encodedBytes = [];\n    for (let i = 0; i < encodedStr.length; i++) {\n        let c = encodedStr.charAt(i);\n        if (c === '%' && /^[a-f0-9]{2}/i.test(encodedStr.substr(i + 1, 2))) {\n            // encoded sequence\n            let byte = encodedStr.substr(i + 1, 2);\n            i += 2;\n            encodedBytes.push(parseInt(byte, 16));\n        } else if (c.charCodeAt(0) > 126) {\n            c = textEncoder.encode(c);\n            for (let j = 0; j < c.length; j++) {\n                encodedBytes.push(c[j]);\n            }\n        } else {\n            // \"normal\" char\n            encodedBytes.push(c.charCodeAt(0));\n        }\n    }\n\n    const byteStr = new ArrayBuffer(encodedBytes.length);\n    const dataView = new DataView(byteStr);\n    for (let i = 0, len = encodedBytes.length; i < len; i++) {\n        dataView.setUint8(i, encodedBytes[i]);\n    }\n\n    return getDecoder(charset).decode(byteStr);\n}\n\nexport function decodeParameterValueContinuations(header) {\n    // handle parameter value continuations\n    // https://tools.ietf.org/html/rfc2231#section-3\n\n    // preprocess values\n    let paramKeys = new Map();\n\n    Object.keys(header.params).forEach(key => {\n        let match = key.match(/\\*((\\d+)\\*?)?$/);\n        if (!match) {\n            // nothing to do here, does not seem like a continuation param\n            return;\n        }\n\n        let actualKey = key.substr(0, match.index).toLowerCase();\n        let nr = Number(match[2]) || 0;\n\n        let paramVal;\n        if (!paramKeys.has(actualKey)) {\n            paramVal = {\n                charset: false,\n                values: []\n            };\n            paramKeys.set(actualKey, paramVal);\n        } else {\n            paramVal = paramKeys.get(actualKey);\n        }\n\n        let value = header.params[key];\n        if (nr === 0 && match[0].charAt(match[0].length - 1) === '*' && (match = value.match(/^([^']*)'[^']*'(.*)$/))) {\n            paramVal.charset = match[1] || 'utf-8';\n            value = match[2];\n        }\n\n        paramVal.values.push({ nr, value });\n\n        // remove the old reference\n        delete header.params[key];\n    });\n\n    paramKeys.forEach((paramVal, key) => {\n        header.params[key] = decodeURIComponentWithCharset(\n            paramVal.values\n                .sort((a, b) => a.nr - b.nr)\n                .map(a => a.value)\n                .join(''),\n            paramVal.charset\n        );\n    });\n}\n", "import { blobToArrayBuffer } from './decode-strings.js';\n\nexport default class PassThroughDecoder {\n    constructor() {\n        this.chunks = [];\n    }\n\n    update(line) {\n        this.chunks.push(line);\n        this.chunks.push('\\n');\n    }\n\n    finalize() {\n        // convert an array of arraybuffers into a blob and then back into a single arraybuffer\n        return blobToArrayBuffer(new Blob(this.chunks, { type: 'application/octet-stream' }));\n    }\n}\n", "import { decodeBase64, blobToArrayBuffer } from './decode-strings.js';\n\nexport default class Base64Decoder {\n    constructor(opts) {\n        opts = opts || {};\n\n        this.decoder = opts.decoder || new TextDecoder();\n\n        this.maxChunkSize = 100 * 1024;\n\n        this.chunks = [];\n\n        this.remainder = '';\n    }\n\n    update(buffer) {\n        let str = this.decoder.decode(buffer);\n\n        if (/[^a-zA-Z0-9+\\/]/.test(str)) {\n            str = str.replace(/[^a-zA-Z0-9+\\/]+/g, '');\n        }\n\n        this.remainder += str;\n\n        if (this.remainder.length >= this.maxChunkSize) {\n            let allowedBytes = Math.floor(this.remainder.length / 4) * 4;\n            let base64Str;\n\n            if (allowedBytes === this.remainder.length) {\n                base64Str = this.remainder;\n                this.remainder = '';\n            } else {\n                base64Str = this.remainder.substr(0, allowedBytes);\n                this.remainder = this.remainder.substr(allowedBytes);\n            }\n\n            if (base64Str.length) {\n                this.chunks.push(decodeBase64(base64Str));\n            }\n        }\n    }\n\n    finalize() {\n        if (this.remainder && !/^=+$/.test(this.remainder)) {\n            this.chunks.push(decodeBase64(this.remainder));\n        }\n\n        return blobToArrayBuffer(new Blob(this.chunks, { type: 'application/octet-stream' }));\n    }\n}\n", "import { blobToArrayBuffer } from './decode-strings.js';\n\nexport default class QPDecoder {\n    constructor(opts) {\n        opts = opts || {};\n\n        this.decoder = opts.decoder || new TextDecoder();\n\n        this.maxChunkSize = 100 * 1024;\n\n        this.remainder = '';\n\n        this.chunks = [];\n    }\n\n    decodeQPBytes(encodedBytes) {\n        let buf = new ArrayBuffer(encodedBytes.length);\n        let dataView = new DataView(buf);\n        for (let i = 0, len = encodedBytes.length; i < len; i++) {\n            dataView.setUint8(i, parseInt(encodedBytes[i], 16));\n        }\n        return buf;\n    }\n\n    decodeChunks(str) {\n        // unwrap newlines\n        str = str.replace(/=\\r?\\n/g, '');\n\n        let list = str.split(/(?==)/);\n        let encodedBytes = [];\n        for (let part of list) {\n            if (part.charAt(0) !== '=') {\n                if (encodedBytes.length) {\n                    this.chunks.push(this.decodeQPBytes(encodedBytes));\n                    encodedBytes = [];\n                }\n                this.chunks.push(part);\n                continue;\n            }\n\n            if (part.length === 3) {\n                encodedBytes.push(part.substr(1));\n                continue;\n            }\n\n            if (part.length > 3) {\n                encodedBytes.push(part.substr(1, 2));\n                this.chunks.push(this.decodeQPBytes(encodedBytes));\n                encodedBytes = [];\n\n                part = part.substr(3);\n                this.chunks.push(part);\n            }\n        }\n        if (encodedBytes.length) {\n            this.chunks.push(this.decodeQPBytes(encodedBytes));\n            encodedBytes = [];\n        }\n    }\n\n    update(buffer) {\n        // expect full lines, so add line terminator as well\n        let str = this.decoder.decode(buffer) + '\\n';\n\n        str = this.remainder + str;\n\n        if (str.length < this.maxChunkSize) {\n            this.remainder = str;\n            return;\n        }\n\n        this.remainder = '';\n\n        let partialEnding = str.match(/=[a-fA-F0-9]?$/);\n        if (partialEnding) {\n            if (partialEnding.index === 0) {\n                this.remainder = str;\n                return;\n            }\n            this.remainder = str.substr(partialEnding.index);\n            str = str.substr(0, partialEnding.index);\n        }\n\n        this.decodeChunks(str);\n    }\n\n    finalize() {\n        if (this.remainder.length) {\n            this.decodeChunks(this.remainder);\n            this.remainder = '';\n        }\n\n        // convert an array of arraybuffers into a blob and then back into a single arraybuffer\n        return blobToArrayBuffer(new Blob(this.chunks, { type: 'application/octet-stream' }));\n    }\n}\n", "import { getDecoder, decodeParameterValueContinuations, textEncoder } from './decode-strings.js';\nimport PassThroughDecoder from './pass-through-decoder.js';\nimport Base64Decoder from './base64-decoder.js';\nimport QPDecoder from './qp-decoder.js';\n\nexport default class MimeNode {\n    constructor(opts) {\n        opts = opts || {};\n\n        this.postalMime = opts.postalMime;\n\n        this.root = !!opts.parentNode;\n        this.childNodes = [];\n        if (opts.parentNode) {\n            opts.parentNode.childNodes.push(this);\n        }\n\n        this.state = 'header';\n\n        this.headerLines = [];\n\n        this.contentType = {\n            value: 'text/plain',\n            default: true\n        };\n\n        this.contentTransferEncoding = {\n            value: '8bit'\n        };\n\n        this.contentDisposition = {\n            value: ''\n        };\n\n        this.headers = [];\n\n        this.contentDecoder = false;\n    }\n\n    setupContentDecoder(transferEncoding) {\n        if (/base64/i.test(transferEncoding)) {\n            this.contentDecoder = new Base64Decoder();\n        } else if (/quoted-printable/i.test(transferEncoding)) {\n            this.contentDecoder = new QPDecoder({ decoder: getDecoder(this.contentType.parsed.params.charset) });\n        } else {\n            this.contentDecoder = new PassThroughDecoder();\n        }\n    }\n\n    async finalize() {\n        if (this.state === 'finished') {\n            return;\n        }\n\n        if (this.state === 'header') {\n            this.processHeaders();\n        }\n\n        // remove self from boundary listing\n        let boundaries = this.postalMime.boundaries;\n        for (let i = boundaries.length - 1; i >= 0; i--) {\n            let boundary = boundaries[i];\n            if (boundary.node === this) {\n                boundaries.splice(i, 1);\n                break;\n            }\n        }\n\n        await this.finalizeChildNodes();\n\n        this.content = this.contentDecoder ? await this.contentDecoder.finalize() : null;\n\n        this.state = 'finished';\n    }\n\n    async finalizeChildNodes() {\n        for (let childNode of this.childNodes) {\n            await childNode.finalize();\n        }\n    }\n\n    parseStructuredHeader(str) {\n        let response = {\n            value: false,\n            params: {}\n        };\n\n        let key = false;\n        let value = '';\n        let stage = 'value';\n\n        let quote = false;\n        let escaped = false;\n        let chr;\n\n        for (let i = 0, len = str.length; i < len; i++) {\n            chr = str.charAt(i);\n            switch (stage) {\n                case 'key':\n                    if (chr === '=') {\n                        key = value.trim().toLowerCase();\n                        stage = 'value';\n                        value = '';\n                        break;\n                    }\n                    value += chr;\n                    break;\n                case 'value':\n                    if (escaped) {\n                        value += chr;\n                    } else if (chr === '\\\\') {\n                        escaped = true;\n                        continue;\n                    } else if (quote && chr === quote) {\n                        quote = false;\n                    } else if (!quote && chr === '\"') {\n                        quote = chr;\n                    } else if (!quote && chr === ';') {\n                        if (key === false) {\n                            response.value = value.trim();\n                        } else {\n                            response.params[key] = value.trim();\n                        }\n                        stage = 'key';\n                        value = '';\n                    } else {\n                        value += chr;\n                    }\n                    escaped = false;\n                    break;\n            }\n        }\n\n        // finalize remainder\n        value = value.trim();\n        if (stage === 'value') {\n            if (key === false) {\n                // default value\n                response.value = value;\n            } else {\n                // subkey value\n                response.params[key] = value;\n            }\n        } else if (value) {\n            // treat as key without value, see emptykey:\n            // Header-Key: somevalue; key=value; emptykey\n            response.params[value.toLowerCase()] = '';\n        }\n\n        if (response.value) {\n            response.value = response.value.toLowerCase();\n        }\n\n        // convert Parameter Value Continuations into single strings\n        decodeParameterValueContinuations(response);\n\n        return response;\n    }\n\n    decodeFlowedText(str, delSp) {\n        return (\n            str\n                .split(/\\r?\\n/)\n                // remove soft linebreaks\n                // soft linebreaks are added after space symbols\n                .reduce((previousValue, currentValue) => {\n                    if (/ $/.test(previousValue) && !/(^|\\n)-- $/.test(previousValue)) {\n                        if (delSp) {\n                            // delsp adds space to text to be able to fold it\n                            // these spaces can be removed once the text is unfolded\n                            return previousValue.slice(0, -1) + currentValue;\n                        } else {\n                            return previousValue + currentValue;\n                        }\n                    } else {\n                        return previousValue + '\\n' + currentValue;\n                    }\n                })\n                // remove whitespace stuffing\n                // http://tools.ietf.org/html/rfc3676#section-4.4\n                .replace(/^ /gm, '')\n        );\n    }\n\n    getTextContent() {\n        if (!this.content) {\n            return '';\n        }\n\n        let str = getDecoder(this.contentType.parsed.params.charset).decode(this.content);\n\n        if (/^flowed$/i.test(this.contentType.parsed.params.format)) {\n            str = this.decodeFlowedText(str, /^yes$/i.test(this.contentType.parsed.params.delsp));\n        }\n\n        return str;\n    }\n\n    processHeaders() {\n        for (let i = this.headerLines.length - 1; i >= 0; i--) {\n            let line = this.headerLines[i];\n            if (i && /^\\s/.test(line)) {\n                this.headerLines[i - 1] += '\\n' + line;\n                this.headerLines.splice(i, 1);\n            } else {\n                // remove folding and extra WS\n                line = line.replace(/\\s+/g, ' ');\n                let sep = line.indexOf(':');\n                let key = sep < 0 ? line.trim() : line.substr(0, sep).trim();\n                let value = sep < 0 ? '' : line.substr(sep + 1).trim();\n                this.headers.push({ key: key.toLowerCase(), originalKey: key, value });\n\n                switch (key.toLowerCase()) {\n                    case 'content-type':\n                        if (this.contentType.default) {\n                            this.contentType = { value, parsed: {} };\n                        }\n                        break;\n                    case 'content-transfer-encoding':\n                        this.contentTransferEncoding = { value, parsed: {} };\n                        break;\n                    case 'content-disposition':\n                        this.contentDisposition = { value, parsed: {} };\n                        break;\n                    case 'content-id':\n                        this.contentId = value;\n                        break;\n                    case 'content-description':\n                        this.contentDescription = value;\n                        break;\n                }\n            }\n        }\n\n        this.contentType.parsed = this.parseStructuredHeader(this.contentType.value);\n        this.contentType.multipart = /^multipart\\//i.test(this.contentType.parsed.value)\n            ? this.contentType.parsed.value.substr(this.contentType.parsed.value.indexOf('/') + 1)\n            : false;\n\n        if (this.contentType.multipart && this.contentType.parsed.params.boundary) {\n            // add self to boundary terminator listing\n            this.postalMime.boundaries.push({\n                value: textEncoder.encode(this.contentType.parsed.params.boundary),\n                node: this\n            });\n        }\n\n        this.contentDisposition.parsed = this.parseStructuredHeader(this.contentDisposition.value);\n\n        this.contentTransferEncoding.encoding = this.contentTransferEncoding.value\n            .toLowerCase()\n            .split(/[^\\w-]/)\n            .shift();\n\n        this.setupContentDecoder(this.contentTransferEncoding.encoding);\n    }\n\n    feed(line) {\n        switch (this.state) {\n            case 'header':\n                if (!line.length) {\n                    this.state = 'body';\n                    return this.processHeaders();\n                }\n                this.headerLines.push(getDecoder().decode(line));\n                break;\n            case 'body': {\n                // add line to body\n                this.contentDecoder.update(line);\n            }\n        }\n    }\n}\n", "// Entity map from https://html.spec.whatwg.org/multipage/named-characters.html#named-character-references\nexport const htmlEntities = {\n    '&AElig': '\\u00C6',\n    '&AElig;': '\\u00C6',\n    '&AMP': '\\u0026',\n    '&AMP;': '\\u0026',\n    '&Aacute': '\\u00C1',\n    '&Aacute;': '\\u00C1',\n    '&Abreve;': '\\u0102',\n    '&Acirc': '\\u00C2',\n    '&Acirc;': '\\u00C2',\n    '&Acy;': '\\u0410',\n    '&Afr;': '\\uD835\\uDD04',\n    '&Agrave': '\\u00C0',\n    '&Agrave;': '\\u00C0',\n    '&Alpha;': '\\u0391',\n    '&Amacr;': '\\u0100',\n    '&And;': '\\u2A53',\n    '&Aogon;': '\\u0104',\n    '&Aopf;': '\\uD835\\uDD38',\n    '&ApplyFunction;': '\\u2061',\n    '&Aring': '\\u00C5',\n    '&Aring;': '\\u00C5',\n    '&Ascr;': '\\uD835\\uDC9C',\n    '&Assign;': '\\u2254',\n    '&Atilde': '\\u00C3',\n    '&Atilde;': '\\u00C3',\n    '&Auml': '\\u00C4',\n    '&Auml;': '\\u00C4',\n    '&Backslash;': '\\u2216',\n    '&Barv;': '\\u2AE7',\n    '&Barwed;': '\\u2306',\n    '&Bcy;': '\\u0411',\n    '&Because;': '\\u2235',\n    '&Bernoullis;': '\\u212C',\n    '&Beta;': '\\u0392',\n    '&Bfr;': '\\uD835\\uDD05',\n    '&Bopf;': '\\uD835\\uDD39',\n    '&Breve;': '\\u02D8',\n    '&Bscr;': '\\u212C',\n    '&Bumpeq;': '\\u224E',\n    '&CHcy;': '\\u0427',\n    '&COPY': '\\u00A9',\n    '&COPY;': '\\u00A9',\n    '&Cacute;': '\\u0106',\n    '&Cap;': '\\u22D2',\n    '&CapitalDifferentialD;': '\\u2145',\n    '&Cayleys;': '\\u212D',\n    '&Ccaron;': '\\u010C',\n    '&Ccedil': '\\u00C7',\n    '&Ccedil;': '\\u00C7',\n    '&Ccirc;': '\\u0108',\n    '&Cconint;': '\\u2230',\n    '&Cdot;': '\\u010A',\n    '&Cedilla;': '\\u00B8',\n    '&CenterDot;': '\\u00B7',\n    '&Cfr;': '\\u212D',\n    '&Chi;': '\\u03A7',\n    '&CircleDot;': '\\u2299',\n    '&CircleMinus;': '\\u2296',\n    '&CirclePlus;': '\\u2295',\n    '&CircleTimes;': '\\u2297',\n    '&ClockwiseContourIntegral;': '\\u2232',\n    '&CloseCurlyDoubleQuote;': '\\u201D',\n    '&CloseCurlyQuote;': '\\u2019',\n    '&Colon;': '\\u2237',\n    '&Colone;': '\\u2A74',\n    '&Congruent;': '\\u2261',\n    '&Conint;': '\\u222F',\n    '&ContourIntegral;': '\\u222E',\n    '&Copf;': '\\u2102',\n    '&Coproduct;': '\\u2210',\n    '&CounterClockwiseContourIntegral;': '\\u2233',\n    '&Cross;': '\\u2A2F',\n    '&Cscr;': '\\uD835\\uDC9E',\n    '&Cup;': '\\u22D3',\n    '&CupCap;': '\\u224D',\n    '&DD;': '\\u2145',\n    '&DDotrahd;': '\\u2911',\n    '&DJcy;': '\\u0402',\n    '&DScy;': '\\u0405',\n    '&DZcy;': '\\u040F',\n    '&Dagger;': '\\u2021',\n    '&Darr;': '\\u21A1',\n    '&Dashv;': '\\u2AE4',\n    '&Dcaron;': '\\u010E',\n    '&Dcy;': '\\u0414',\n    '&Del;': '\\u2207',\n    '&Delta;': '\\u0394',\n    '&Dfr;': '\\uD835\\uDD07',\n    '&DiacriticalAcute;': '\\u00B4',\n    '&DiacriticalDot;': '\\u02D9',\n    '&DiacriticalDoubleAcute;': '\\u02DD',\n    '&DiacriticalGrave;': '\\u0060',\n    '&DiacriticalTilde;': '\\u02DC',\n    '&Diamond;': '\\u22C4',\n    '&DifferentialD;': '\\u2146',\n    '&Dopf;': '\\uD835\\uDD3B',\n    '&Dot;': '\\u00A8',\n    '&DotDot;': '\\u20DC',\n    '&DotEqual;': '\\u2250',\n    '&DoubleContourIntegral;': '\\u222F',\n    '&DoubleDot;': '\\u00A8',\n    '&DoubleDownArrow;': '\\u21D3',\n    '&DoubleLeftArrow;': '\\u21D0',\n    '&DoubleLeftRightArrow;': '\\u21D4',\n    '&DoubleLeftTee;': '\\u2AE4',\n    '&DoubleLongLeftArrow;': '\\u27F8',\n    '&DoubleLongLeftRightArrow;': '\\u27FA',\n    '&DoubleLongRightArrow;': '\\u27F9',\n    '&DoubleRightArrow;': '\\u21D2',\n    '&DoubleRightTee;': '\\u22A8',\n    '&DoubleUpArrow;': '\\u21D1',\n    '&DoubleUpDownArrow;': '\\u21D5',\n    '&DoubleVerticalBar;': '\\u2225',\n    '&DownArrow;': '\\u2193',\n    '&DownArrowBar;': '\\u2913',\n    '&DownArrowUpArrow;': '\\u21F5',\n    '&DownBreve;': '\\u0311',\n    '&DownLeftRightVector;': '\\u2950',\n    '&DownLeftTeeVector;': '\\u295E',\n    '&DownLeftVector;': '\\u21BD',\n    '&DownLeftVectorBar;': '\\u2956',\n    '&DownRightTeeVector;': '\\u295F',\n    '&DownRightVector;': '\\u21C1',\n    '&DownRightVectorBar;': '\\u2957',\n    '&DownTee;': '\\u22A4',\n    '&DownTeeArrow;': '\\u21A7',\n    '&Downarrow;': '\\u21D3',\n    '&Dscr;': '\\uD835\\uDC9F',\n    '&Dstrok;': '\\u0110',\n    '&ENG;': '\\u014A',\n    '&ETH': '\\u00D0',\n    '&ETH;': '\\u00D0',\n    '&Eacute': '\\u00C9',\n    '&Eacute;': '\\u00C9',\n    '&Ecaron;': '\\u011A',\n    '&Ecirc': '\\u00CA',\n    '&Ecirc;': '\\u00CA',\n    '&Ecy;': '\\u042D',\n    '&Edot;': '\\u0116',\n    '&Efr;': '\\uD835\\uDD08',\n    '&Egrave': '\\u00C8',\n    '&Egrave;': '\\u00C8',\n    '&Element;': '\\u2208',\n    '&Emacr;': '\\u0112',\n    '&EmptySmallSquare;': '\\u25FB',\n    '&EmptyVerySmallSquare;': '\\u25AB',\n    '&Eogon;': '\\u0118',\n    '&Eopf;': '\\uD835\\uDD3C',\n    '&Epsilon;': '\\u0395',\n    '&Equal;': '\\u2A75',\n    '&EqualTilde;': '\\u2242',\n    '&Equilibrium;': '\\u21CC',\n    '&Escr;': '\\u2130',\n    '&Esim;': '\\u2A73',\n    '&Eta;': '\\u0397',\n    '&Euml': '\\u00CB',\n    '&Euml;': '\\u00CB',\n    '&Exists;': '\\u2203',\n    '&ExponentialE;': '\\u2147',\n    '&Fcy;': '\\u0424',\n    '&Ffr;': '\\uD835\\uDD09',\n    '&FilledSmallSquare;': '\\u25FC',\n    '&FilledVerySmallSquare;': '\\u25AA',\n    '&Fopf;': '\\uD835\\uDD3D',\n    '&ForAll;': '\\u2200',\n    '&Fouriertrf;': '\\u2131',\n    '&Fscr;': '\\u2131',\n    '&GJcy;': '\\u0403',\n    '&GT': '\\u003E',\n    '&GT;': '\\u003E',\n    '&Gamma;': '\\u0393',\n    '&Gammad;': '\\u03DC',\n    '&Gbreve;': '\\u011E',\n    '&Gcedil;': '\\u0122',\n    '&Gcirc;': '\\u011C',\n    '&Gcy;': '\\u0413',\n    '&Gdot;': '\\u0120',\n    '&Gfr;': '\\uD835\\uDD0A',\n    '&Gg;': '\\u22D9',\n    '&Gopf;': '\\uD835\\uDD3E',\n    '&GreaterEqual;': '\\u2265',\n    '&GreaterEqualLess;': '\\u22DB',\n    '&GreaterFullEqual;': '\\u2267',\n    '&GreaterGreater;': '\\u2AA2',\n    '&GreaterLess;': '\\u2277',\n    '&GreaterSlantEqual;': '\\u2A7E',\n    '&GreaterTilde;': '\\u2273',\n    '&Gscr;': '\\uD835\\uDCA2',\n    '&Gt;': '\\u226B',\n    '&HARDcy;': '\\u042A',\n    '&Hacek;': '\\u02C7',\n    '&Hat;': '\\u005E',\n    '&Hcirc;': '\\u0124',\n    '&Hfr;': '\\u210C',\n    '&HilbertSpace;': '\\u210B',\n    '&Hopf;': '\\u210D',\n    '&HorizontalLine;': '\\u2500',\n    '&Hscr;': '\\u210B',\n    '&Hstrok;': '\\u0126',\n    '&HumpDownHump;': '\\u224E',\n    '&HumpEqual;': '\\u224F',\n    '&IEcy;': '\\u0415',\n    '&IJlig;': '\\u0132',\n    '&IOcy;': '\\u0401',\n    '&Iacute': '\\u00CD',\n    '&Iacute;': '\\u00CD',\n    '&Icirc': '\\u00CE',\n    '&Icirc;': '\\u00CE',\n    '&Icy;': '\\u0418',\n    '&Idot;': '\\u0130',\n    '&Ifr;': '\\u2111',\n    '&Igrave': '\\u00CC',\n    '&Igrave;': '\\u00CC',\n    '&Im;': '\\u2111',\n    '&Imacr;': '\\u012A',\n    '&ImaginaryI;': '\\u2148',\n    '&Implies;': '\\u21D2',\n    '&Int;': '\\u222C',\n    '&Integral;': '\\u222B',\n    '&Intersection;': '\\u22C2',\n    '&InvisibleComma;': '\\u2063',\n    '&InvisibleTimes;': '\\u2062',\n    '&Iogon;': '\\u012E',\n    '&Iopf;': '\\uD835\\uDD40',\n    '&Iota;': '\\u0399',\n    '&Iscr;': '\\u2110',\n    '&Itilde;': '\\u0128',\n    '&Iukcy;': '\\u0406',\n    '&Iuml': '\\u00CF',\n    '&Iuml;': '\\u00CF',\n    '&Jcirc;': '\\u0134',\n    '&Jcy;': '\\u0419',\n    '&Jfr;': '\\uD835\\uDD0D',\n    '&Jopf;': '\\uD835\\uDD41',\n    '&Jscr;': '\\uD835\\uDCA5',\n    '&Jsercy;': '\\u0408',\n    '&Jukcy;': '\\u0404',\n    '&KHcy;': '\\u0425',\n    '&KJcy;': '\\u040C',\n    '&Kappa;': '\\u039A',\n    '&Kcedil;': '\\u0136',\n    '&Kcy;': '\\u041A',\n    '&Kfr;': '\\uD835\\uDD0E',\n    '&Kopf;': '\\uD835\\uDD42',\n    '&Kscr;': '\\uD835\\uDCA6',\n    '&LJcy;': '\\u0409',\n    '&LT': '\\u003C',\n    '&LT;': '\\u003C',\n    '&Lacute;': '\\u0139',\n    '&Lambda;': '\\u039B',\n    '&Lang;': '\\u27EA',\n    '&Laplacetrf;': '\\u2112',\n    '&Larr;': '\\u219E',\n    '&Lcaron;': '\\u013D',\n    '&Lcedil;': '\\u013B',\n    '&Lcy;': '\\u041B',\n    '&LeftAngleBracket;': '\\u27E8',\n    '&LeftArrow;': '\\u2190',\n    '&LeftArrowBar;': '\\u21E4',\n    '&LeftArrowRightArrow;': '\\u21C6',\n    '&LeftCeiling;': '\\u2308',\n    '&LeftDoubleBracket;': '\\u27E6',\n    '&LeftDownTeeVector;': '\\u2961',\n    '&LeftDownVector;': '\\u21C3',\n    '&LeftDownVectorBar;': '\\u2959',\n    '&LeftFloor;': '\\u230A',\n    '&LeftRightArrow;': '\\u2194',\n    '&LeftRightVector;': '\\u294E',\n    '&LeftTee;': '\\u22A3',\n    '&LeftTeeArrow;': '\\u21A4',\n    '&LeftTeeVector;': '\\u295A',\n    '&LeftTriangle;': '\\u22B2',\n    '&LeftTriangleBar;': '\\u29CF',\n    '&LeftTriangleEqual;': '\\u22B4',\n    '&LeftUpDownVector;': '\\u2951',\n    '&LeftUpTeeVector;': '\\u2960',\n    '&LeftUpVector;': '\\u21BF',\n    '&LeftUpVectorBar;': '\\u2958',\n    '&LeftVector;': '\\u21BC',\n    '&LeftVectorBar;': '\\u2952',\n    '&Leftarrow;': '\\u21D0',\n    '&Leftrightarrow;': '\\u21D4',\n    '&LessEqualGreater;': '\\u22DA',\n    '&LessFullEqual;': '\\u2266',\n    '&LessGreater;': '\\u2276',\n    '&LessLess;': '\\u2AA1',\n    '&LessSlantEqual;': '\\u2A7D',\n    '&LessTilde;': '\\u2272',\n    '&Lfr;': '\\uD835\\uDD0F',\n    '&Ll;': '\\u22D8',\n    '&Lleftarrow;': '\\u21DA',\n    '&Lmidot;': '\\u013F',\n    '&LongLeftArrow;': '\\u27F5',\n    '&LongLeftRightArrow;': '\\u27F7',\n    '&LongRightArrow;': '\\u27F6',\n    '&Longleftarrow;': '\\u27F8',\n    '&Longleftrightarrow;': '\\u27FA',\n    '&Longrightarrow;': '\\u27F9',\n    '&Lopf;': '\\uD835\\uDD43',\n    '&LowerLeftArrow;': '\\u2199',\n    '&LowerRightArrow;': '\\u2198',\n    '&Lscr;': '\\u2112',\n    '&Lsh;': '\\u21B0',\n    '&Lstrok;': '\\u0141',\n    '&Lt;': '\\u226A',\n    '&Map;': '\\u2905',\n    '&Mcy;': '\\u041C',\n    '&MediumSpace;': '\\u205F',\n    '&Mellintrf;': '\\u2133',\n    '&Mfr;': '\\uD835\\uDD10',\n    '&MinusPlus;': '\\u2213',\n    '&Mopf;': '\\uD835\\uDD44',\n    '&Mscr;': '\\u2133',\n    '&Mu;': '\\u039C',\n    '&NJcy;': '\\u040A',\n    '&Nacute;': '\\u0143',\n    '&Ncaron;': '\\u0147',\n    '&Ncedil;': '\\u0145',\n    '&Ncy;': '\\u041D',\n    '&NegativeMediumSpace;': '\\u200B',\n    '&NegativeThickSpace;': '\\u200B',\n    '&NegativeThinSpace;': '\\u200B',\n    '&NegativeVeryThinSpace;': '\\u200B',\n    '&NestedGreaterGreater;': '\\u226B',\n    '&NestedLessLess;': '\\u226A',\n    '&NewLine;': '\\u000A',\n    '&Nfr;': '\\uD835\\uDD11',\n    '&NoBreak;': '\\u2060',\n    '&NonBreakingSpace;': '\\u00A0',\n    '&Nopf;': '\\u2115',\n    '&Not;': '\\u2AEC',\n    '&NotCongruent;': '\\u2262',\n    '&NotCupCap;': '\\u226D',\n    '&NotDoubleVerticalBar;': '\\u2226',\n    '&NotElement;': '\\u2209',\n    '&NotEqual;': '\\u2260',\n    '&NotEqualTilde;': '\\u2242\\u0338',\n    '&NotExists;': '\\u2204',\n    '&NotGreater;': '\\u226F',\n    '&NotGreaterEqual;': '\\u2271',\n    '&NotGreaterFullEqual;': '\\u2267\\u0338',\n    '&NotGreaterGreater;': '\\u226B\\u0338',\n    '&NotGreaterLess;': '\\u2279',\n    '&NotGreaterSlantEqual;': '\\u2A7E\\u0338',\n    '&NotGreaterTilde;': '\\u2275',\n    '&NotHumpDownHump;': '\\u224E\\u0338',\n    '&NotHumpEqual;': '\\u224F\\u0338',\n    '&NotLeftTriangle;': '\\u22EA',\n    '&NotLeftTriangleBar;': '\\u29CF\\u0338',\n    '&NotLeftTriangleEqual;': '\\u22EC',\n    '&NotLess;': '\\u226E',\n    '&NotLessEqual;': '\\u2270',\n    '&NotLessGreater;': '\\u2278',\n    '&NotLessLess;': '\\u226A\\u0338',\n    '&NotLessSlantEqual;': '\\u2A7D\\u0338',\n    '&NotLessTilde;': '\\u2274',\n    '&NotNestedGreaterGreater;': '\\u2AA2\\u0338',\n    '&NotNestedLessLess;': '\\u2AA1\\u0338',\n    '&NotPrecedes;': '\\u2280',\n    '&NotPrecedesEqual;': '\\u2AAF\\u0338',\n    '&NotPrecedesSlantEqual;': '\\u22E0',\n    '&NotReverseElement;': '\\u220C',\n    '&NotRightTriangle;': '\\u22EB',\n    '&NotRightTriangleBar;': '\\u29D0\\u0338',\n    '&NotRightTriangleEqual;': '\\u22ED',\n    '&NotSquareSubset;': '\\u228F\\u0338',\n    '&NotSquareSubsetEqual;': '\\u22E2',\n    '&NotSquareSuperset;': '\\u2290\\u0338',\n    '&NotSquareSupersetEqual;': '\\u22E3',\n    '&NotSubset;': '\\u2282\\u20D2',\n    '&NotSubsetEqual;': '\\u2288',\n    '&NotSucceeds;': '\\u2281',\n    '&NotSucceedsEqual;': '\\u2AB0\\u0338',\n    '&NotSucceedsSlantEqual;': '\\u22E1',\n    '&NotSucceedsTilde;': '\\u227F\\u0338',\n    '&NotSuperset;': '\\u2283\\u20D2',\n    '&NotSupersetEqual;': '\\u2289',\n    '&NotTilde;': '\\u2241',\n    '&NotTildeEqual;': '\\u2244',\n    '&NotTildeFullEqual;': '\\u2247',\n    '&NotTildeTilde;': '\\u2249',\n    '&NotVerticalBar;': '\\u2224',\n    '&Nscr;': '\\uD835\\uDCA9',\n    '&Ntilde': '\\u00D1',\n    '&Ntilde;': '\\u00D1',\n    '&Nu;': '\\u039D',\n    '&OElig;': '\\u0152',\n    '&Oacute': '\\u00D3',\n    '&Oacute;': '\\u00D3',\n    '&Ocirc': '\\u00D4',\n    '&Ocirc;': '\\u00D4',\n    '&Ocy;': '\\u041E',\n    '&Odblac;': '\\u0150',\n    '&Ofr;': '\\uD835\\uDD12',\n    '&Ograve': '\\u00D2',\n    '&Ograve;': '\\u00D2',\n    '&Omacr;': '\\u014C',\n    '&Omega;': '\\u03A9',\n    '&Omicron;': '\\u039F',\n    '&Oopf;': '\\uD835\\uDD46',\n    '&OpenCurlyDoubleQuote;': '\\u201C',\n    '&OpenCurlyQuote;': '\\u2018',\n    '&Or;': '\\u2A54',\n    '&Oscr;': '\\uD835\\uDCAA',\n    '&Oslash': '\\u00D8',\n    '&Oslash;': '\\u00D8',\n    '&Otilde': '\\u00D5',\n    '&Otilde;': '\\u00D5',\n    '&Otimes;': '\\u2A37',\n    '&Ouml': '\\u00D6',\n    '&Ouml;': '\\u00D6',\n    '&OverBar;': '\\u203E',\n    '&OverBrace;': '\\u23DE',\n    '&OverBracket;': '\\u23B4',\n    '&OverParenthesis;': '\\u23DC',\n    '&PartialD;': '\\u2202',\n    '&Pcy;': '\\u041F',\n    '&Pfr;': '\\uD835\\uDD13',\n    '&Phi;': '\\u03A6',\n    '&Pi;': '\\u03A0',\n    '&PlusMinus;': '\\u00B1',\n    '&Poincareplane;': '\\u210C',\n    '&Popf;': '\\u2119',\n    '&Pr;': '\\u2ABB',\n    '&Precedes;': '\\u227A',\n    '&PrecedesEqual;': '\\u2AAF',\n    '&PrecedesSlantEqual;': '\\u227C',\n    '&PrecedesTilde;': '\\u227E',\n    '&Prime;': '\\u2033',\n    '&Product;': '\\u220F',\n    '&Proportion;': '\\u2237',\n    '&Proportional;': '\\u221D',\n    '&Pscr;': '\\uD835\\uDCAB',\n    '&Psi;': '\\u03A8',\n    '&QUOT': '\\u0022',\n    '&QUOT;': '\\u0022',\n    '&Qfr;': '\\uD835\\uDD14',\n    '&Qopf;': '\\u211A',\n    '&Qscr;': '\\uD835\\uDCAC',\n    '&RBarr;': '\\u2910',\n    '&REG': '\\u00AE',\n    '&REG;': '\\u00AE',\n    '&Racute;': '\\u0154',\n    '&Rang;': '\\u27EB',\n    '&Rarr;': '\\u21A0',\n    '&Rarrtl;': '\\u2916',\n    '&Rcaron;': '\\u0158',\n    '&Rcedil;': '\\u0156',\n    '&Rcy;': '\\u0420',\n    '&Re;': '\\u211C',\n    '&ReverseElement;': '\\u220B',\n    '&ReverseEquilibrium;': '\\u21CB',\n    '&ReverseUpEquilibrium;': '\\u296F',\n    '&Rfr;': '\\u211C',\n    '&Rho;': '\\u03A1',\n    '&RightAngleBracket;': '\\u27E9',\n    '&RightArrow;': '\\u2192',\n    '&RightArrowBar;': '\\u21E5',\n    '&RightArrowLeftArrow;': '\\u21C4',\n    '&RightCeiling;': '\\u2309',\n    '&RightDoubleBracket;': '\\u27E7',\n    '&RightDownTeeVector;': '\\u295D',\n    '&RightDownVector;': '\\u21C2',\n    '&RightDownVectorBar;': '\\u2955',\n    '&RightFloor;': '\\u230B',\n    '&RightTee;': '\\u22A2',\n    '&RightTeeArrow;': '\\u21A6',\n    '&RightTeeVector;': '\\u295B',\n    '&RightTriangle;': '\\u22B3',\n    '&RightTriangleBar;': '\\u29D0',\n    '&RightTriangleEqual;': '\\u22B5',\n    '&RightUpDownVector;': '\\u294F',\n    '&RightUpTeeVector;': '\\u295C',\n    '&RightUpVector;': '\\u21BE',\n    '&RightUpVectorBar;': '\\u2954',\n    '&RightVector;': '\\u21C0',\n    '&RightVectorBar;': '\\u2953',\n    '&Rightarrow;': '\\u21D2',\n    '&Ropf;': '\\u211D',\n    '&RoundImplies;': '\\u2970',\n    '&Rrightarrow;': '\\u21DB',\n    '&Rscr;': '\\u211B',\n    '&Rsh;': '\\u21B1',\n    '&RuleDelayed;': '\\u29F4',\n    '&SHCHcy;': '\\u0429',\n    '&SHcy;': '\\u0428',\n    '&SOFTcy;': '\\u042C',\n    '&Sacute;': '\\u015A',\n    '&Sc;': '\\u2ABC',\n    '&Scaron;': '\\u0160',\n    '&Scedil;': '\\u015E',\n    '&Scirc;': '\\u015C',\n    '&Scy;': '\\u0421',\n    '&Sfr;': '\\uD835\\uDD16',\n    '&ShortDownArrow;': '\\u2193',\n    '&ShortLeftArrow;': '\\u2190',\n    '&ShortRightArrow;': '\\u2192',\n    '&ShortUpArrow;': '\\u2191',\n    '&Sigma;': '\\u03A3',\n    '&SmallCircle;': '\\u2218',\n    '&Sopf;': '\\uD835\\uDD4A',\n    '&Sqrt;': '\\u221A',\n    '&Square;': '\\u25A1',\n    '&SquareIntersection;': '\\u2293',\n    '&SquareSubset;': '\\u228F',\n    '&SquareSubsetEqual;': '\\u2291',\n    '&SquareSuperset;': '\\u2290',\n    '&SquareSupersetEqual;': '\\u2292',\n    '&SquareUnion;': '\\u2294',\n    '&Sscr;': '\\uD835\\uDCAE',\n    '&Star;': '\\u22C6',\n    '&Sub;': '\\u22D0',\n    '&Subset;': '\\u22D0',\n    '&SubsetEqual;': '\\u2286',\n    '&Succeeds;': '\\u227B',\n    '&SucceedsEqual;': '\\u2AB0',\n    '&SucceedsSlantEqual;': '\\u227D',\n    '&SucceedsTilde;': '\\u227F',\n    '&SuchThat;': '\\u220B',\n    '&Sum;': '\\u2211',\n    '&Sup;': '\\u22D1',\n    '&Superset;': '\\u2283',\n    '&SupersetEqual;': '\\u2287',\n    '&Supset;': '\\u22D1',\n    '&THORN': '\\u00DE',\n    '&THORN;': '\\u00DE',\n    '&TRADE;': '\\u2122',\n    '&TSHcy;': '\\u040B',\n    '&TScy;': '\\u0426',\n    '&Tab;': '\\u0009',\n    '&Tau;': '\\u03A4',\n    '&Tcaron;': '\\u0164',\n    '&Tcedil;': '\\u0162',\n    '&Tcy;': '\\u0422',\n    '&Tfr;': '\\uD835\\uDD17',\n    '&Therefore;': '\\u2234',\n    '&Theta;': '\\u0398',\n    '&ThickSpace;': '\\u205F\\u200A',\n    '&ThinSpace;': '\\u2009',\n    '&Tilde;': '\\u223C',\n    '&TildeEqual;': '\\u2243',\n    '&TildeFullEqual;': '\\u2245',\n    '&TildeTilde;': '\\u2248',\n    '&Topf;': '\\uD835\\uDD4B',\n    '&TripleDot;': '\\u20DB',\n    '&Tscr;': '\\uD835\\uDCAF',\n    '&Tstrok;': '\\u0166',\n    '&Uacute': '\\u00DA',\n    '&Uacute;': '\\u00DA',\n    '&Uarr;': '\\u219F',\n    '&Uarrocir;': '\\u2949',\n    '&Ubrcy;': '\\u040E',\n    '&Ubreve;': '\\u016C',\n    '&Ucirc': '\\u00DB',\n    '&Ucirc;': '\\u00DB',\n    '&Ucy;': '\\u0423',\n    '&Udblac;': '\\u0170',\n    '&Ufr;': '\\uD835\\uDD18',\n    '&Ugrave': '\\u00D9',\n    '&Ugrave;': '\\u00D9',\n    '&Umacr;': '\\u016A',\n    '&UnderBar;': '\\u005F',\n    '&UnderBrace;': '\\u23DF',\n    '&UnderBracket;': '\\u23B5',\n    '&UnderParenthesis;': '\\u23DD',\n    '&Union;': '\\u22C3',\n    '&UnionPlus;': '\\u228E',\n    '&Uogon;': '\\u0172',\n    '&Uopf;': '\\uD835\\uDD4C',\n    '&UpArrow;': '\\u2191',\n    '&UpArrowBar;': '\\u2912',\n    '&UpArrowDownArrow;': '\\u21C5',\n    '&UpDownArrow;': '\\u2195',\n    '&UpEquilibrium;': '\\u296E',\n    '&UpTee;': '\\u22A5',\n    '&UpTeeArrow;': '\\u21A5',\n    '&Uparrow;': '\\u21D1',\n    '&Updownarrow;': '\\u21D5',\n    '&UpperLeftArrow;': '\\u2196',\n    '&UpperRightArrow;': '\\u2197',\n    '&Upsi;': '\\u03D2',\n    '&Upsilon;': '\\u03A5',\n    '&Uring;': '\\u016E',\n    '&Uscr;': '\\uD835\\uDCB0',\n    '&Utilde;': '\\u0168',\n    '&Uuml': '\\u00DC',\n    '&Uuml;': '\\u00DC',\n    '&VDash;': '\\u22AB',\n    '&Vbar;': '\\u2AEB',\n    '&Vcy;': '\\u0412',\n    '&Vdash;': '\\u22A9',\n    '&Vdashl;': '\\u2AE6',\n    '&Vee;': '\\u22C1',\n    '&Verbar;': '\\u2016',\n    '&Vert;': '\\u2016',\n    '&VerticalBar;': '\\u2223',\n    '&VerticalLine;': '\\u007C',\n    '&VerticalSeparator;': '\\u2758',\n    '&VerticalTilde;': '\\u2240',\n    '&VeryThinSpace;': '\\u200A',\n    '&Vfr;': '\\uD835\\uDD19',\n    '&Vopf;': '\\uD835\\uDD4D',\n    '&Vscr;': '\\uD835\\uDCB1',\n    '&Vvdash;': '\\u22AA',\n    '&Wcirc;': '\\u0174',\n    '&Wedge;': '\\u22C0',\n    '&Wfr;': '\\uD835\\uDD1A',\n    '&Wopf;': '\\uD835\\uDD4E',\n    '&Wscr;': '\\uD835\\uDCB2',\n    '&Xfr;': '\\uD835\\uDD1B',\n    '&Xi;': '\\u039E',\n    '&Xopf;': '\\uD835\\uDD4F',\n    '&Xscr;': '\\uD835\\uDCB3',\n    '&YAcy;': '\\u042F',\n    '&YIcy;': '\\u0407',\n    '&YUcy;': '\\u042E',\n    '&Yacute': '\\u00DD',\n    '&Yacute;': '\\u00DD',\n    '&Ycirc;': '\\u0176',\n    '&Ycy;': '\\u042B',\n    '&Yfr;': '\\uD835\\uDD1C',\n    '&Yopf;': '\\uD835\\uDD50',\n    '&Yscr;': '\\uD835\\uDCB4',\n    '&Yuml;': '\\u0178',\n    '&ZHcy;': '\\u0416',\n    '&Zacute;': '\\u0179',\n    '&Zcaron;': '\\u017D',\n    '&Zcy;': '\\u0417',\n    '&Zdot;': '\\u017B',\n    '&ZeroWidthSpace;': '\\u200B',\n    '&Zeta;': '\\u0396',\n    '&Zfr;': '\\u2128',\n    '&Zopf;': '\\u2124',\n    '&Zscr;': '\\uD835\\uDCB5',\n    '&aacute': '\\u00E1',\n    '&aacute;': '\\u00E1',\n    '&abreve;': '\\u0103',\n    '&ac;': '\\u223E',\n    '&acE;': '\\u223E\\u0333',\n    '&acd;': '\\u223F',\n    '&acirc': '\\u00E2',\n    '&acirc;': '\\u00E2',\n    '&acute': '\\u00B4',\n    '&acute;': '\\u00B4',\n    '&acy;': '\\u0430',\n    '&aelig': '\\u00E6',\n    '&aelig;': '\\u00E6',\n    '&af;': '\\u2061',\n    '&afr;': '\\uD835\\uDD1E',\n    '&agrave': '\\u00E0',\n    '&agrave;': '\\u00E0',\n    '&alefsym;': '\\u2135',\n    '&aleph;': '\\u2135',\n    '&alpha;': '\\u03B1',\n    '&amacr;': '\\u0101',\n    '&amalg;': '\\u2A3F',\n    '&amp': '\\u0026',\n    '&amp;': '\\u0026',\n    '&and;': '\\u2227',\n    '&andand;': '\\u2A55',\n    '&andd;': '\\u2A5C',\n    '&andslope;': '\\u2A58',\n    '&andv;': '\\u2A5A',\n    '&ang;': '\\u2220',\n    '&ange;': '\\u29A4',\n    '&angle;': '\\u2220',\n    '&angmsd;': '\\u2221',\n    '&angmsdaa;': '\\u29A8',\n    '&angmsdab;': '\\u29A9',\n    '&angmsdac;': '\\u29AA',\n    '&angmsdad;': '\\u29AB',\n    '&angmsdae;': '\\u29AC',\n    '&angmsdaf;': '\\u29AD',\n    '&angmsdag;': '\\u29AE',\n    '&angmsdah;': '\\u29AF',\n    '&angrt;': '\\u221F',\n    '&angrtvb;': '\\u22BE',\n    '&angrtvbd;': '\\u299D',\n    '&angsph;': '\\u2222',\n    '&angst;': '\\u00C5',\n    '&angzarr;': '\\u237C',\n    '&aogon;': '\\u0105',\n    '&aopf;': '\\uD835\\uDD52',\n    '&ap;': '\\u2248',\n    '&apE;': '\\u2A70',\n    '&apacir;': '\\u2A6F',\n    '&ape;': '\\u224A',\n    '&apid;': '\\u224B',\n    '&apos;': '\\u0027',\n    '&approx;': '\\u2248',\n    '&approxeq;': '\\u224A',\n    '&aring': '\\u00E5',\n    '&aring;': '\\u00E5',\n    '&ascr;': '\\uD835\\uDCB6',\n    '&ast;': '\\u002A',\n    '&asymp;': '\\u2248',\n    '&asympeq;': '\\u224D',\n    '&atilde': '\\u00E3',\n    '&atilde;': '\\u00E3',\n    '&auml': '\\u00E4',\n    '&auml;': '\\u00E4',\n    '&awconint;': '\\u2233',\n    '&awint;': '\\u2A11',\n    '&bNot;': '\\u2AED',\n    '&backcong;': '\\u224C',\n    '&backepsilon;': '\\u03F6',\n    '&backprime;': '\\u2035',\n    '&backsim;': '\\u223D',\n    '&backsimeq;': '\\u22CD',\n    '&barvee;': '\\u22BD',\n    '&barwed;': '\\u2305',\n    '&barwedge;': '\\u2305',\n    '&bbrk;': '\\u23B5',\n    '&bbrktbrk;': '\\u23B6',\n    '&bcong;': '\\u224C',\n    '&bcy;': '\\u0431',\n    '&bdquo;': '\\u201E',\n    '&becaus;': '\\u2235',\n    '&because;': '\\u2235',\n    '&bemptyv;': '\\u29B0',\n    '&bepsi;': '\\u03F6',\n    '&bernou;': '\\u212C',\n    '&beta;': '\\u03B2',\n    '&beth;': '\\u2136',\n    '&between;': '\\u226C',\n    '&bfr;': '\\uD835\\uDD1F',\n    '&bigcap;': '\\u22C2',\n    '&bigcirc;': '\\u25EF',\n    '&bigcup;': '\\u22C3',\n    '&bigodot;': '\\u2A00',\n    '&bigoplus;': '\\u2A01',\n    '&bigotimes;': '\\u2A02',\n    '&bigsqcup;': '\\u2A06',\n    '&bigstar;': '\\u2605',\n    '&bigtriangledown;': '\\u25BD',\n    '&bigtriangleup;': '\\u25B3',\n    '&biguplus;': '\\u2A04',\n    '&bigvee;': '\\u22C1',\n    '&bigwedge;': '\\u22C0',\n    '&bkarow;': '\\u290D',\n    '&blacklozenge;': '\\u29EB',\n    '&blacksquare;': '\\u25AA',\n    '&blacktriangle;': '\\u25B4',\n    '&blacktriangledown;': '\\u25BE',\n    '&blacktriangleleft;': '\\u25C2',\n    '&blacktriangleright;': '\\u25B8',\n    '&blank;': '\\u2423',\n    '&blk12;': '\\u2592',\n    '&blk14;': '\\u2591',\n    '&blk34;': '\\u2593',\n    '&block;': '\\u2588',\n    '&bne;': '\\u003D\\u20E5',\n    '&bnequiv;': '\\u2261\\u20E5',\n    '&bnot;': '\\u2310',\n    '&bopf;': '\\uD835\\uDD53',\n    '&bot;': '\\u22A5',\n    '&bottom;': '\\u22A5',\n    '&bowtie;': '\\u22C8',\n    '&boxDL;': '\\u2557',\n    '&boxDR;': '\\u2554',\n    '&boxDl;': '\\u2556',\n    '&boxDr;': '\\u2553',\n    '&boxH;': '\\u2550',\n    '&boxHD;': '\\u2566',\n    '&boxHU;': '\\u2569',\n    '&boxHd;': '\\u2564',\n    '&boxHu;': '\\u2567',\n    '&boxUL;': '\\u255D',\n    '&boxUR;': '\\u255A',\n    '&boxUl;': '\\u255C',\n    '&boxUr;': '\\u2559',\n    '&boxV;': '\\u2551',\n    '&boxVH;': '\\u256C',\n    '&boxVL;': '\\u2563',\n    '&boxVR;': '\\u2560',\n    '&boxVh;': '\\u256B',\n    '&boxVl;': '\\u2562',\n    '&boxVr;': '\\u255F',\n    '&boxbox;': '\\u29C9',\n    '&boxdL;': '\\u2555',\n    '&boxdR;': '\\u2552',\n    '&boxdl;': '\\u2510',\n    '&boxdr;': '\\u250C',\n    '&boxh;': '\\u2500',\n    '&boxhD;': '\\u2565',\n    '&boxhU;': '\\u2568',\n    '&boxhd;': '\\u252C',\n    '&boxhu;': '\\u2534',\n    '&boxminus;': '\\u229F',\n    '&boxplus;': '\\u229E',\n    '&boxtimes;': '\\u22A0',\n    '&boxuL;': '\\u255B',\n    '&boxuR;': '\\u2558',\n    '&boxul;': '\\u2518',\n    '&boxur;': '\\u2514',\n    '&boxv;': '\\u2502',\n    '&boxvH;': '\\u256A',\n    '&boxvL;': '\\u2561',\n    '&boxvR;': '\\u255E',\n    '&boxvh;': '\\u253C',\n    '&boxvl;': '\\u2524',\n    '&boxvr;': '\\u251C',\n    '&bprime;': '\\u2035',\n    '&breve;': '\\u02D8',\n    '&brvbar': '\\u00A6',\n    '&brvbar;': '\\u00A6',\n    '&bscr;': '\\uD835\\uDCB7',\n    '&bsemi;': '\\u204F',\n    '&bsim;': '\\u223D',\n    '&bsime;': '\\u22CD',\n    '&bsol;': '\\u005C',\n    '&bsolb;': '\\u29C5',\n    '&bsolhsub;': '\\u27C8',\n    '&bull;': '\\u2022',\n    '&bullet;': '\\u2022',\n    '&bump;': '\\u224E',\n    '&bumpE;': '\\u2AAE',\n    '&bumpe;': '\\u224F',\n    '&bumpeq;': '\\u224F',\n    '&cacute;': '\\u0107',\n    '&cap;': '\\u2229',\n    '&capand;': '\\u2A44',\n    '&capbrcup;': '\\u2A49',\n    '&capcap;': '\\u2A4B',\n    '&capcup;': '\\u2A47',\n    '&capdot;': '\\u2A40',\n    '&caps;': '\\u2229\\uFE00',\n    '&caret;': '\\u2041',\n    '&caron;': '\\u02C7',\n    '&ccaps;': '\\u2A4D',\n    '&ccaron;': '\\u010D',\n    '&ccedil': '\\u00E7',\n    '&ccedil;': '\\u00E7',\n    '&ccirc;': '\\u0109',\n    '&ccups;': '\\u2A4C',\n    '&ccupssm;': '\\u2A50',\n    '&cdot;': '\\u010B',\n    '&cedil': '\\u00B8',\n    '&cedil;': '\\u00B8',\n    '&cemptyv;': '\\u29B2',\n    '&cent': '\\u00A2',\n    '&cent;': '\\u00A2',\n    '&centerdot;': '\\u00B7',\n    '&cfr;': '\\uD835\\uDD20',\n    '&chcy;': '\\u0447',\n    '&check;': '\\u2713',\n    '&checkmark;': '\\u2713',\n    '&chi;': '\\u03C7',\n    '&cir;': '\\u25CB',\n    '&cirE;': '\\u29C3',\n    '&circ;': '\\u02C6',\n    '&circeq;': '\\u2257',\n    '&circlearrowleft;': '\\u21BA',\n    '&circlearrowright;': '\\u21BB',\n    '&circledR;': '\\u00AE',\n    '&circledS;': '\\u24C8',\n    '&circledast;': '\\u229B',\n    '&circledcirc;': '\\u229A',\n    '&circleddash;': '\\u229D',\n    '&cire;': '\\u2257',\n    '&cirfnint;': '\\u2A10',\n    '&cirmid;': '\\u2AEF',\n    '&cirscir;': '\\u29C2',\n    '&clubs;': '\\u2663',\n    '&clubsuit;': '\\u2663',\n    '&colon;': '\\u003A',\n    '&colone;': '\\u2254',\n    '&coloneq;': '\\u2254',\n    '&comma;': '\\u002C',\n    '&commat;': '\\u0040',\n    '&comp;': '\\u2201',\n    '&compfn;': '\\u2218',\n    '&complement;': '\\u2201',\n    '&complexes;': '\\u2102',\n    '&cong;': '\\u2245',\n    '&congdot;': '\\u2A6D',\n    '&conint;': '\\u222E',\n    '&copf;': '\\uD835\\uDD54',\n    '&coprod;': '\\u2210',\n    '&copy': '\\u00A9',\n    '&copy;': '\\u00A9',\n    '&copysr;': '\\u2117',\n    '&crarr;': '\\u21B5',\n    '&cross;': '\\u2717',\n    '&cscr;': '\\uD835\\uDCB8',\n    '&csub;': '\\u2ACF',\n    '&csube;': '\\u2AD1',\n    '&csup;': '\\u2AD0',\n    '&csupe;': '\\u2AD2',\n    '&ctdot;': '\\u22EF',\n    '&cudarrl;': '\\u2938',\n    '&cudarrr;': '\\u2935',\n    '&cuepr;': '\\u22DE',\n    '&cuesc;': '\\u22DF',\n    '&cularr;': '\\u21B6',\n    '&cularrp;': '\\u293D',\n    '&cup;': '\\u222A',\n    '&cupbrcap;': '\\u2A48',\n    '&cupcap;': '\\u2A46',\n    '&cupcup;': '\\u2A4A',\n    '&cupdot;': '\\u228D',\n    '&cupor;': '\\u2A45',\n    '&cups;': '\\u222A\\uFE00',\n    '&curarr;': '\\u21B7',\n    '&curarrm;': '\\u293C',\n    '&curlyeqprec;': '\\u22DE',\n    '&curlyeqsucc;': '\\u22DF',\n    '&curlyvee;': '\\u22CE',\n    '&curlywedge;': '\\u22CF',\n    '&curren': '\\u00A4',\n    '&curren;': '\\u00A4',\n    '&curvearrowleft;': '\\u21B6',\n    '&curvearrowright;': '\\u21B7',\n    '&cuvee;': '\\u22CE',\n    '&cuwed;': '\\u22CF',\n    '&cwconint;': '\\u2232',\n    '&cwint;': '\\u2231',\n    '&cylcty;': '\\u232D',\n    '&dArr;': '\\u21D3',\n    '&dHar;': '\\u2965',\n    '&dagger;': '\\u2020',\n    '&daleth;': '\\u2138',\n    '&darr;': '\\u2193',\n    '&dash;': '\\u2010',\n    '&dashv;': '\\u22A3',\n    '&dbkarow;': '\\u290F',\n    '&dblac;': '\\u02DD',\n    '&dcaron;': '\\u010F',\n    '&dcy;': '\\u0434',\n    '&dd;': '\\u2146',\n    '&ddagger;': '\\u2021',\n    '&ddarr;': '\\u21CA',\n    '&ddotseq;': '\\u2A77',\n    '&deg': '\\u00B0',\n    '&deg;': '\\u00B0',\n    '&delta;': '\\u03B4',\n    '&demptyv;': '\\u29B1',\n    '&dfisht;': '\\u297F',\n    '&dfr;': '\\uD835\\uDD21',\n    '&dharl;': '\\u21C3',\n    '&dharr;': '\\u21C2',\n    '&diam;': '\\u22C4',\n    '&diamond;': '\\u22C4',\n    '&diamondsuit;': '\\u2666',\n    '&diams;': '\\u2666',\n    '&die;': '\\u00A8',\n    '&digamma;': '\\u03DD',\n    '&disin;': '\\u22F2',\n    '&div;': '\\u00F7',\n    '&divide': '\\u00F7',\n    '&divide;': '\\u00F7',\n    '&divideontimes;': '\\u22C7',\n    '&divonx;': '\\u22C7',\n    '&djcy;': '\\u0452',\n    '&dlcorn;': '\\u231E',\n    '&dlcrop;': '\\u230D',\n    '&dollar;': '\\u0024',\n    '&dopf;': '\\uD835\\uDD55',\n    '&dot;': '\\u02D9',\n    '&doteq;': '\\u2250',\n    '&doteqdot;': '\\u2251',\n    '&dotminus;': '\\u2238',\n    '&dotplus;': '\\u2214',\n    '&dotsquare;': '\\u22A1',\n    '&doublebarwedge;': '\\u2306',\n    '&downarrow;': '\\u2193',\n    '&downdownarrows;': '\\u21CA',\n    '&downharpoonleft;': '\\u21C3',\n    '&downharpoonright;': '\\u21C2',\n    '&drbkarow;': '\\u2910',\n    '&drcorn;': '\\u231F',\n    '&drcrop;': '\\u230C',\n    '&dscr;': '\\uD835\\uDCB9',\n    '&dscy;': '\\u0455',\n    '&dsol;': '\\u29F6',\n    '&dstrok;': '\\u0111',\n    '&dtdot;': '\\u22F1',\n    '&dtri;': '\\u25BF',\n    '&dtrif;': '\\u25BE',\n    '&duarr;': '\\u21F5',\n    '&duhar;': '\\u296F',\n    '&dwangle;': '\\u29A6',\n    '&dzcy;': '\\u045F',\n    '&dzigrarr;': '\\u27FF',\n    '&eDDot;': '\\u2A77',\n    '&eDot;': '\\u2251',\n    '&eacute': '\\u00E9',\n    '&eacute;': '\\u00E9',\n    '&easter;': '\\u2A6E',\n    '&ecaron;': '\\u011B',\n    '&ecir;': '\\u2256',\n    '&ecirc': '\\u00EA',\n    '&ecirc;': '\\u00EA',\n    '&ecolon;': '\\u2255',\n    '&ecy;': '\\u044D',\n    '&edot;': '\\u0117',\n    '&ee;': '\\u2147',\n    '&efDot;': '\\u2252',\n    '&efr;': '\\uD835\\uDD22',\n    '&eg;': '\\u2A9A',\n    '&egrave': '\\u00E8',\n    '&egrave;': '\\u00E8',\n    '&egs;': '\\u2A96',\n    '&egsdot;': '\\u2A98',\n    '&el;': '\\u2A99',\n    '&elinters;': '\\u23E7',\n    '&ell;': '\\u2113',\n    '&els;': '\\u2A95',\n    '&elsdot;': '\\u2A97',\n    '&emacr;': '\\u0113',\n    '&empty;': '\\u2205',\n    '&emptyset;': '\\u2205',\n    '&emptyv;': '\\u2205',\n    '&emsp13;': '\\u2004',\n    '&emsp14;': '\\u2005',\n    '&emsp;': '\\u2003',\n    '&eng;': '\\u014B',\n    '&ensp;': '\\u2002',\n    '&eogon;': '\\u0119',\n    '&eopf;': '\\uD835\\uDD56',\n    '&epar;': '\\u22D5',\n    '&eparsl;': '\\u29E3',\n    '&eplus;': '\\u2A71',\n    '&epsi;': '\\u03B5',\n    '&epsilon;': '\\u03B5',\n    '&epsiv;': '\\u03F5',\n    '&eqcirc;': '\\u2256',\n    '&eqcolon;': '\\u2255',\n    '&eqsim;': '\\u2242',\n    '&eqslantgtr;': '\\u2A96',\n    '&eqslantless;': '\\u2A95',\n    '&equals;': '\\u003D',\n    '&equest;': '\\u225F',\n    '&equiv;': '\\u2261',\n    '&equivDD;': '\\u2A78',\n    '&eqvparsl;': '\\u29E5',\n    '&erDot;': '\\u2253',\n    '&erarr;': '\\u2971',\n    '&escr;': '\\u212F',\n    '&esdot;': '\\u2250',\n    '&esim;': '\\u2242',\n    '&eta;': '\\u03B7',\n    '&eth': '\\u00F0',\n    '&eth;': '\\u00F0',\n    '&euml': '\\u00EB',\n    '&euml;': '\\u00EB',\n    '&euro;': '\\u20AC',\n    '&excl;': '\\u0021',\n    '&exist;': '\\u2203',\n    '&expectation;': '\\u2130',\n    '&exponentiale;': '\\u2147',\n    '&fallingdotseq;': '\\u2252',\n    '&fcy;': '\\u0444',\n    '&female;': '\\u2640',\n    '&ffilig;': '\\uFB03',\n    '&fflig;': '\\uFB00',\n    '&ffllig;': '\\uFB04',\n    '&ffr;': '\\uD835\\uDD23',\n    '&filig;': '\\uFB01',\n    '&fjlig;': '\\u0066\\u006A',\n    '&flat;': '\\u266D',\n    '&fllig;': '\\uFB02',\n    '&fltns;': '\\u25B1',\n    '&fnof;': '\\u0192',\n    '&fopf;': '\\uD835\\uDD57',\n    '&forall;': '\\u2200',\n    '&fork;': '\\u22D4',\n    '&forkv;': '\\u2AD9',\n    '&fpartint;': '\\u2A0D',\n    '&frac12': '\\u00BD',\n    '&frac12;': '\\u00BD',\n    '&frac13;': '\\u2153',\n    '&frac14': '\\u00BC',\n    '&frac14;': '\\u00BC',\n    '&frac15;': '\\u2155',\n    '&frac16;': '\\u2159',\n    '&frac18;': '\\u215B',\n    '&frac23;': '\\u2154',\n    '&frac25;': '\\u2156',\n    '&frac34': '\\u00BE',\n    '&frac34;': '\\u00BE',\n    '&frac35;': '\\u2157',\n    '&frac38;': '\\u215C',\n    '&frac45;': '\\u2158',\n    '&frac56;': '\\u215A',\n    '&frac58;': '\\u215D',\n    '&frac78;': '\\u215E',\n    '&frasl;': '\\u2044',\n    '&frown;': '\\u2322',\n    '&fscr;': '\\uD835\\uDCBB',\n    '&gE;': '\\u2267',\n    '&gEl;': '\\u2A8C',\n    '&gacute;': '\\u01F5',\n    '&gamma;': '\\u03B3',\n    '&gammad;': '\\u03DD',\n    '&gap;': '\\u2A86',\n    '&gbreve;': '\\u011F',\n    '&gcirc;': '\\u011D',\n    '&gcy;': '\\u0433',\n    '&gdot;': '\\u0121',\n    '&ge;': '\\u2265',\n    '&gel;': '\\u22DB',\n    '&geq;': '\\u2265',\n    '&geqq;': '\\u2267',\n    '&geqslant;': '\\u2A7E',\n    '&ges;': '\\u2A7E',\n    '&gescc;': '\\u2AA9',\n    '&gesdot;': '\\u2A80',\n    '&gesdoto;': '\\u2A82',\n    '&gesdotol;': '\\u2A84',\n    '&gesl;': '\\u22DB\\uFE00',\n    '&gesles;': '\\u2A94',\n    '&gfr;': '\\uD835\\uDD24',\n    '&gg;': '\\u226B',\n    '&ggg;': '\\u22D9',\n    '&gimel;': '\\u2137',\n    '&gjcy;': '\\u0453',\n    '&gl;': '\\u2277',\n    '&glE;': '\\u2A92',\n    '&gla;': '\\u2AA5',\n    '&glj;': '\\u2AA4',\n    '&gnE;': '\\u2269',\n    '&gnap;': '\\u2A8A',\n    '&gnapprox;': '\\u2A8A',\n    '&gne;': '\\u2A88',\n    '&gneq;': '\\u2A88',\n    '&gneqq;': '\\u2269',\n    '&gnsim;': '\\u22E7',\n    '&gopf;': '\\uD835\\uDD58',\n    '&grave;': '\\u0060',\n    '&gscr;': '\\u210A',\n    '&gsim;': '\\u2273',\n    '&gsime;': '\\u2A8E',\n    '&gsiml;': '\\u2A90',\n    '&gt': '\\u003E',\n    '&gt;': '\\u003E',\n    '&gtcc;': '\\u2AA7',\n    '&gtcir;': '\\u2A7A',\n    '&gtdot;': '\\u22D7',\n    '&gtlPar;': '\\u2995',\n    '&gtquest;': '\\u2A7C',\n    '&gtrapprox;': '\\u2A86',\n    '&gtrarr;': '\\u2978',\n    '&gtrdot;': '\\u22D7',\n    '&gtreqless;': '\\u22DB',\n    '&gtreqqless;': '\\u2A8C',\n    '&gtrless;': '\\u2277',\n    '&gtrsim;': '\\u2273',\n    '&gvertneqq;': '\\u2269\\uFE00',\n    '&gvnE;': '\\u2269\\uFE00',\n    '&hArr;': '\\u21D4',\n    '&hairsp;': '\\u200A',\n    '&half;': '\\u00BD',\n    '&hamilt;': '\\u210B',\n    '&hardcy;': '\\u044A',\n    '&harr;': '\\u2194',\n    '&harrcir;': '\\u2948',\n    '&harrw;': '\\u21AD',\n    '&hbar;': '\\u210F',\n    '&hcirc;': '\\u0125',\n    '&hearts;': '\\u2665',\n    '&heartsuit;': '\\u2665',\n    '&hellip;': '\\u2026',\n    '&hercon;': '\\u22B9',\n    '&hfr;': '\\uD835\\uDD25',\n    '&hksearow;': '\\u2925',\n    '&hkswarow;': '\\u2926',\n    '&hoarr;': '\\u21FF',\n    '&homtht;': '\\u223B',\n    '&hookleftarrow;': '\\u21A9',\n    '&hookrightarrow;': '\\u21AA',\n    '&hopf;': '\\uD835\\uDD59',\n    '&horbar;': '\\u2015',\n    '&hscr;': '\\uD835\\uDCBD',\n    '&hslash;': '\\u210F',\n    '&hstrok;': '\\u0127',\n    '&hybull;': '\\u2043',\n    '&hyphen;': '\\u2010',\n    '&iacute': '\\u00ED',\n    '&iacute;': '\\u00ED',\n    '&ic;': '\\u2063',\n    '&icirc': '\\u00EE',\n    '&icirc;': '\\u00EE',\n    '&icy;': '\\u0438',\n    '&iecy;': '\\u0435',\n    '&iexcl': '\\u00A1',\n    '&iexcl;': '\\u00A1',\n    '&iff;': '\\u21D4',\n    '&ifr;': '\\uD835\\uDD26',\n    '&igrave': '\\u00EC',\n    '&igrave;': '\\u00EC',\n    '&ii;': '\\u2148',\n    '&iiiint;': '\\u2A0C',\n    '&iiint;': '\\u222D',\n    '&iinfin;': '\\u29DC',\n    '&iiota;': '\\u2129',\n    '&ijlig;': '\\u0133',\n    '&imacr;': '\\u012B',\n    '&image;': '\\u2111',\n    '&imagline;': '\\u2110',\n    '&imagpart;': '\\u2111',\n    '&imath;': '\\u0131',\n    '&imof;': '\\u22B7',\n    '&imped;': '\\u01B5',\n    '&in;': '\\u2208',\n    '&incare;': '\\u2105',\n    '&infin;': '\\u221E',\n    '&infintie;': '\\u29DD',\n    '&inodot;': '\\u0131',\n    '&int;': '\\u222B',\n    '&intcal;': '\\u22BA',\n    '&integers;': '\\u2124',\n    '&intercal;': '\\u22BA',\n    '&intlarhk;': '\\u2A17',\n    '&intprod;': '\\u2A3C',\n    '&iocy;': '\\u0451',\n    '&iogon;': '\\u012F',\n    '&iopf;': '\\uD835\\uDD5A',\n    '&iota;': '\\u03B9',\n    '&iprod;': '\\u2A3C',\n    '&iquest': '\\u00BF',\n    '&iquest;': '\\u00BF',\n    '&iscr;': '\\uD835\\uDCBE',\n    '&isin;': '\\u2208',\n    '&isinE;': '\\u22F9',\n    '&isindot;': '\\u22F5',\n    '&isins;': '\\u22F4',\n    '&isinsv;': '\\u22F3',\n    '&isinv;': '\\u2208',\n    '&it;': '\\u2062',\n    '&itilde;': '\\u0129',\n    '&iukcy;': '\\u0456',\n    '&iuml': '\\u00EF',\n    '&iuml;': '\\u00EF',\n    '&jcirc;': '\\u0135',\n    '&jcy;': '\\u0439',\n    '&jfr;': '\\uD835\\uDD27',\n    '&jmath;': '\\u0237',\n    '&jopf;': '\\uD835\\uDD5B',\n    '&jscr;': '\\uD835\\uDCBF',\n    '&jsercy;': '\\u0458',\n    '&jukcy;': '\\u0454',\n    '&kappa;': '\\u03BA',\n    '&kappav;': '\\u03F0',\n    '&kcedil;': '\\u0137',\n    '&kcy;': '\\u043A',\n    '&kfr;': '\\uD835\\uDD28',\n    '&kgreen;': '\\u0138',\n    '&khcy;': '\\u0445',\n    '&kjcy;': '\\u045C',\n    '&kopf;': '\\uD835\\uDD5C',\n    '&kscr;': '\\uD835\\uDCC0',\n    '&lAarr;': '\\u21DA',\n    '&lArr;': '\\u21D0',\n    '&lAtail;': '\\u291B',\n    '&lBarr;': '\\u290E',\n    '&lE;': '\\u2266',\n    '&lEg;': '\\u2A8B',\n    '&lHar;': '\\u2962',\n    '&lacute;': '\\u013A',\n    '&laemptyv;': '\\u29B4',\n    '&lagran;': '\\u2112',\n    '&lambda;': '\\u03BB',\n    '&lang;': '\\u27E8',\n    '&langd;': '\\u2991',\n    '&langle;': '\\u27E8',\n    '&lap;': '\\u2A85',\n    '&laquo': '\\u00AB',\n    '&laquo;': '\\u00AB',\n    '&larr;': '\\u2190',\n    '&larrb;': '\\u21E4',\n    '&larrbfs;': '\\u291F',\n    '&larrfs;': '\\u291D',\n    '&larrhk;': '\\u21A9',\n    '&larrlp;': '\\u21AB',\n    '&larrpl;': '\\u2939',\n    '&larrsim;': '\\u2973',\n    '&larrtl;': '\\u21A2',\n    '&lat;': '\\u2AAB',\n    '&latail;': '\\u2919',\n    '&late;': '\\u2AAD',\n    '&lates;': '\\u2AAD\\uFE00',\n    '&lbarr;': '\\u290C',\n    '&lbbrk;': '\\u2772',\n    '&lbrace;': '\\u007B',\n    '&lbrack;': '\\u005B',\n    '&lbrke;': '\\u298B',\n    '&lbrksld;': '\\u298F',\n    '&lbrkslu;': '\\u298D',\n    '&lcaron;': '\\u013E',\n    '&lcedil;': '\\u013C',\n    '&lceil;': '\\u2308',\n    '&lcub;': '\\u007B',\n    '&lcy;': '\\u043B',\n    '&ldca;': '\\u2936',\n    '&ldquo;': '\\u201C',\n    '&ldquor;': '\\u201E',\n    '&ldrdhar;': '\\u2967',\n    '&ldrushar;': '\\u294B',\n    '&ldsh;': '\\u21B2',\n    '&le;': '\\u2264',\n    '&leftarrow;': '\\u2190',\n    '&leftarrowtail;': '\\u21A2',\n    '&leftharpoondown;': '\\u21BD',\n    '&leftharpoonup;': '\\u21BC',\n    '&leftleftarrows;': '\\u21C7',\n    '&leftrightarrow;': '\\u2194',\n    '&leftrightarrows;': '\\u21C6',\n    '&leftrightharpoons;': '\\u21CB',\n    '&leftrightsquigarrow;': '\\u21AD',\n    '&leftthreetimes;': '\\u22CB',\n    '&leg;': '\\u22DA',\n    '&leq;': '\\u2264',\n    '&leqq;': '\\u2266',\n    '&leqslant;': '\\u2A7D',\n    '&les;': '\\u2A7D',\n    '&lescc;': '\\u2AA8',\n    '&lesdot;': '\\u2A7F',\n    '&lesdoto;': '\\u2A81',\n    '&lesdotor;': '\\u2A83',\n    '&lesg;': '\\u22DA\\uFE00',\n    '&lesges;': '\\u2A93',\n    '&lessapprox;': '\\u2A85',\n    '&lessdot;': '\\u22D6',\n    '&lesseqgtr;': '\\u22DA',\n    '&lesseqqgtr;': '\\u2A8B',\n    '&lessgtr;': '\\u2276',\n    '&lesssim;': '\\u2272',\n    '&lfisht;': '\\u297C',\n    '&lfloor;': '\\u230A',\n    '&lfr;': '\\uD835\\uDD29',\n    '&lg;': '\\u2276',\n    '&lgE;': '\\u2A91',\n    '&lhard;': '\\u21BD',\n    '&lharu;': '\\u21BC',\n    '&lharul;': '\\u296A',\n    '&lhblk;': '\\u2584',\n    '&ljcy;': '\\u0459',\n    '&ll;': '\\u226A',\n    '&llarr;': '\\u21C7',\n    '&llcorner;': '\\u231E',\n    '&llhard;': '\\u296B',\n    '&lltri;': '\\u25FA',\n    '&lmidot;': '\\u0140',\n    '&lmoust;': '\\u23B0',\n    '&lmoustache;': '\\u23B0',\n    '&lnE;': '\\u2268',\n    '&lnap;': '\\u2A89',\n    '&lnapprox;': '\\u2A89',\n    '&lne;': '\\u2A87',\n    '&lneq;': '\\u2A87',\n    '&lneqq;': '\\u2268',\n    '&lnsim;': '\\u22E6',\n    '&loang;': '\\u27EC',\n    '&loarr;': '\\u21FD',\n    '&lobrk;': '\\u27E6',\n    '&longleftarrow;': '\\u27F5',\n    '&longleftrightarrow;': '\\u27F7',\n    '&longmapsto;': '\\u27FC',\n    '&longrightarrow;': '\\u27F6',\n    '&looparrowleft;': '\\u21AB',\n    '&looparrowright;': '\\u21AC',\n    '&lopar;': '\\u2985',\n    '&lopf;': '\\uD835\\uDD5D',\n    '&loplus;': '\\u2A2D',\n    '&lotimes;': '\\u2A34',\n    '&lowast;': '\\u2217',\n    '&lowbar;': '\\u005F',\n    '&loz;': '\\u25CA',\n    '&lozenge;': '\\u25CA',\n    '&lozf;': '\\u29EB',\n    '&lpar;': '\\u0028',\n    '&lparlt;': '\\u2993',\n    '&lrarr;': '\\u21C6',\n    '&lrcorner;': '\\u231F',\n    '&lrhar;': '\\u21CB',\n    '&lrhard;': '\\u296D',\n    '&lrm;': '\\u200E',\n    '&lrtri;': '\\u22BF',\n    '&lsaquo;': '\\u2039',\n    '&lscr;': '\\uD835\\uDCC1',\n    '&lsh;': '\\u21B0',\n    '&lsim;': '\\u2272',\n    '&lsime;': '\\u2A8D',\n    '&lsimg;': '\\u2A8F',\n    '&lsqb;': '\\u005B',\n    '&lsquo;': '\\u2018',\n    '&lsquor;': '\\u201A',\n    '&lstrok;': '\\u0142',\n    '&lt': '\\u003C',\n    '&lt;': '\\u003C',\n    '&ltcc;': '\\u2AA6',\n    '&ltcir;': '\\u2A79',\n    '&ltdot;': '\\u22D6',\n    '&lthree;': '\\u22CB',\n    '&ltimes;': '\\u22C9',\n    '&ltlarr;': '\\u2976',\n    '&ltquest;': '\\u2A7B',\n    '&ltrPar;': '\\u2996',\n    '&ltri;': '\\u25C3',\n    '&ltrie;': '\\u22B4',\n    '&ltrif;': '\\u25C2',\n    '&lurdshar;': '\\u294A',\n    '&luruhar;': '\\u2966',\n    '&lvertneqq;': '\\u2268\\uFE00',\n    '&lvnE;': '\\u2268\\uFE00',\n    '&mDDot;': '\\u223A',\n    '&macr': '\\u00AF',\n    '&macr;': '\\u00AF',\n    '&male;': '\\u2642',\n    '&malt;': '\\u2720',\n    '&maltese;': '\\u2720',\n    '&map;': '\\u21A6',\n    '&mapsto;': '\\u21A6',\n    '&mapstodown;': '\\u21A7',\n    '&mapstoleft;': '\\u21A4',\n    '&mapstoup;': '\\u21A5',\n    '&marker;': '\\u25AE',\n    '&mcomma;': '\\u2A29',\n    '&mcy;': '\\u043C',\n    '&mdash;': '\\u2014',\n    '&measuredangle;': '\\u2221',\n    '&mfr;': '\\uD835\\uDD2A',\n    '&mho;': '\\u2127',\n    '&micro': '\\u00B5',\n    '&micro;': '\\u00B5',\n    '&mid;': '\\u2223',\n    '&midast;': '\\u002A',\n    '&midcir;': '\\u2AF0',\n    '&middot': '\\u00B7',\n    '&middot;': '\\u00B7',\n    '&minus;': '\\u2212',\n    '&minusb;': '\\u229F',\n    '&minusd;': '\\u2238',\n    '&minusdu;': '\\u2A2A',\n    '&mlcp;': '\\u2ADB',\n    '&mldr;': '\\u2026',\n    '&mnplus;': '\\u2213',\n    '&models;': '\\u22A7',\n    '&mopf;': '\\uD835\\uDD5E',\n    '&mp;': '\\u2213',\n    '&mscr;': '\\uD835\\uDCC2',\n    '&mstpos;': '\\u223E',\n    '&mu;': '\\u03BC',\n    '&multimap;': '\\u22B8',\n    '&mumap;': '\\u22B8',\n    '&nGg;': '\\u22D9\\u0338',\n    '&nGt;': '\\u226B\\u20D2',\n    '&nGtv;': '\\u226B\\u0338',\n    '&nLeftarrow;': '\\u21CD',\n    '&nLeftrightarrow;': '\\u21CE',\n    '&nLl;': '\\u22D8\\u0338',\n    '&nLt;': '\\u226A\\u20D2',\n    '&nLtv;': '\\u226A\\u0338',\n    '&nRightarrow;': '\\u21CF',\n    '&nVDash;': '\\u22AF',\n    '&nVdash;': '\\u22AE',\n    '&nabla;': '\\u2207',\n    '&nacute;': '\\u0144',\n    '&nang;': '\\u2220\\u20D2',\n    '&nap;': '\\u2249',\n    '&napE;': '\\u2A70\\u0338',\n    '&napid;': '\\u224B\\u0338',\n    '&napos;': '\\u0149',\n    '&napprox;': '\\u2249',\n    '&natur;': '\\u266E',\n    '&natural;': '\\u266E',\n    '&naturals;': '\\u2115',\n    '&nbsp': '\\u00A0',\n    '&nbsp;': '\\u00A0',\n    '&nbump;': '\\u224E\\u0338',\n    '&nbumpe;': '\\u224F\\u0338',\n    '&ncap;': '\\u2A43',\n    '&ncaron;': '\\u0148',\n    '&ncedil;': '\\u0146',\n    '&ncong;': '\\u2247',\n    '&ncongdot;': '\\u2A6D\\u0338',\n    '&ncup;': '\\u2A42',\n    '&ncy;': '\\u043D',\n    '&ndash;': '\\u2013',\n    '&ne;': '\\u2260',\n    '&neArr;': '\\u21D7',\n    '&nearhk;': '\\u2924',\n    '&nearr;': '\\u2197',\n    '&nearrow;': '\\u2197',\n    '&nedot;': '\\u2250\\u0338',\n    '&nequiv;': '\\u2262',\n    '&nesear;': '\\u2928',\n    '&nesim;': '\\u2242\\u0338',\n    '&nexist;': '\\u2204',\n    '&nexists;': '\\u2204',\n    '&nfr;': '\\uD835\\uDD2B',\n    '&ngE;': '\\u2267\\u0338',\n    '&nge;': '\\u2271',\n    '&ngeq;': '\\u2271',\n    '&ngeqq;': '\\u2267\\u0338',\n    '&ngeqslant;': '\\u2A7E\\u0338',\n    '&nges;': '\\u2A7E\\u0338',\n    '&ngsim;': '\\u2275',\n    '&ngt;': '\\u226F',\n    '&ngtr;': '\\u226F',\n    '&nhArr;': '\\u21CE',\n    '&nharr;': '\\u21AE',\n    '&nhpar;': '\\u2AF2',\n    '&ni;': '\\u220B',\n    '&nis;': '\\u22FC',\n    '&nisd;': '\\u22FA',\n    '&niv;': '\\u220B',\n    '&njcy;': '\\u045A',\n    '&nlArr;': '\\u21CD',\n    '&nlE;': '\\u2266\\u0338',\n    '&nlarr;': '\\u219A',\n    '&nldr;': '\\u2025',\n    '&nle;': '\\u2270',\n    '&nleftarrow;': '\\u219A',\n    '&nleftrightarrow;': '\\u21AE',\n    '&nleq;': '\\u2270',\n    '&nleqq;': '\\u2266\\u0338',\n    '&nleqslant;': '\\u2A7D\\u0338',\n    '&nles;': '\\u2A7D\\u0338',\n    '&nless;': '\\u226E',\n    '&nlsim;': '\\u2274',\n    '&nlt;': '\\u226E',\n    '&nltri;': '\\u22EA',\n    '&nltrie;': '\\u22EC',\n    '&nmid;': '\\u2224',\n    '&nopf;': '\\uD835\\uDD5F',\n    '&not': '\\u00AC',\n    '&not;': '\\u00AC',\n    '&notin;': '\\u2209',\n    '&notinE;': '\\u22F9\\u0338',\n    '&notindot;': '\\u22F5\\u0338',\n    '&notinva;': '\\u2209',\n    '&notinvb;': '\\u22F7',\n    '&notinvc;': '\\u22F6',\n    '&notni;': '\\u220C',\n    '&notniva;': '\\u220C',\n    '&notnivb;': '\\u22FE',\n    '&notnivc;': '\\u22FD',\n    '&npar;': '\\u2226',\n    '&nparallel;': '\\u2226',\n    '&nparsl;': '\\u2AFD\\u20E5',\n    '&npart;': '\\u2202\\u0338',\n    '&npolint;': '\\u2A14',\n    '&npr;': '\\u2280',\n    '&nprcue;': '\\u22E0',\n    '&npre;': '\\u2AAF\\u0338',\n    '&nprec;': '\\u2280',\n    '&npreceq;': '\\u2AAF\\u0338',\n    '&nrArr;': '\\u21CF',\n    '&nrarr;': '\\u219B',\n    '&nrarrc;': '\\u2933\\u0338',\n    '&nrarrw;': '\\u219D\\u0338',\n    '&nrightarrow;': '\\u219B',\n    '&nrtri;': '\\u22EB',\n    '&nrtrie;': '\\u22ED',\n    '&nsc;': '\\u2281',\n    '&nsccue;': '\\u22E1',\n    '&nsce;': '\\u2AB0\\u0338',\n    '&nscr;': '\\uD835\\uDCC3',\n    '&nshortmid;': '\\u2224',\n    '&nshortparallel;': '\\u2226',\n    '&nsim;': '\\u2241',\n    '&nsime;': '\\u2244',\n    '&nsimeq;': '\\u2244',\n    '&nsmid;': '\\u2224',\n    '&nspar;': '\\u2226',\n    '&nsqsube;': '\\u22E2',\n    '&nsqsupe;': '\\u22E3',\n    '&nsub;': '\\u2284',\n    '&nsubE;': '\\u2AC5\\u0338',\n    '&nsube;': '\\u2288',\n    '&nsubset;': '\\u2282\\u20D2',\n    '&nsubseteq;': '\\u2288',\n    '&nsubseteqq;': '\\u2AC5\\u0338',\n    '&nsucc;': '\\u2281',\n    '&nsucceq;': '\\u2AB0\\u0338',\n    '&nsup;': '\\u2285',\n    '&nsupE;': '\\u2AC6\\u0338',\n    '&nsupe;': '\\u2289',\n    '&nsupset;': '\\u2283\\u20D2',\n    '&nsupseteq;': '\\u2289',\n    '&nsupseteqq;': '\\u2AC6\\u0338',\n    '&ntgl;': '\\u2279',\n    '&ntilde': '\\u00F1',\n    '&ntilde;': '\\u00F1',\n    '&ntlg;': '\\u2278',\n    '&ntriangleleft;': '\\u22EA',\n    '&ntrianglelefteq;': '\\u22EC',\n    '&ntriangleright;': '\\u22EB',\n    '&ntrianglerighteq;': '\\u22ED',\n    '&nu;': '\\u03BD',\n    '&num;': '\\u0023',\n    '&numero;': '\\u2116',\n    '&numsp;': '\\u2007',\n    '&nvDash;': '\\u22AD',\n    '&nvHarr;': '\\u2904',\n    '&nvap;': '\\u224D\\u20D2',\n    '&nvdash;': '\\u22AC',\n    '&nvge;': '\\u2265\\u20D2',\n    '&nvgt;': '\\u003E\\u20D2',\n    '&nvinfin;': '\\u29DE',\n    '&nvlArr;': '\\u2902',\n    '&nvle;': '\\u2264\\u20D2',\n    '&nvlt;': '\\u003C\\u20D2',\n    '&nvltrie;': '\\u22B4\\u20D2',\n    '&nvrArr;': '\\u2903',\n    '&nvrtrie;': '\\u22B5\\u20D2',\n    '&nvsim;': '\\u223C\\u20D2',\n    '&nwArr;': '\\u21D6',\n    '&nwarhk;': '\\u2923',\n    '&nwarr;': '\\u2196',\n    '&nwarrow;': '\\u2196',\n    '&nwnear;': '\\u2927',\n    '&oS;': '\\u24C8',\n    '&oacute': '\\u00F3',\n    '&oacute;': '\\u00F3',\n    '&oast;': '\\u229B',\n    '&ocir;': '\\u229A',\n    '&ocirc': '\\u00F4',\n    '&ocirc;': '\\u00F4',\n    '&ocy;': '\\u043E',\n    '&odash;': '\\u229D',\n    '&odblac;': '\\u0151',\n    '&odiv;': '\\u2A38',\n    '&odot;': '\\u2299',\n    '&odsold;': '\\u29BC',\n    '&oelig;': '\\u0153',\n    '&ofcir;': '\\u29BF',\n    '&ofr;': '\\uD835\\uDD2C',\n    '&ogon;': '\\u02DB',\n    '&ograve': '\\u00F2',\n    '&ograve;': '\\u00F2',\n    '&ogt;': '\\u29C1',\n    '&ohbar;': '\\u29B5',\n    '&ohm;': '\\u03A9',\n    '&oint;': '\\u222E',\n    '&olarr;': '\\u21BA',\n    '&olcir;': '\\u29BE',\n    '&olcross;': '\\u29BB',\n    '&oline;': '\\u203E',\n    '&olt;': '\\u29C0',\n    '&omacr;': '\\u014D',\n    '&omega;': '\\u03C9',\n    '&omicron;': '\\u03BF',\n    '&omid;': '\\u29B6',\n    '&ominus;': '\\u2296',\n    '&oopf;': '\\uD835\\uDD60',\n    '&opar;': '\\u29B7',\n    '&operp;': '\\u29B9',\n    '&oplus;': '\\u2295',\n    '&or;': '\\u2228',\n    '&orarr;': '\\u21BB',\n    '&ord;': '\\u2A5D',\n    '&order;': '\\u2134',\n    '&orderof;': '\\u2134',\n    '&ordf': '\\u00AA',\n    '&ordf;': '\\u00AA',\n    '&ordm': '\\u00BA',\n    '&ordm;': '\\u00BA',\n    '&origof;': '\\u22B6',\n    '&oror;': '\\u2A56',\n    '&orslope;': '\\u2A57',\n    '&orv;': '\\u2A5B',\n    '&oscr;': '\\u2134',\n    '&oslash': '\\u00F8',\n    '&oslash;': '\\u00F8',\n    '&osol;': '\\u2298',\n    '&otilde': '\\u00F5',\n    '&otilde;': '\\u00F5',\n    '&otimes;': '\\u2297',\n    '&otimesas;': '\\u2A36',\n    '&ouml': '\\u00F6',\n    '&ouml;': '\\u00F6',\n    '&ovbar;': '\\u233D',\n    '&par;': '\\u2225',\n    '&para': '\\u00B6',\n    '&para;': '\\u00B6',\n    '&parallel;': '\\u2225',\n    '&parsim;': '\\u2AF3',\n    '&parsl;': '\\u2AFD',\n    '&part;': '\\u2202',\n    '&pcy;': '\\u043F',\n    '&percnt;': '\\u0025',\n    '&period;': '\\u002E',\n    '&permil;': '\\u2030',\n    '&perp;': '\\u22A5',\n    '&pertenk;': '\\u2031',\n    '&pfr;': '\\uD835\\uDD2D',\n    '&phi;': '\\u03C6',\n    '&phiv;': '\\u03D5',\n    '&phmmat;': '\\u2133',\n    '&phone;': '\\u260E',\n    '&pi;': '\\u03C0',\n    '&pitchfork;': '\\u22D4',\n    '&piv;': '\\u03D6',\n    '&planck;': '\\u210F',\n    '&planckh;': '\\u210E',\n    '&plankv;': '\\u210F',\n    '&plus;': '\\u002B',\n    '&plusacir;': '\\u2A23',\n    '&plusb;': '\\u229E',\n    '&pluscir;': '\\u2A22',\n    '&plusdo;': '\\u2214',\n    '&plusdu;': '\\u2A25',\n    '&pluse;': '\\u2A72',\n    '&plusmn': '\\u00B1',\n    '&plusmn;': '\\u00B1',\n    '&plussim;': '\\u2A26',\n    '&plustwo;': '\\u2A27',\n    '&pm;': '\\u00B1',\n    '&pointint;': '\\u2A15',\n    '&popf;': '\\uD835\\uDD61',\n    '&pound': '\\u00A3',\n    '&pound;': '\\u00A3',\n    '&pr;': '\\u227A',\n    '&prE;': '\\u2AB3',\n    '&prap;': '\\u2AB7',\n    '&prcue;': '\\u227C',\n    '&pre;': '\\u2AAF',\n    '&prec;': '\\u227A',\n    '&precapprox;': '\\u2AB7',\n    '&preccurlyeq;': '\\u227C',\n    '&preceq;': '\\u2AAF',\n    '&precnapprox;': '\\u2AB9',\n    '&precneqq;': '\\u2AB5',\n    '&precnsim;': '\\u22E8',\n    '&precsim;': '\\u227E',\n    '&prime;': '\\u2032',\n    '&primes;': '\\u2119',\n    '&prnE;': '\\u2AB5',\n    '&prnap;': '\\u2AB9',\n    '&prnsim;': '\\u22E8',\n    '&prod;': '\\u220F',\n    '&profalar;': '\\u232E',\n    '&profline;': '\\u2312',\n    '&profsurf;': '\\u2313',\n    '&prop;': '\\u221D',\n    '&propto;': '\\u221D',\n    '&prsim;': '\\u227E',\n    '&prurel;': '\\u22B0',\n    '&pscr;': '\\uD835\\uDCC5',\n    '&psi;': '\\u03C8',\n    '&puncsp;': '\\u2008',\n    '&qfr;': '\\uD835\\uDD2E',\n    '&qint;': '\\u2A0C',\n    '&qopf;': '\\uD835\\uDD62',\n    '&qprime;': '\\u2057',\n    '&qscr;': '\\uD835\\uDCC6',\n    '&quaternions;': '\\u210D',\n    '&quatint;': '\\u2A16',\n    '&quest;': '\\u003F',\n    '&questeq;': '\\u225F',\n    '&quot': '\\u0022',\n    '&quot;': '\\u0022',\n    '&rAarr;': '\\u21DB',\n    '&rArr;': '\\u21D2',\n    '&rAtail;': '\\u291C',\n    '&rBarr;': '\\u290F',\n    '&rHar;': '\\u2964',\n    '&race;': '\\u223D\\u0331',\n    '&racute;': '\\u0155',\n    '&radic;': '\\u221A',\n    '&raemptyv;': '\\u29B3',\n    '&rang;': '\\u27E9',\n    '&rangd;': '\\u2992',\n    '&range;': '\\u29A5',\n    '&rangle;': '\\u27E9',\n    '&raquo': '\\u00BB',\n    '&raquo;': '\\u00BB',\n    '&rarr;': '\\u2192',\n    '&rarrap;': '\\u2975',\n    '&rarrb;': '\\u21E5',\n    '&rarrbfs;': '\\u2920',\n    '&rarrc;': '\\u2933',\n    '&rarrfs;': '\\u291E',\n    '&rarrhk;': '\\u21AA',\n    '&rarrlp;': '\\u21AC',\n    '&rarrpl;': '\\u2945',\n    '&rarrsim;': '\\u2974',\n    '&rarrtl;': '\\u21A3',\n    '&rarrw;': '\\u219D',\n    '&ratail;': '\\u291A',\n    '&ratio;': '\\u2236',\n    '&rationals;': '\\u211A',\n    '&rbarr;': '\\u290D',\n    '&rbbrk;': '\\u2773',\n    '&rbrace;': '\\u007D',\n    '&rbrack;': '\\u005D',\n    '&rbrke;': '\\u298C',\n    '&rbrksld;': '\\u298E',\n    '&rbrkslu;': '\\u2990',\n    '&rcaron;': '\\u0159',\n    '&rcedil;': '\\u0157',\n    '&rceil;': '\\u2309',\n    '&rcub;': '\\u007D',\n    '&rcy;': '\\u0440',\n    '&rdca;': '\\u2937',\n    '&rdldhar;': '\\u2969',\n    '&rdquo;': '\\u201D',\n    '&rdquor;': '\\u201D',\n    '&rdsh;': '\\u21B3',\n    '&real;': '\\u211C',\n    '&realine;': '\\u211B',\n    '&realpart;': '\\u211C',\n    '&reals;': '\\u211D',\n    '&rect;': '\\u25AD',\n    '&reg': '\\u00AE',\n    '&reg;': '\\u00AE',\n    '&rfisht;': '\\u297D',\n    '&rfloor;': '\\u230B',\n    '&rfr;': '\\uD835\\uDD2F',\n    '&rhard;': '\\u21C1',\n    '&rharu;': '\\u21C0',\n    '&rharul;': '\\u296C',\n    '&rho;': '\\u03C1',\n    '&rhov;': '\\u03F1',\n    '&rightarrow;': '\\u2192',\n    '&rightarrowtail;': '\\u21A3',\n    '&rightharpoondown;': '\\u21C1',\n    '&rightharpoonup;': '\\u21C0',\n    '&rightleftarrows;': '\\u21C4',\n    '&rightleftharpoons;': '\\u21CC',\n    '&rightrightarrows;': '\\u21C9',\n    '&rightsquigarrow;': '\\u219D',\n    '&rightthreetimes;': '\\u22CC',\n    '&ring;': '\\u02DA',\n    '&risingdotseq;': '\\u2253',\n    '&rlarr;': '\\u21C4',\n    '&rlhar;': '\\u21CC',\n    '&rlm;': '\\u200F',\n    '&rmoust;': '\\u23B1',\n    '&rmoustache;': '\\u23B1',\n    '&rnmid;': '\\u2AEE',\n    '&roang;': '\\u27ED',\n    '&roarr;': '\\u21FE',\n    '&robrk;': '\\u27E7',\n    '&ropar;': '\\u2986',\n    '&ropf;': '\\uD835\\uDD63',\n    '&roplus;': '\\u2A2E',\n    '&rotimes;': '\\u2A35',\n    '&rpar;': '\\u0029',\n    '&rpargt;': '\\u2994',\n    '&rppolint;': '\\u2A12',\n    '&rrarr;': '\\u21C9',\n    '&rsaquo;': '\\u203A',\n    '&rscr;': '\\uD835\\uDCC7',\n    '&rsh;': '\\u21B1',\n    '&rsqb;': '\\u005D',\n    '&rsquo;': '\\u2019',\n    '&rsquor;': '\\u2019',\n    '&rthree;': '\\u22CC',\n    '&rtimes;': '\\u22CA',\n    '&rtri;': '\\u25B9',\n    '&rtrie;': '\\u22B5',\n    '&rtrif;': '\\u25B8',\n    '&rtriltri;': '\\u29CE',\n    '&ruluhar;': '\\u2968',\n    '&rx;': '\\u211E',\n    '&sacute;': '\\u015B',\n    '&sbquo;': '\\u201A',\n    '&sc;': '\\u227B',\n    '&scE;': '\\u2AB4',\n    '&scap;': '\\u2AB8',\n    '&scaron;': '\\u0161',\n    '&sccue;': '\\u227D',\n    '&sce;': '\\u2AB0',\n    '&scedil;': '\\u015F',\n    '&scirc;': '\\u015D',\n    '&scnE;': '\\u2AB6',\n    '&scnap;': '\\u2ABA',\n    '&scnsim;': '\\u22E9',\n    '&scpolint;': '\\u2A13',\n    '&scsim;': '\\u227F',\n    '&scy;': '\\u0441',\n    '&sdot;': '\\u22C5',\n    '&sdotb;': '\\u22A1',\n    '&sdote;': '\\u2A66',\n    '&seArr;': '\\u21D8',\n    '&searhk;': '\\u2925',\n    '&searr;': '\\u2198',\n    '&searrow;': '\\u2198',\n    '&sect': '\\u00A7',\n    '&sect;': '\\u00A7',\n    '&semi;': '\\u003B',\n    '&seswar;': '\\u2929',\n    '&setminus;': '\\u2216',\n    '&setmn;': '\\u2216',\n    '&sext;': '\\u2736',\n    '&sfr;': '\\uD835\\uDD30',\n    '&sfrown;': '\\u2322',\n    '&sharp;': '\\u266F',\n    '&shchcy;': '\\u0449',\n    '&shcy;': '\\u0448',\n    '&shortmid;': '\\u2223',\n    '&shortparallel;': '\\u2225',\n    '&shy': '\\u00AD',\n    '&shy;': '\\u00AD',\n    '&sigma;': '\\u03C3',\n    '&sigmaf;': '\\u03C2',\n    '&sigmav;': '\\u03C2',\n    '&sim;': '\\u223C',\n    '&simdot;': '\\u2A6A',\n    '&sime;': '\\u2243',\n    '&simeq;': '\\u2243',\n    '&simg;': '\\u2A9E',\n    '&simgE;': '\\u2AA0',\n    '&siml;': '\\u2A9D',\n    '&simlE;': '\\u2A9F',\n    '&simne;': '\\u2246',\n    '&simplus;': '\\u2A24',\n    '&simrarr;': '\\u2972',\n    '&slarr;': '\\u2190',\n    '&smallsetminus;': '\\u2216',\n    '&smashp;': '\\u2A33',\n    '&smeparsl;': '\\u29E4',\n    '&smid;': '\\u2223',\n    '&smile;': '\\u2323',\n    '&smt;': '\\u2AAA',\n    '&smte;': '\\u2AAC',\n    '&smtes;': '\\u2AAC\\uFE00',\n    '&softcy;': '\\u044C',\n    '&sol;': '\\u002F',\n    '&solb;': '\\u29C4',\n    '&solbar;': '\\u233F',\n    '&sopf;': '\\uD835\\uDD64',\n    '&spades;': '\\u2660',\n    '&spadesuit;': '\\u2660',\n    '&spar;': '\\u2225',\n    '&sqcap;': '\\u2293',\n    '&sqcaps;': '\\u2293\\uFE00',\n    '&sqcup;': '\\u2294',\n    '&sqcups;': '\\u2294\\uFE00',\n    '&sqsub;': '\\u228F',\n    '&sqsube;': '\\u2291',\n    '&sqsubset;': '\\u228F',\n    '&sqsubseteq;': '\\u2291',\n    '&sqsup;': '\\u2290',\n    '&sqsupe;': '\\u2292',\n    '&sqsupset;': '\\u2290',\n    '&sqsupseteq;': '\\u2292',\n    '&squ;': '\\u25A1',\n    '&square;': '\\u25A1',\n    '&squarf;': '\\u25AA',\n    '&squf;': '\\u25AA',\n    '&srarr;': '\\u2192',\n    '&sscr;': '\\uD835\\uDCC8',\n    '&ssetmn;': '\\u2216',\n    '&ssmile;': '\\u2323',\n    '&sstarf;': '\\u22C6',\n    '&star;': '\\u2606',\n    '&starf;': '\\u2605',\n    '&straightepsilon;': '\\u03F5',\n    '&straightphi;': '\\u03D5',\n    '&strns;': '\\u00AF',\n    '&sub;': '\\u2282',\n    '&subE;': '\\u2AC5',\n    '&subdot;': '\\u2ABD',\n    '&sube;': '\\u2286',\n    '&subedot;': '\\u2AC3',\n    '&submult;': '\\u2AC1',\n    '&subnE;': '\\u2ACB',\n    '&subne;': '\\u228A',\n    '&subplus;': '\\u2ABF',\n    '&subrarr;': '\\u2979',\n    '&subset;': '\\u2282',\n    '&subseteq;': '\\u2286',\n    '&subseteqq;': '\\u2AC5',\n    '&subsetneq;': '\\u228A',\n    '&subsetneqq;': '\\u2ACB',\n    '&subsim;': '\\u2AC7',\n    '&subsub;': '\\u2AD5',\n    '&subsup;': '\\u2AD3',\n    '&succ;': '\\u227B',\n    '&succapprox;': '\\u2AB8',\n    '&succcurlyeq;': '\\u227D',\n    '&succeq;': '\\u2AB0',\n    '&succnapprox;': '\\u2ABA',\n    '&succneqq;': '\\u2AB6',\n    '&succnsim;': '\\u22E9',\n    '&succsim;': '\\u227F',\n    '&sum;': '\\u2211',\n    '&sung;': '\\u266A',\n    '&sup1': '\\u00B9',\n    '&sup1;': '\\u00B9',\n    '&sup2': '\\u00B2',\n    '&sup2;': '\\u00B2',\n    '&sup3': '\\u00B3',\n    '&sup3;': '\\u00B3',\n    '&sup;': '\\u2283',\n    '&supE;': '\\u2AC6',\n    '&supdot;': '\\u2ABE',\n    '&supdsub;': '\\u2AD8',\n    '&supe;': '\\u2287',\n    '&supedot;': '\\u2AC4',\n    '&suphsol;': '\\u27C9',\n    '&suphsub;': '\\u2AD7',\n    '&suplarr;': '\\u297B',\n    '&supmult;': '\\u2AC2',\n    '&supnE;': '\\u2ACC',\n    '&supne;': '\\u228B',\n    '&supplus;': '\\u2AC0',\n    '&supset;': '\\u2283',\n    '&supseteq;': '\\u2287',\n    '&supseteqq;': '\\u2AC6',\n    '&supsetneq;': '\\u228B',\n    '&supsetneqq;': '\\u2ACC',\n    '&supsim;': '\\u2AC8',\n    '&supsub;': '\\u2AD4',\n    '&supsup;': '\\u2AD6',\n    '&swArr;': '\\u21D9',\n    '&swarhk;': '\\u2926',\n    '&swarr;': '\\u2199',\n    '&swarrow;': '\\u2199',\n    '&swnwar;': '\\u292A',\n    '&szlig': '\\u00DF',\n    '&szlig;': '\\u00DF',\n    '&target;': '\\u2316',\n    '&tau;': '\\u03C4',\n    '&tbrk;': '\\u23B4',\n    '&tcaron;': '\\u0165',\n    '&tcedil;': '\\u0163',\n    '&tcy;': '\\u0442',\n    '&tdot;': '\\u20DB',\n    '&telrec;': '\\u2315',\n    '&tfr;': '\\uD835\\uDD31',\n    '&there4;': '\\u2234',\n    '&therefore;': '\\u2234',\n    '&theta;': '\\u03B8',\n    '&thetasym;': '\\u03D1',\n    '&thetav;': '\\u03D1',\n    '&thickapprox;': '\\u2248',\n    '&thicksim;': '\\u223C',\n    '&thinsp;': '\\u2009',\n    '&thkap;': '\\u2248',\n    '&thksim;': '\\u223C',\n    '&thorn': '\\u00FE',\n    '&thorn;': '\\u00FE',\n    '&tilde;': '\\u02DC',\n    '&times': '\\u00D7',\n    '&times;': '\\u00D7',\n    '&timesb;': '\\u22A0',\n    '&timesbar;': '\\u2A31',\n    '&timesd;': '\\u2A30',\n    '&tint;': '\\u222D',\n    '&toea;': '\\u2928',\n    '&top;': '\\u22A4',\n    '&topbot;': '\\u2336',\n    '&topcir;': '\\u2AF1',\n    '&topf;': '\\uD835\\uDD65',\n    '&topfork;': '\\u2ADA',\n    '&tosa;': '\\u2929',\n    '&tprime;': '\\u2034',\n    '&trade;': '\\u2122',\n    '&triangle;': '\\u25B5',\n    '&triangledown;': '\\u25BF',\n    '&triangleleft;': '\\u25C3',\n    '&trianglelefteq;': '\\u22B4',\n    '&triangleq;': '\\u225C',\n    '&triangleright;': '\\u25B9',\n    '&trianglerighteq;': '\\u22B5',\n    '&tridot;': '\\u25EC',\n    '&trie;': '\\u225C',\n    '&triminus;': '\\u2A3A',\n    '&triplus;': '\\u2A39',\n    '&trisb;': '\\u29CD',\n    '&tritime;': '\\u2A3B',\n    '&trpezium;': '\\u23E2',\n    '&tscr;': '\\uD835\\uDCC9',\n    '&tscy;': '\\u0446',\n    '&tshcy;': '\\u045B',\n    '&tstrok;': '\\u0167',\n    '&twixt;': '\\u226C',\n    '&twoheadleftarrow;': '\\u219E',\n    '&twoheadrightarrow;': '\\u21A0',\n    '&uArr;': '\\u21D1',\n    '&uHar;': '\\u2963',\n    '&uacute': '\\u00FA',\n    '&uacute;': '\\u00FA',\n    '&uarr;': '\\u2191',\n    '&ubrcy;': '\\u045E',\n    '&ubreve;': '\\u016D',\n    '&ucirc': '\\u00FB',\n    '&ucirc;': '\\u00FB',\n    '&ucy;': '\\u0443',\n    '&udarr;': '\\u21C5',\n    '&udblac;': '\\u0171',\n    '&udhar;': '\\u296E',\n    '&ufisht;': '\\u297E',\n    '&ufr;': '\\uD835\\uDD32',\n    '&ugrave': '\\u00F9',\n    '&ugrave;': '\\u00F9',\n    '&uharl;': '\\u21BF',\n    '&uharr;': '\\u21BE',\n    '&uhblk;': '\\u2580',\n    '&ulcorn;': '\\u231C',\n    '&ulcorner;': '\\u231C',\n    '&ulcrop;': '\\u230F',\n    '&ultri;': '\\u25F8',\n    '&umacr;': '\\u016B',\n    '&uml': '\\u00A8',\n    '&uml;': '\\u00A8',\n    '&uogon;': '\\u0173',\n    '&uopf;': '\\uD835\\uDD66',\n    '&uparrow;': '\\u2191',\n    '&updownarrow;': '\\u2195',\n    '&upharpoonleft;': '\\u21BF',\n    '&upharpoonright;': '\\u21BE',\n    '&uplus;': '\\u228E',\n    '&upsi;': '\\u03C5',\n    '&upsih;': '\\u03D2',\n    '&upsilon;': '\\u03C5',\n    '&upuparrows;': '\\u21C8',\n    '&urcorn;': '\\u231D',\n    '&urcorner;': '\\u231D',\n    '&urcrop;': '\\u230E',\n    '&uring;': '\\u016F',\n    '&urtri;': '\\u25F9',\n    '&uscr;': '\\uD835\\uDCCA',\n    '&utdot;': '\\u22F0',\n    '&utilde;': '\\u0169',\n    '&utri;': '\\u25B5',\n    '&utrif;': '\\u25B4',\n    '&uuarr;': '\\u21C8',\n    '&uuml': '\\u00FC',\n    '&uuml;': '\\u00FC',\n    '&uwangle;': '\\u29A7',\n    '&vArr;': '\\u21D5',\n    '&vBar;': '\\u2AE8',\n    '&vBarv;': '\\u2AE9',\n    '&vDash;': '\\u22A8',\n    '&vangrt;': '\\u299C',\n    '&varepsilon;': '\\u03F5',\n    '&varkappa;': '\\u03F0',\n    '&varnothing;': '\\u2205',\n    '&varphi;': '\\u03D5',\n    '&varpi;': '\\u03D6',\n    '&varpropto;': '\\u221D',\n    '&varr;': '\\u2195',\n    '&varrho;': '\\u03F1',\n    '&varsigma;': '\\u03C2',\n    '&varsubsetneq;': '\\u228A\\uFE00',\n    '&varsubsetneqq;': '\\u2ACB\\uFE00',\n    '&varsupsetneq;': '\\u228B\\uFE00',\n    '&varsupsetneqq;': '\\u2ACC\\uFE00',\n    '&vartheta;': '\\u03D1',\n    '&vartriangleleft;': '\\u22B2',\n    '&vartriangleright;': '\\u22B3',\n    '&vcy;': '\\u0432',\n    '&vdash;': '\\u22A2',\n    '&vee;': '\\u2228',\n    '&veebar;': '\\u22BB',\n    '&veeeq;': '\\u225A',\n    '&vellip;': '\\u22EE',\n    '&verbar;': '\\u007C',\n    '&vert;': '\\u007C',\n    '&vfr;': '\\uD835\\uDD33',\n    '&vltri;': '\\u22B2',\n    '&vnsub;': '\\u2282\\u20D2',\n    '&vnsup;': '\\u2283\\u20D2',\n    '&vopf;': '\\uD835\\uDD67',\n    '&vprop;': '\\u221D',\n    '&vrtri;': '\\u22B3',\n    '&vscr;': '\\uD835\\uDCCB',\n    '&vsubnE;': '\\u2ACB\\uFE00',\n    '&vsubne;': '\\u228A\\uFE00',\n    '&vsupnE;': '\\u2ACC\\uFE00',\n    '&vsupne;': '\\u228B\\uFE00',\n    '&vzigzag;': '\\u299A',\n    '&wcirc;': '\\u0175',\n    '&wedbar;': '\\u2A5F',\n    '&wedge;': '\\u2227',\n    '&wedgeq;': '\\u2259',\n    '&weierp;': '\\u2118',\n    '&wfr;': '\\uD835\\uDD34',\n    '&wopf;': '\\uD835\\uDD68',\n    '&wp;': '\\u2118',\n    '&wr;': '\\u2240',\n    '&wreath;': '\\u2240',\n    '&wscr;': '\\uD835\\uDCCC',\n    '&xcap;': '\\u22C2',\n    '&xcirc;': '\\u25EF',\n    '&xcup;': '\\u22C3',\n    '&xdtri;': '\\u25BD',\n    '&xfr;': '\\uD835\\uDD35',\n    '&xhArr;': '\\u27FA',\n    '&xharr;': '\\u27F7',\n    '&xi;': '\\u03BE',\n    '&xlArr;': '\\u27F8',\n    '&xlarr;': '\\u27F5',\n    '&xmap;': '\\u27FC',\n    '&xnis;': '\\u22FB',\n    '&xodot;': '\\u2A00',\n    '&xopf;': '\\uD835\\uDD69',\n    '&xoplus;': '\\u2A01',\n    '&xotime;': '\\u2A02',\n    '&xrArr;': '\\u27F9',\n    '&xrarr;': '\\u27F6',\n    '&xscr;': '\\uD835\\uDCCD',\n    '&xsqcup;': '\\u2A06',\n    '&xuplus;': '\\u2A04',\n    '&xutri;': '\\u25B3',\n    '&xvee;': '\\u22C1',\n    '&xwedge;': '\\u22C0',\n    '&yacute': '\\u00FD',\n    '&yacute;': '\\u00FD',\n    '&yacy;': '\\u044F',\n    '&ycirc;': '\\u0177',\n    '&ycy;': '\\u044B',\n    '&yen': '\\u00A5',\n    '&yen;': '\\u00A5',\n    '&yfr;': '\\uD835\\uDD36',\n    '&yicy;': '\\u0457',\n    '&yopf;': '\\uD835\\uDD6A',\n    '&yscr;': '\\uD835\\uDCCE',\n    '&yucy;': '\\u044E',\n    '&yuml': '\\u00FF',\n    '&yuml;': '\\u00FF',\n    '&zacute;': '\\u017A',\n    '&zcaron;': '\\u017E',\n    '&zcy;': '\\u0437',\n    '&zdot;': '\\u017C',\n    '&zeetrf;': '\\u2128',\n    '&zeta;': '\\u03B6',\n    '&zfr;': '\\uD835\\uDD37',\n    '&zhcy;': '\\u0436',\n    '&zigrarr;': '\\u21DD',\n    '&zopf;': '\\uD835\\uDD6B',\n    '&zscr;': '\\uD835\\uDCCF',\n    '&zwj;': '\\u200D',\n    '&zwnj;': '\\u200C'\n};\n\nexport default htmlEntities;\n", "import htmlEntities from './html-entities.js';\n\nexport function decodeHTMLEntities(str) {\n    return str.replace(/&(#\\d+|#x[a-f0-9]+|[a-z]+\\d*);?/gi, (match, entity) => {\n        if (typeof htmlEntities[match] === 'string') {\n            return htmlEntities[match];\n        }\n\n        if (entity.charAt(0) !== '#' || match.charAt(match.length - 1) !== ';') {\n            // keep as is, invalid or unknown sequence\n            return match;\n        }\n\n        let codePoint;\n        if (entity.charAt(1) === 'x') {\n            // hex\n            codePoint = parseInt(entity.substr(2), 16);\n        } else {\n            // dec\n            codePoint = parseInt(entity.substr(1), 10);\n        }\n\n        var output = '';\n\n        if ((codePoint >= 0xd800 && codePoint <= 0xdfff) || codePoint > 0x10ffff) {\n            // Invalid range, return a replacement character instead\n            return '\\uFFFD';\n        }\n\n        if (codePoint > 0xffff) {\n            codePoint -= 0x10000;\n            output += String.fromCharCode(((codePoint >>> 10) & 0x3ff) | 0xd800);\n            codePoint = 0xdc00 | (codePoint & 0x3ff);\n        }\n\n        output += String.fromCharCode(codePoint);\n\n        return output;\n    });\n}\n\nexport function escapeHtml(str) {\n    return str.trim().replace(/[<>\"'?&]/g, c => {\n        let hex = c.charCodeAt(0).toString(16);\n        if (hex.length < 2) {\n            hex = '0' + hex;\n        }\n        return '&#x' + hex.toUpperCase() + ';';\n    });\n}\n\nexport function textToHtml(str) {\n    let html = escapeHtml(str).replace(/\\n/g, '<br />');\n    return '<div>' + html + '</div>';\n}\n\nexport function htmlToText(str) {\n    str = str\n        // we can't process tags on multiple lines so remove newlines first\n        .replace(/\\r?\\n/g, '\\u0001')\n        .replace(/<\\!\\-\\-.*?\\-\\->/gi, ' ')\n\n        .replace(/<br\\b[^>]*>/gi, '\\n')\n        .replace(/<\\/?(p|div|table|tr|td|th)\\b[^>]*>/gi, '\\n\\n')\n        .replace(/<script\\b[^>]*>.*?<\\/script\\b[^>]*>/gi, ' ')\n        .replace(/^.*<body\\b[^>]*>/i, '')\n        .replace(/^.*<\\/head\\b[^>]*>/i, '')\n        .replace(/^.*<\\!doctype\\b[^>]*>/i, '')\n        .replace(/<\\/body\\b[^>]*>.*$/i, '')\n        .replace(/<\\/html\\b[^>]*>.*$/i, '')\n\n        .replace(/<a\\b[^>]*href\\s*=\\s*[\"']?([^\\s\"']+)[^>]*>/gi, ' ($1) ')\n\n        .replace(/<\\/?(span|em|i|strong|b|u|a)\\b[^>]*>/gi, '')\n\n        .replace(/<li\\b[^>]*>[\\n\\u0001\\s]*/gi, '* ')\n\n        .replace(/<hr\\b[^>]*>/g, '\\n-------------\\n')\n\n        .replace(/<[^>]*>/g, ' ')\n\n        // convert linebreak placeholders back to newlines\n        .replace(/\\u0001/g, '\\n')\n\n        .replace(/[ \\t]+/g, ' ')\n\n        .replace(/^\\s+$/gm, '')\n\n        .replace(/\\n\\n+/g, '\\n\\n')\n        .replace(/^\\n+/, '\\n')\n        .replace(/\\n+$/, '\\n');\n\n    str = decodeHTMLEntities(str);\n\n    return str;\n}\n\nfunction formatTextAddress(address) {\n    return []\n        .concat(address.name || [])\n        .concat(address.name ? `<${address.address}>` : address.address)\n        .join(' ');\n}\n\nfunction formatTextAddresses(addresses) {\n    let parts = [];\n\n    let processAddress = (address, partCounter) => {\n        if (partCounter) {\n            parts.push(', ');\n        }\n\n        if (address.group) {\n            let groupStart = `${address.name}:`;\n            let groupEnd = `;`;\n\n            parts.push(groupStart);\n            address.group.forEach(processAddress);\n            parts.push(groupEnd);\n        } else {\n            parts.push(formatTextAddress(address));\n        }\n    };\n\n    addresses.forEach(processAddress);\n\n    return parts.join('');\n}\n\nfunction formatHtmlAddress(address) {\n    return `<a href=\"mailto:${escapeHtml(address.address)}\" class=\"postal-email-address\">${escapeHtml(address.name || `<${address.address}>`)}</a>`;\n}\n\nfunction formatHtmlAddresses(addresses) {\n    let parts = [];\n\n    let processAddress = (address, partCounter) => {\n        if (partCounter) {\n            parts.push('<span class=\"postal-email-address-separator\">, </span>');\n        }\n\n        if (address.group) {\n            let groupStart = `<span class=\"postal-email-address-group\">${escapeHtml(address.name)}:</span>`;\n            let groupEnd = `<span class=\"postal-email-address-group\">;</span>`;\n\n            parts.push(groupStart);\n            address.group.forEach(processAddress);\n            parts.push(groupEnd);\n        } else {\n            parts.push(formatHtmlAddress(address));\n        }\n    };\n\n    addresses.forEach(processAddress);\n\n    return parts.join(' ');\n}\n\nfunction foldLines(str, lineLength, afterSpace) {\n    str = (str || '').toString();\n    lineLength = lineLength || 76;\n\n    let pos = 0,\n        len = str.length,\n        result = '',\n        line,\n        match;\n\n    while (pos < len) {\n        line = str.substr(pos, lineLength);\n        if (line.length < lineLength) {\n            result += line;\n            break;\n        }\n        if ((match = line.match(/^[^\\n\\r]*(\\r?\\n|\\r)/))) {\n            line = match[0];\n            result += line;\n            pos += line.length;\n            continue;\n        } else if ((match = line.match(/(\\s+)[^\\s]*$/)) && match[0].length - (afterSpace ? (match[1] || '').length : 0) < line.length) {\n            line = line.substr(0, line.length - (match[0].length - (afterSpace ? (match[1] || '').length : 0)));\n        } else if ((match = str.substr(pos + line.length).match(/^[^\\s]+(\\s*)/))) {\n            line = line + match[0].substr(0, match[0].length - (!afterSpace ? (match[1] || '').length : 0));\n        }\n\n        result += line;\n        pos += line.length;\n        if (pos < len) {\n            result += '\\r\\n';\n        }\n    }\n\n    return result;\n}\n\nexport function formatTextHeader(message) {\n    let rows = [];\n\n    if (message.from) {\n        rows.push({ key: 'From', val: formatTextAddress(message.from) });\n    }\n\n    if (message.subject) {\n        rows.push({ key: 'Subject', val: message.subject });\n    }\n\n    if (message.date) {\n        let dateOptions = {\n            year: 'numeric',\n            month: 'numeric',\n            day: 'numeric',\n            hour: 'numeric',\n            minute: 'numeric',\n            second: 'numeric',\n            hour12: false\n        };\n\n        let dateStr = typeof Intl === 'undefined' ? message.date : new Intl.DateTimeFormat('default', dateOptions).format(new Date(message.date));\n\n        rows.push({ key: 'Date', val: dateStr });\n    }\n\n    if (message.to && message.to.length) {\n        rows.push({ key: 'To', val: formatTextAddresses(message.to) });\n    }\n\n    if (message.cc && message.cc.length) {\n        rows.push({ key: 'Cc', val: formatTextAddresses(message.cc) });\n    }\n\n    if (message.bcc && message.bcc.length) {\n        rows.push({ key: 'Bcc', val: formatTextAddresses(message.bcc) });\n    }\n\n    // Align keys and values by adding space between these two\n    // Also make sure that the separator line is as long as the longest line\n    // Should end up with something like this:\n    /*\n    -----------------------------\n    From:    xx xx <<EMAIL>>\n    Subject: Example Subject\n    Date:    16/02/2021, 02:57:06\n    To:      <EMAIL>\n    -----------------------------\n    */\n\n    let maxKeyLength = rows\n        .map(r => r.key.length)\n        .reduce((acc, cur) => {\n            return cur > acc ? cur : acc;\n        }, 0);\n\n    rows = rows.flatMap(row => {\n        let sepLen = maxKeyLength - row.key.length;\n        let prefix = `${row.key}: ${' '.repeat(sepLen)}`;\n        let emptyPrefix = `${' '.repeat(row.key.length + 1)} ${' '.repeat(sepLen)}`;\n\n        let foldedLines = foldLines(row.val, 80, true)\n            .split(/\\r?\\n/)\n            .map(line => line.trim());\n\n        return foldedLines.map((line, i) => `${i ? emptyPrefix : prefix}${line}`);\n    });\n\n    let maxLineLength = rows\n        .map(r => r.length)\n        .reduce((acc, cur) => {\n            return cur > acc ? cur : acc;\n        }, 0);\n\n    let lineMarker = '-'.repeat(maxLineLength);\n\n    let template = `\n${lineMarker}\n${rows.join('\\n')}\n${lineMarker}\n`;\n\n    return template;\n}\n\nexport function formatHtmlHeader(message) {\n    let rows = [];\n\n    if (message.from) {\n        rows.push(`<div class=\"postal-email-header-key\">From</div><div class=\"postal-email-header-value\">${formatHtmlAddress(message.from)}</div>`);\n    }\n\n    if (message.subject) {\n        rows.push(\n            `<div class=\"postal-email-header-key\">Subject</div><div class=\"postal-email-header-value postal-email-header-subject\">${escapeHtml(\n                message.subject\n            )}</div>`\n        );\n    }\n\n    if (message.date) {\n        let dateOptions = {\n            year: 'numeric',\n            month: 'numeric',\n            day: 'numeric',\n            hour: 'numeric',\n            minute: 'numeric',\n            second: 'numeric',\n            hour12: false\n        };\n\n        let dateStr = typeof Intl === 'undefined' ? message.date : new Intl.DateTimeFormat('default', dateOptions).format(new Date(message.date));\n\n        rows.push(\n            `<div class=\"postal-email-header-key\">Date</div><div class=\"postal-email-header-value postal-email-header-date\" data-date=\"${escapeHtml(\n                message.date\n            )}\">${escapeHtml(dateStr)}</div>`\n        );\n    }\n\n    if (message.to && message.to.length) {\n        rows.push(`<div class=\"postal-email-header-key\">To</div><div class=\"postal-email-header-value\">${formatHtmlAddresses(message.to)}</div>`);\n    }\n\n    if (message.cc && message.cc.length) {\n        rows.push(`<div class=\"postal-email-header-key\">Cc</div><div class=\"postal-email-header-value\">${formatHtmlAddresses(message.cc)}</div>`);\n    }\n\n    if (message.bcc && message.bcc.length) {\n        rows.push(`<div class=\"postal-email-header-key\">Bcc</div><div class=\"postal-email-header-value\">${formatHtmlAddresses(message.bcc)}</div>`);\n    }\n\n    let template = `<div class=\"postal-email-header\">${rows.length ? '<div class=\"postal-email-header-row\">' : ''}${rows.join(\n        '</div>\\n<div class=\"postal-email-header-row\">'\n    )}${rows.length ? '</div>' : ''}</div>`;\n\n    return template;\n}\n", "import { decodeWords } from './decode-strings.js';\n\n/**\n * Converts tokens for a single address into an address object\n *\n * @param {Array} tokens Tokens object\n * @return {Object} Address object\n */\nfunction _handleAddress(tokens) {\n    let token;\n    let isGroup = false;\n    let state = 'text';\n    let address;\n    let addresses = [];\n    let data = {\n        address: [],\n        comment: [],\n        group: [],\n        text: []\n    };\n    let i;\n    let len;\n\n    // Filter out <addresses>, (comments) and regular text\n    for (i = 0, len = tokens.length; i < len; i++) {\n        token = tokens[i];\n        if (token.type === 'operator') {\n            switch (token.value) {\n                case '<':\n                    state = 'address';\n                    break;\n                case '(':\n                    state = 'comment';\n                    break;\n                case ':':\n                    state = 'group';\n                    isGroup = true;\n                    break;\n                default:\n                    state = 'text';\n            }\n        } else if (token.value) {\n            if (state === 'address') {\n                // handle use case where unquoted name includes a \"<\"\n                // Apple Mail truncates everything between an unexpected < and an address\n                // and so will we\n                token.value = token.value.replace(/^[^<]*<\\s*/, '');\n            }\n            data[state].push(token.value);\n        }\n    }\n\n    // If there is no text but a comment, replace the two\n    if (!data.text.length && data.comment.length) {\n        data.text = data.comment;\n        data.comment = [];\n    }\n\n    if (isGroup) {\n        // http://tools.ietf.org/html/rfc2822#appendix-A.1.3\n        data.text = data.text.join(' ');\n        addresses.push({\n            name: decodeWords(data.text || (address && address.name)),\n            group: data.group.length ? addressParser(data.group.join(',')) : []\n        });\n    } else {\n        // If no address was found, try to detect one from regular text\n        if (!data.address.length && data.text.length) {\n            for (i = data.text.length - 1; i >= 0; i--) {\n                if (data.text[i].match(/^[^@\\s]+@[^@\\s]+$/)) {\n                    data.address = data.text.splice(i, 1);\n                    break;\n                }\n            }\n\n            let _regexHandler = function (address) {\n                if (!data.address.length) {\n                    data.address = [address.trim()];\n                    return ' ';\n                } else {\n                    return address;\n                }\n            };\n\n            // still no address\n            if (!data.address.length) {\n                for (i = data.text.length - 1; i >= 0; i--) {\n                    // fixed the regex to parse email address correctly when email address has more than one @\n                    data.text[i] = data.text[i].replace(/\\s*\\b[^@\\s]+@[^\\s]+\\b\\s*/, _regexHandler).trim();\n                    if (data.address.length) {\n                        break;\n                    }\n                }\n            }\n        }\n\n        // If there's still is no text but a comment exixts, replace the two\n        if (!data.text.length && data.comment.length) {\n            data.text = data.comment;\n            data.comment = [];\n        }\n\n        // Keep only the first address occurence, push others to regular text\n        if (data.address.length > 1) {\n            data.text = data.text.concat(data.address.splice(1));\n        }\n\n        // Join values with spaces\n        data.text = data.text.join(' ');\n        data.address = data.address.join(' ');\n\n        if (!data.address && /^=\\?[^=]+?=$/.test(data.text.trim())) {\n            // try to extract words from text content\n            const parsedSubAddresses = addressParser(decodeWords(data.text));\n            if (parsedSubAddresses && parsedSubAddresses.length) {\n                return parsedSubAddresses;\n            }\n        }\n\n        if (!data.address && isGroup) {\n            return [];\n        } else {\n            address = {\n                address: data.address || data.text || '',\n                name: decodeWords(data.text || data.address || '')\n            };\n\n            if (address.address === address.name) {\n                if ((address.address || '').match(/@/)) {\n                    address.name = '';\n                } else {\n                    address.address = '';\n                }\n            }\n\n            addresses.push(address);\n        }\n    }\n\n    return addresses;\n}\n\n/**\n * Creates a Tokenizer object for tokenizing address field strings\n *\n * @constructor\n * @param {String} str Address field string\n */\nclass Tokenizer {\n    constructor(str) {\n        this.str = (str || '').toString();\n        this.operatorCurrent = '';\n        this.operatorExpecting = '';\n        this.node = null;\n        this.escaped = false;\n\n        this.list = [];\n        /**\n         * Operator tokens and which tokens are expected to end the sequence\n         */\n        this.operators = {\n            '\"': '\"',\n            '(': ')',\n            '<': '>',\n            ',': '',\n            ':': ';',\n            // Semicolons are not a legal delimiter per the RFC2822 grammar other\n            // than for terminating a group, but they are also not valid for any\n            // other use in this context.  Given that some mail clients have\n            // historically allowed the semicolon as a delimiter equivalent to the\n            // comma in their UI, it makes sense to treat them the same as a comma\n            // when used outside of a group.\n            ';': ''\n        };\n    }\n\n    /**\n     * Tokenizes the original input string\n     *\n     * @return {Array} An array of operator|text tokens\n     */\n    tokenize() {\n        let chr,\n            list = [];\n        for (let i = 0, len = this.str.length; i < len; i++) {\n            chr = this.str.charAt(i);\n            this.checkChar(chr);\n        }\n\n        this.list.forEach(node => {\n            node.value = (node.value || '').toString().trim();\n            if (node.value) {\n                list.push(node);\n            }\n        });\n\n        return list;\n    }\n\n    /**\n     * Checks if a character is an operator or text and acts accordingly\n     *\n     * @param {String} chr Character from the address field\n     */\n    checkChar(chr) {\n        if (this.escaped) {\n            // ignore next condition blocks\n        } else if (chr === this.operatorExpecting) {\n            this.node = {\n                type: 'operator',\n                value: chr\n            };\n            this.list.push(this.node);\n            this.node = null;\n            this.operatorExpecting = '';\n            this.escaped = false;\n            return;\n        } else if (!this.operatorExpecting && chr in this.operators) {\n            this.node = {\n                type: 'operator',\n                value: chr\n            };\n            this.list.push(this.node);\n            this.node = null;\n            this.operatorExpecting = this.operators[chr];\n            this.escaped = false;\n            return;\n        } else if (['\"', \"'\"].includes(this.operatorExpecting) && chr === '\\\\') {\n            this.escaped = true;\n            return;\n        }\n\n        if (!this.node) {\n            this.node = {\n                type: 'text',\n                value: ''\n            };\n            this.list.push(this.node);\n        }\n\n        if (chr === '\\n') {\n            // Convert newlines to spaces. Carriage return is ignored as \\r and \\n usually\n            // go together anyway and there already is a WS for \\n. Lone \\r means something is fishy.\n            chr = ' ';\n        }\n\n        if (chr.charCodeAt(0) >= 0x21 || [' ', '\\t'].includes(chr)) {\n            // skip command bytes\n            this.node.value += chr;\n        }\n\n        this.escaped = false;\n    }\n}\n\n/**\n * Parses structured e-mail addresses from an address field\n *\n * Example:\n *\n *    'Name <address@domain>'\n *\n * will be converted to\n *\n *     [{name: 'Name', address: 'address@domain'}]\n *\n * @param {String} str Address field\n * @return {Array} An array of address objects\n */\nfunction addressParser(str, options) {\n    options = options || {};\n\n    let tokenizer = new Tokenizer(str);\n    let tokens = tokenizer.tokenize();\n\n    let addresses = [];\n    let address = [];\n    let parsedAddresses = [];\n\n    tokens.forEach(token => {\n        if (token.type === 'operator' && (token.value === ',' || token.value === ';')) {\n            if (address.length) {\n                addresses.push(address);\n            }\n            address = [];\n        } else {\n            address.push(token);\n        }\n    });\n\n    if (address.length) {\n        addresses.push(address);\n    }\n\n    addresses.forEach(address => {\n        address = _handleAddress(address);\n        if (address.length) {\n            parsedAddresses = parsedAddresses.concat(address);\n        }\n    });\n\n    if (options.flatten) {\n        let addresses = [];\n        let walkAddressList = list => {\n            list.forEach(address => {\n                if (address.group) {\n                    return walkAddressList(address.group);\n                } else {\n                    addresses.push(address);\n                }\n            });\n        };\n        walkAddressList(parsedAddresses);\n        return addresses;\n    }\n\n    return parsedAddresses;\n}\n\n// expose to the world\nexport default addressParser;\n", "// Code from: https://gist.githubusercontent.com/jonleighton/958841/raw/fb05a8632efb75d85d43deb593df04367ce48371/base64ArrayBuffer.js\n\n// Converts an ArrayBuffer directly to base64, without any intermediate 'convert to string then\n// use window.btoa' step. According to my tests, this appears to be a faster approach:\n// http://jsperf.com/encoding-xhr-image-data/5\n\n/*\nMIT LICENSE\n\nCopyright 2011 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n\nexport function base64ArrayBuffer(arrayBuffer) {\n    var base64 = '';\n    var encodings = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\n    var bytes = new Uint8Array(arrayBuffer);\n    var byteLength = bytes.byteLength;\n    var byteRemainder = byteLength % 3;\n    var mainLength = byteLength - byteRemainder;\n\n    var a, b, c, d;\n    var chunk;\n\n    // Main loop deals with bytes in chunks of 3\n    for (var i = 0; i < mainLength; i = i + 3) {\n        // Combine the three bytes into a single integer\n        chunk = (bytes[i] << 16) | (bytes[i + 1] << 8) | bytes[i + 2];\n\n        // Use bitmasks to extract 6-bit segments from the triplet\n        a = (chunk & 16515072) >> 18; // 16515072 = (2^6 - 1) << 18\n        b = (chunk & 258048) >> 12; // 258048   = (2^6 - 1) << 12\n        c = (chunk & 4032) >> 6; // 4032     = (2^6 - 1) << 6\n        d = chunk & 63; // 63       = 2^6 - 1\n\n        // Convert the raw binary segments to the appropriate ASCII encoding\n        base64 += encodings[a] + encodings[b] + encodings[c] + encodings[d];\n    }\n\n    // Deal with the remaining bytes and padding\n    if (byteRemainder == 1) {\n        chunk = bytes[mainLength];\n\n        a = (chunk & 252) >> 2; // 252 = (2^6 - 1) << 2\n\n        // Set the 4 least significant bits to zero\n        b = (chunk & 3) << 4; // 3   = 2^2 - 1\n\n        base64 += encodings[a] + encodings[b] + '==';\n    } else if (byteRemainder == 2) {\n        chunk = (bytes[mainLength] << 8) | bytes[mainLength + 1];\n\n        a = (chunk & 64512) >> 10; // 64512 = (2^6 - 1) << 10\n        b = (chunk & 1008) >> 4; // 1008  = (2^6 - 1) << 4\n\n        // Set the 2 least significant bits to zero\n        c = (chunk & 15) << 2; // 15    = 2^4 - 1\n\n        base64 += encodings[a] + encodings[b] + encodings[c] + '=';\n    }\n\n    return base64;\n}\n", "import MimeNode from './mime-node.js';\nimport { textToHtml, htmlToText, formatTextHeader, formatHtmlHeader } from './text-format.js';\nimport addressParser from './address-parser.js';\nimport { decodeWords, textEncoder, blobToArrayBuffer } from './decode-strings.js';\nimport { base64ArrayBuffer } from './base64-encoder.js';\n\nexport { addressParser, decodeWords };\n\nexport default class PostalMime {\n    static parse(buf, options) {\n        const parser = new PostalMime(options);\n        return parser.parse(buf);\n    }\n\n    constructor(options) {\n        this.options = options || {};\n\n        this.root = this.currentNode = new MimeNode({\n            postalMime: this\n        });\n        this.boundaries = [];\n\n        this.textContent = {};\n        this.attachments = [];\n\n        this.attachmentEncoding =\n            (this.options.attachmentEncoding || '')\n                .toString()\n                .replace(/[-_\\s]/g, '')\n                .trim()\n                .toLowerCase() || 'arraybuffer';\n\n        this.started = false;\n    }\n\n    async finalize() {\n        // close all pending nodes\n        await this.root.finalize();\n    }\n\n    async processLine(line, isFinal) {\n        let boundaries = this.boundaries;\n\n        // check if this is a mime boundary\n        if (boundaries.length && line.length > 2 && line[0] === 0x2d && line[1] === 0x2d) {\n            // could be a boundary marker\n            for (let i = boundaries.length - 1; i >= 0; i--) {\n                let boundary = boundaries[i];\n\n                if (line.length !== boundary.value.length + 2 && line.length !== boundary.value.length + 4) {\n                    continue;\n                }\n\n                let isTerminator = line.length === boundary.value.length + 4;\n\n                if (isTerminator && (line[line.length - 2] !== 0x2d || line[line.length - 1] !== 0x2d)) {\n                    continue;\n                }\n\n                let boudaryMatches = true;\n                for (let i = 0; i < boundary.value.length; i++) {\n                    if (line[i + 2] !== boundary.value[i]) {\n                        boudaryMatches = false;\n                        break;\n                    }\n                }\n                if (!boudaryMatches) {\n                    continue;\n                }\n\n                if (isTerminator) {\n                    await boundary.node.finalize();\n\n                    this.currentNode = boundary.node.parentNode || this.root;\n                } else {\n                    // finalize any open child nodes (should be just one though)\n                    await boundary.node.finalizeChildNodes();\n\n                    this.currentNode = new MimeNode({\n                        postalMime: this,\n                        parentNode: boundary.node\n                    });\n                }\n\n                if (isFinal) {\n                    return this.finalize();\n                }\n\n                return;\n            }\n        }\n\n        this.currentNode.feed(line);\n\n        if (isFinal) {\n            return this.finalize();\n        }\n    }\n\n    readLine() {\n        let startPos = this.readPos;\n        let endPos = this.readPos;\n\n        let res = () => {\n            return {\n                bytes: new Uint8Array(this.buf, startPos, endPos - startPos),\n                done: this.readPos >= this.av.length\n            };\n        };\n\n        while (this.readPos < this.av.length) {\n            const c = this.av[this.readPos++];\n\n            if (c !== 0x0d && c !== 0x0a) {\n                endPos = this.readPos;\n            }\n\n            if (c === 0x0a) {\n                return res();\n            }\n        }\n\n        return res();\n    }\n\n    async processNodeTree() {\n        // get text nodes\n\n        let textContent = {};\n\n        let textTypes = new Set();\n        let textMap = (this.textMap = new Map());\n\n        let forceRfc822Attachments = this.forceRfc822Attachments();\n\n        let walk = async (node, alternative, related) => {\n            alternative = alternative || false;\n            related = related || false;\n\n            if (!node.contentType.multipart) {\n                // is it inline message/rfc822\n                if (this.isInlineMessageRfc822(node) && !forceRfc822Attachments) {\n                    const subParser = new PostalMime();\n                    node.subMessage = await subParser.parse(node.content);\n\n                    if (!textMap.has(node)) {\n                        textMap.set(node, {});\n                    }\n\n                    let textEntry = textMap.get(node);\n\n                    // default to text if there is no content\n                    if (node.subMessage.text || !node.subMessage.html) {\n                        textEntry.plain = textEntry.plain || [];\n                        textEntry.plain.push({ type: 'subMessage', value: node.subMessage });\n                        textTypes.add('plain');\n                    }\n\n                    if (node.subMessage.html) {\n                        textEntry.html = textEntry.html || [];\n                        textEntry.html.push({ type: 'subMessage', value: node.subMessage });\n                        textTypes.add('html');\n                    }\n\n                    if (subParser.textMap) {\n                        subParser.textMap.forEach((subTextEntry, subTextNode) => {\n                            textMap.set(subTextNode, subTextEntry);\n                        });\n                    }\n\n                    for (let attachment of node.subMessage.attachments || []) {\n                        this.attachments.push(attachment);\n                    }\n                }\n\n                // is it text?\n                else if (this.isInlineTextNode(node)) {\n                    let textType = node.contentType.parsed.value.substr(node.contentType.parsed.value.indexOf('/') + 1);\n\n                    let selectorNode = alternative || node;\n                    if (!textMap.has(selectorNode)) {\n                        textMap.set(selectorNode, {});\n                    }\n\n                    let textEntry = textMap.get(selectorNode);\n                    textEntry[textType] = textEntry[textType] || [];\n                    textEntry[textType].push({ type: 'text', value: node.getTextContent() });\n                    textTypes.add(textType);\n                }\n\n                // is it an attachment\n                else if (node.content) {\n                    const filename = node.contentDisposition.parsed.params.filename || node.contentType.parsed.params.name || null;\n                    const attachment = {\n                        filename: filename ? decodeWords(filename) : null,\n                        mimeType: node.contentType.parsed.value,\n                        disposition: node.contentDisposition.parsed.value || null\n                    };\n\n                    if (related && node.contentId) {\n                        attachment.related = true;\n                    }\n\n                    if (node.contentDescription) {\n                        attachment.description = node.contentDescription;\n                    }\n\n                    if (node.contentId) {\n                        attachment.contentId = node.contentId;\n                    }\n\n                    switch (node.contentType.parsed.value) {\n                        // Special handling for calendar events\n                        case 'text/calendar':\n                        case 'application/ics': {\n                            if (node.contentType.parsed.params.method) {\n                                attachment.method = node.contentType.parsed.params.method.toString().toUpperCase().trim();\n                            }\n\n                            // Enforce into unicode\n                            const decodedText = node.getTextContent().replace(/\\r?\\n/g, '\\n').replace(/\\n*$/, '\\n');\n                            attachment.content = textEncoder.encode(decodedText);\n                            break;\n                        }\n\n                        // Regular attachments\n                        default:\n                            attachment.content = node.content;\n                    }\n\n                    this.attachments.push(attachment);\n                }\n            } else if (node.contentType.multipart === 'alternative') {\n                alternative = node;\n            } else if (node.contentType.multipart === 'related') {\n                related = node;\n            }\n\n            for (let childNode of node.childNodes) {\n                await walk(childNode, alternative, related);\n            }\n        };\n\n        await walk(this.root, false, []);\n\n        textMap.forEach(mapEntry => {\n            textTypes.forEach(textType => {\n                if (!textContent[textType]) {\n                    textContent[textType] = [];\n                }\n\n                if (mapEntry[textType]) {\n                    mapEntry[textType].forEach(textEntry => {\n                        switch (textEntry.type) {\n                            case 'text':\n                                textContent[textType].push(textEntry.value);\n                                break;\n\n                            case 'subMessage':\n                                {\n                                    switch (textType) {\n                                        case 'html':\n                                            textContent[textType].push(formatHtmlHeader(textEntry.value));\n                                            break;\n                                        case 'plain':\n                                            textContent[textType].push(formatTextHeader(textEntry.value));\n                                            break;\n                                    }\n                                }\n                                break;\n                        }\n                    });\n                } else {\n                    let alternativeType;\n                    switch (textType) {\n                        case 'html':\n                            alternativeType = 'plain';\n                            break;\n                        case 'plain':\n                            alternativeType = 'html';\n                            break;\n                    }\n\n                    (mapEntry[alternativeType] || []).forEach(textEntry => {\n                        switch (textEntry.type) {\n                            case 'text':\n                                switch (textType) {\n                                    case 'html':\n                                        textContent[textType].push(textToHtml(textEntry.value));\n                                        break;\n                                    case 'plain':\n                                        textContent[textType].push(htmlToText(textEntry.value));\n                                        break;\n                                }\n                                break;\n\n                            case 'subMessage':\n                                {\n                                    switch (textType) {\n                                        case 'html':\n                                            textContent[textType].push(formatHtmlHeader(textEntry.value));\n                                            break;\n                                        case 'plain':\n                                            textContent[textType].push(formatTextHeader(textEntry.value));\n                                            break;\n                                    }\n                                }\n                                break;\n                        }\n                    });\n                }\n            });\n        });\n\n        Object.keys(textContent).forEach(textType => {\n            textContent[textType] = textContent[textType].join('\\n');\n        });\n\n        this.textContent = textContent;\n    }\n\n    isInlineTextNode(node) {\n        if (node.contentDisposition.parsed.value === 'attachment') {\n            // no matter the type, this is an attachment\n            return false;\n        }\n\n        switch (node.contentType.parsed.value) {\n            case 'text/html':\n            case 'text/plain':\n                return true;\n\n            case 'text/calendar':\n            case 'text/csv':\n            default:\n                return false;\n        }\n    }\n\n    isInlineMessageRfc822(node) {\n        if (node.contentType.parsed.value !== 'message/rfc822') {\n            return false;\n        }\n        let disposition = node.contentDisposition.parsed.value || (this.options.rfc822Attachments ? 'attachment' : 'inline');\n        return disposition === 'inline';\n    }\n\n    // Check if this is a specially crafted report email where message/rfc822 content should not be inlined\n    forceRfc822Attachments() {\n        if (this.options.forceRfc822Attachments) {\n            return true;\n        }\n\n        let forceRfc822Attachments = false;\n        let walk = node => {\n            if (!node.contentType.multipart) {\n                if (['message/delivery-status', 'message/feedback-report'].includes(node.contentType.parsed.value)) {\n                    forceRfc822Attachments = true;\n                }\n            }\n\n            for (let childNode of node.childNodes) {\n                walk(childNode);\n            }\n        };\n        walk(this.root);\n        return forceRfc822Attachments;\n    }\n\n    async resolveStream(stream) {\n        let chunkLen = 0;\n        let chunks = [];\n        const reader = stream.getReader();\n\n        while (true) {\n            const { done, value } = await reader.read();\n            if (done) {\n                break;\n            }\n            chunks.push(value);\n            chunkLen += value.length;\n        }\n\n        const result = new Uint8Array(chunkLen);\n        let chunkPointer = 0;\n        for (let chunk of chunks) {\n            result.set(chunk, chunkPointer);\n            chunkPointer += chunk.length;\n        }\n\n        return result;\n    }\n\n    async parse(buf) {\n        if (this.started) {\n            throw new Error('Can not reuse parser, create a new PostalMime object');\n        }\n        this.started = true;\n\n        // Check if the input is a readable stream and resolve it into an ArrayBuffer\n        if (buf && typeof buf.getReader === 'function') {\n            buf = await this.resolveStream(buf);\n        }\n\n        // Should it throw for an empty value instead of defaulting to an empty ArrayBuffer?\n        buf = buf || new ArrayBuffer(0);\n\n        // Cast string input to Uint8Array\n        if (typeof buf === 'string') {\n            buf = textEncoder.encode(buf);\n        }\n\n        // Cast Blob to ArrayBuffer\n        if (buf instanceof Blob || Object.prototype.toString.call(buf) === '[object Blob]') {\n            buf = await blobToArrayBuffer(buf);\n        }\n\n        // Cast Node.js Buffer object or Uint8Array into ArrayBuffer\n        if (buf.buffer instanceof ArrayBuffer) {\n            buf = new Uint8Array(buf).buffer;\n        }\n\n        this.buf = buf;\n\n        this.av = new Uint8Array(buf);\n        this.readPos = 0;\n\n        while (this.readPos < this.av.length) {\n            const line = this.readLine();\n\n            await this.processLine(line.bytes, line.done);\n        }\n\n        await this.processNodeTree();\n\n        const message = {\n            headers: this.root.headers.map(entry => ({ key: entry.key, value: entry.value })).reverse()\n        };\n\n        for (const key of ['from', 'sender']) {\n            const addressHeader = this.root.headers.find(line => line.key === key);\n            if (addressHeader && addressHeader.value) {\n                const addresses = addressParser(addressHeader.value);\n                if (addresses && addresses.length) {\n                    message[key] = addresses[0];\n                }\n            }\n        }\n\n        for (const key of ['delivered-to', 'return-path']) {\n            const addressHeader = this.root.headers.find(line => line.key === key);\n            if (addressHeader && addressHeader.value) {\n                const addresses = addressParser(addressHeader.value);\n                if (addresses && addresses.length && addresses[0].address) {\n                    const camelKey = key.replace(/\\-(.)/g, (o, c) => c.toUpperCase());\n                    message[camelKey] = addresses[0].address;\n                }\n            }\n        }\n\n        for (const key of ['to', 'cc', 'bcc', 'reply-to']) {\n            const addressHeaders = this.root.headers.filter(line => line.key === key);\n            let addresses = [];\n\n            addressHeaders\n                .filter(entry => entry && entry.value)\n                .map(entry => addressParser(entry.value))\n                .forEach(parsed => (addresses = addresses.concat(parsed || [])));\n\n            if (addresses && addresses.length) {\n                const camelKey = key.replace(/\\-(.)/g, (o, c) => c.toUpperCase());\n                message[camelKey] = addresses;\n            }\n        }\n\n        for (const key of ['subject', 'message-id', 'in-reply-to', 'references']) {\n            const header = this.root.headers.find(line => line.key === key);\n            if (header && header.value) {\n                const camelKey = key.replace(/\\-(.)/g, (o, c) => c.toUpperCase());\n                message[camelKey] = decodeWords(header.value);\n            }\n        }\n\n        let dateHeader = this.root.headers.find(line => line.key === 'date');\n        if (dateHeader) {\n            let date = new Date(dateHeader.value);\n            if (!date || date.toString() === 'Invalid Date') {\n                date = dateHeader.value;\n            } else {\n                // enforce ISO format if seems to be a valid date\n                date = date.toISOString();\n            }\n            message.date = date;\n        }\n\n        if (this.textContent?.html) {\n            message.html = this.textContent.html;\n        }\n\n        if (this.textContent?.plain) {\n            message.text = this.textContent.plain;\n        }\n\n        message.attachments = this.attachments;\n\n        switch (this.attachmentEncoding) {\n            case 'arraybuffer':\n                break;\n\n            case 'base64':\n                for (let attachment of message.attachments || []) {\n                    if (attachment?.content) {\n                        attachment.content = base64ArrayBuffer(attachment.content);\n                        attachment.encoding = 'base64';\n                    }\n                }\n                break;\n\n            case 'utf8':\n                let attachmentDecoder = new TextDecoder('utf8');\n                for (let attachment of message.attachments || []) {\n                    if (attachment?.content) {\n                        attachment.content = attachmentDecoder.decode(attachment.content);\n                        attachment.encoding = 'utf8';\n                    }\n                }\n                break;\n\n            default:\n                throw new Error('Unknwon attachment encoding');\n        }\n\n        return message;\n    }\n}\n", "import PostalMime from 'postal-mime'\nimport type { ParsedEmail } from '@/types'\n\nexport class EmailParserService {\n  async parseEmail(rawEmail: string | ArrayBuffer): Promise<ParsedEmail> {\n    try {\n      const parser = new PostalMime()\n      const email = await parser.parse(rawEmail)\n\n      // 提取基本信息\n      const from = {\n        address: email.from?.address || '',\n        name: email.from?.name || ''\n      }\n\n      const to = email.to?.[0]?.address || ''\n      const subject = email.subject || '无主题'\n      const text = email.text || ''\n      const html = email.html || ''\n\n      // 尝试提取验证码\n      const verificationCode = this.extractVerificationCode(text + ' ' + html)\n\n      return {\n        from,\n        to,\n        subject,\n        text,\n        html,\n        verificationCode\n      }\n    } catch (error) {\n      console.error('Email parsing error:', error)\n      \n      // 返回解析失败的默认结构\n      return {\n        from: { address: '', name: '' },\n        to: '',\n        subject: '邮件解析失败',\n        text: '邮件内容解析失败，请查看原始邮件',\n        html: '',\n        verificationCode: undefined\n      }\n    }\n  }\n\n  private extractVerificationCode(content: string): string | undefined {\n    if (!content) return undefined\n\n    // 常见的验证码模式\n    const patterns = [\n      // 中文验证码模式\n      /验证码[：:\\s]*([0-9]{4,8})/i,\n      /您的验证码是[：:\\s]*([0-9]{4,8})/i,\n      /验证码为[：:\\s]*([0-9]{4,8})/i,\n      /动态码[：:\\s]*([0-9]{4,8})/i,\n      /安全码[：:\\s]*([0-9]{4,8})/i,\n      \n      // 英文验证码模式\n      /verification code[：:\\s]*([0-9]{4,8})/i,\n      /your verification code is[：:\\s]*([0-9]{4,8})/i,\n      /code[：:\\s]*([0-9]{4,8})/i,\n      /pin[：:\\s]*([0-9]{4,8})/i,\n      /otp[：:\\s]*([0-9]{4,8})/i,\n      /security code[：:\\s]*([0-9]{4,8})/i,\n      /access code[：:\\s]*([0-9]{4,8})/i,\n      \n      // 通用数字模式\n      /\\b([0-9]{4,8})\\b.*验证/i,\n      /\\b([0-9]{4,8})\\b.*code/i,\n      /\\b([0-9]{6})\\b/g, // 6位数字（最常见的验证码格式）\n      \n      // 特殊格式\n      /(\\d{3}[-\\s]\\d{3})/g, // 123-456 或 123 456 格式\n      /(\\d{2}[-\\s]\\d{2}[-\\s]\\d{2})/g, // 12-34-56 格式\n    ]\n\n    for (const pattern of patterns) {\n      const match = content.match(pattern)\n      if (match && match[1]) {\n        const code = match[1].replace(/[-\\s]/g, '') // 移除分隔符\n        \n        // 验证码长度检查\n        if (code.length >= 4 && code.length <= 8) {\n          return code\n        }\n      }\n    }\n\n    // 如果没有找到明确的验证码模式，尝试查找独立的4-8位数字\n    const standaloneNumbers = content.match(/\\b\\d{4,8}\\b/g)\n    if (standaloneNumbers && standaloneNumbers.length > 0) {\n      // 返回第一个找到的4-8位数字\n      return standaloneNumbers[0]\n    }\n\n    return undefined\n  }\n\n  // 提取邮件中的链接\n  extractLinks(content: string): string[] {\n    const linkPattern = /https?:\\/\\/[^\\s<>\"]+/gi\n    const matches = content.match(linkPattern)\n    return matches || []\n  }\n\n  // 提取邮件中的电话号码\n  extractPhoneNumbers(content: string): string[] {\n    const phonePatterns = [\n      /\\b\\d{3}[-.]?\\d{3}[-.]?\\d{4}\\b/g, // 美国格式\n      /\\b\\d{3}[-\\s]?\\d{4}[-\\s]?\\d{4}\\b/g, // 中国格式\n      /\\+\\d{1,3}[-\\s]?\\d{3,4}[-\\s]?\\d{3,4}[-\\s]?\\d{3,4}/g // 国际格式\n    ]\n\n    const phoneNumbers: string[] = []\n    phonePatterns.forEach(pattern => {\n      const matches = content.match(pattern)\n      if (matches) {\n        phoneNumbers.push(...matches)\n      }\n    })\n\n    return phoneNumbers\n  }\n\n  // 检测邮件类型\n  detectEmailType(subject: string, content: string): string {\n    const lowerSubject = subject.toLowerCase()\n    const lowerContent = content.toLowerCase()\n\n    // 验证码邮件\n    if (lowerSubject.includes('verification') || \n        lowerSubject.includes('验证') ||\n        lowerContent.includes('verification code') ||\n        lowerContent.includes('验证码')) {\n      return 'verification'\n    }\n\n    // 重置密码邮件\n    if (lowerSubject.includes('reset') || \n        lowerSubject.includes('password') ||\n        lowerSubject.includes('重置') ||\n        lowerSubject.includes('密码')) {\n      return 'password_reset'\n    }\n\n    // 注册确认邮件\n    if (lowerSubject.includes('confirm') || \n        lowerSubject.includes('welcome') ||\n        lowerSubject.includes('确认') ||\n        lowerSubject.includes('欢迎')) {\n      return 'registration'\n    }\n\n    // 通知邮件\n    if (lowerSubject.includes('notification') || \n        lowerSubject.includes('alert') ||\n        lowerSubject.includes('通知') ||\n        lowerSubject.includes('提醒')) {\n      return 'notification'\n    }\n\n    // 营销邮件\n    if (lowerSubject.includes('offer') || \n        lowerSubject.includes('sale') ||\n        lowerSubject.includes('discount') ||\n        lowerSubject.includes('优惠') ||\n        lowerSubject.includes('促销')) {\n      return 'marketing'\n    }\n\n    return 'general'\n  }\n\n  // 清理HTML内容，提取纯文本\n  stripHtml(html: string): string {\n    if (!html) return ''\n\n    return html\n      .replace(/<script[^>]*>.*?<\\/script>/gi, '') // 移除脚本\n      .replace(/<style[^>]*>.*?<\\/style>/gi, '') // 移除样式\n      .replace(/<[^>]+>/g, '') // 移除HTML标签\n      .replace(/&nbsp;/g, ' ') // 替换空格实体\n      .replace(/&amp;/g, '&') // 替换&实体\n      .replace(/&lt;/g, '<') // 替换<实体\n      .replace(/&gt;/g, '>') // 替换>实体\n      .replace(/&quot;/g, '\"') // 替换\"实体\n      .replace(/&#39;/g, \"'\") // 替换'实体\n      .replace(/\\s+/g, ' ') // 合并多个空格\n      .trim()\n  }\n}\n", "import type {\n  Env,\n  TempEmail,\n  Email,\n  Domain,\n  CreateEmailRequest,\n  RedeemRequest,\n  PaginationParams,\n  PaginatedResponse\n} from '@/types'\nimport { ValidationError, NotFoundError } from '@/types'\nimport { DatabaseService } from '@/modules/shared/database.service'\nimport { EmailParserService } from './parser.service'\n\nexport class EmailService {\n  private parserService: EmailParserService\n\n  constructor(\n    private env: Env,\n    private dbService: DatabaseService\n  ) {\n    this.parserService = new EmailParserService()\n  }\n\n  async createTempEmail(userId: number, request: CreateEmailRequest): Promise<TempEmail> {\n    // 1. 检查用户配额\n    const user = await this.dbService.getUserById(userId)\n    if (!user) {\n      throw new NotFoundError('用户不存在')\n    }\n\n    if (user.quota <= 0) {\n      throw new ValidationError('配额不足，无法创建临时邮箱')\n    }\n\n    // 2. 验证域名\n    const domain = await this.dbService.getDomainById(request.domainId)\n    if (!domain || domain.status !== 1) {\n      throw new ValidationError('无效的域名')\n    }\n\n    // 3. 生成随机邮箱前缀\n    const prefix = this.generateEmailPrefix()\n    const email = `${prefix}@${domain.domain}`\n\n    // 4. 检查邮箱是否已存在\n    const existingEmail = await this.dbService.getTempEmailByEmail(email)\n    if (existingEmail) {\n      // 如果存在，重新生成\n      return this.createTempEmail(userId, request)\n    }\n\n    // 5. 原子操作：扣除配额并创建邮箱\n    const success = await this.dbService.decrementUserQuota(userId)\n    if (!success) {\n      throw new ValidationError('配额不足，无法创建临时邮箱')\n    }\n\n    try {\n      const tempEmail = await this.dbService.createTempEmail(userId, email, request.domainId)\n\n      // 6. 记录日志\n      await this.dbService.createLog({\n        userId,\n        action: 'CREATE_EMAIL',\n        details: `Created temp email: ${email}`\n      })\n\n      return tempEmail\n    } catch (error) {\n      // 如果创建失败，恢复配额\n      await this.dbService.updateUserQuota(userId, user.quota)\n      throw error\n    }\n  }\n\n  async getTempEmails(userId: number): Promise<TempEmail[]> {\n    return await this.dbService.getTempEmailsByUserId(userId)\n  }\n\n  async deleteTempEmail(userId: number, emailId: number): Promise<void> {\n    const success = await this.dbService.deleteTempEmail(emailId, userId)\n    if (!success) {\n      throw new NotFoundError('临时邮箱不存在或无权限删除')\n    }\n\n    // 记录日志\n    await this.dbService.createLog({\n      userId,\n      action: 'DELETE_EMAIL',\n      details: `Deleted temp email ID: ${emailId}`\n    })\n  }\n\n  async getEmailsForTempEmail(\n    userId: number, \n    tempEmailId: number, \n    pagination: PaginationParams\n  ): Promise<PaginatedResponse<Email>> {\n    // 验证临时邮箱是否属于当前用户\n    const tempEmails = await this.dbService.getTempEmailsByUserId(userId)\n    const tempEmail = tempEmails.find(email => email.id === tempEmailId)\n    \n    if (!tempEmail) {\n      throw new NotFoundError('临时邮箱不存在或无权限访问')\n    }\n\n    return await this.dbService.getEmailsForTempEmail(tempEmailId, pagination)\n  }\n\n  async deleteEmail(userId: number, emailId: number): Promise<void> {\n    // 这里需要验证邮件是否属于用户的临时邮箱\n    // 为简化，暂时直接删除，实际应用中需要添加权限检查\n    const success = await this.dbService.deleteEmail(emailId)\n    if (!success) {\n      throw new NotFoundError('邮件不存在')\n    }\n\n    // 记录日志\n    await this.dbService.createLog({\n      userId,\n      action: 'DELETE_EMAIL_CONTENT',\n      details: `Deleted email ID: ${emailId}`\n    })\n  }\n\n  async getActiveDomains(): Promise<Domain[]> {\n    return await this.dbService.getActiveDomains()\n  }\n\n  async redeemCode(userId: number, request: RedeemRequest): Promise<{ quota: number }> {\n    // 1. 验证兑换码\n    const redeemCode = await this.dbService.getRedeemCode(request.code)\n    if (!redeemCode) {\n      throw new ValidationError('兑换码不存在')\n    }\n\n    if (redeemCode.used) {\n      throw new ValidationError('兑换码已被使用')\n    }\n\n    if (new Date(redeemCode.valid_until) < new Date()) {\n      throw new ValidationError('兑换码已过期')\n    }\n\n    // 2. 使用兑换码\n    const success = await this.dbService.useRedeemCode(request.code, userId)\n    if (!success) {\n      throw new ValidationError('兑换码使用失败')\n    }\n\n    // 3. 更新用户配额\n    const user = await this.dbService.getUserById(userId)\n    if (!user) {\n      throw new NotFoundError('用户不存在')\n    }\n\n    const newQuota = user.quota + redeemCode.quota\n    await this.dbService.updateUserQuota(userId, newQuota)\n\n    // 4. 记录日志\n    await this.dbService.createLog({\n      userId,\n      action: 'REDEEM_CODE',\n      details: `Redeemed code: ${request.code}, quota: ${redeemCode.quota}`\n    })\n\n    return { quota: newQuota }\n  }\n\n  // 处理接收到的邮件（由Email Routing触发）\n  async handleIncomingEmail(rawEmail: string | ArrayBuffer, recipientEmail: string): Promise<void> {\n    try {\n      console.log('Processing incoming email for:', recipientEmail)\n\n      // 1. 查找对应的临时邮箱\n      const tempEmail = await this.dbService.getTempEmailByEmail(recipientEmail)\n      if (!tempEmail || !tempEmail.active) {\n        console.log('Temp email not found or inactive:', recipientEmail)\n        return\n      }\n\n      // 2. 解析邮件内容\n      const parsedEmail = await this.parserService.parseEmail(rawEmail)\n\n      // 3. 存储邮件到数据库\n      const email = await this.dbService.createEmail({\n        tempEmailId: tempEmail.id,\n        sender: parsedEmail.from.address,\n        subject: parsedEmail.subject,\n        content: parsedEmail.text,\n        htmlContent: parsedEmail.html,\n        verificationCode: parsedEmail.verificationCode\n      })\n\n      console.log('Email stored successfully:', email.id)\n\n      // 4. 这里可以添加实时推送逻辑（WebSocket等）\n      // await this.notifyUser(tempEmail.user_id, email)\n\n    } catch (error) {\n      console.error('Error handling incoming email:', error)\n    }\n  }\n\n  private generateEmailPrefix(): string {\n    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'\n    let result = ''\n    \n    // 生成8-12位随机字符串\n    const length = Math.floor(Math.random() * 5) + 8\n    \n    for (let i = 0; i < length; i++) {\n      result += chars.charAt(Math.floor(Math.random() * chars.length))\n    }\n    \n    return result\n  }\n\n  // 获取用户配额信息\n  async getQuotaInfo(userId: number): Promise<{ quota: number; used: number }> {\n    const user = await this.dbService.getUserById(userId)\n    if (!user) {\n      throw new NotFoundError('用户不存在')\n    }\n\n    const tempEmails = await this.dbService.getTempEmailsByUserId(userId)\n    const used = tempEmails.filter(email => email.active).length\n\n    return {\n      quota: user.quota,\n      used\n    }\n  }\n\n  // 搜索邮件\n  async searchEmails(\n    userId: number,\n    params: {\n      tempEmailId?: number\n      keyword?: string\n      sender?: string\n      dateFrom?: string\n      dateTo?: string\n    } & PaginationParams\n  ): Promise<PaginatedResponse<Email>> {\n    // 这里需要实现复杂的搜索逻辑\n    // 为简化，暂时返回空结果\n    return {\n      data: [],\n      total: 0,\n      page: params.page,\n      limit: params.limit,\n      totalPages: 0\n    }\n  }\n}\n", "import type { \n  Env, \n  CreateEmailRequest, \n  RedeemRequest, \n  ApiResponse,\n  PaginationParams \n} from '@/types'\nimport { EmailService } from '@/modules/email/email.service'\nimport { DatabaseService } from '@/modules/shared/database.service'\nimport { TurnstileService } from '@/middleware/turnstile.middleware'\nimport { withAuth, type AuthenticatedRequest } from '@/middleware/auth.middleware'\nimport type { JWTPayload } from '@/types'\n\nexport class EmailHandler {\n  private emailService: EmailService\n  private turnstileService: TurnstileService\n  public createTempEmail: (request: Request) => Promise<Response>\n  public getTempEmails: (request: Request) => Promise<Response>\n  public deleteTempEmail: (request: Request) => Promise<Response>\n  public getEmailsForTempEmail: (request: Request) => Promise<Response>\n  public deleteEmail: (request: Request) => Promise<Response>\n  public redeemCode: (request: Request) => Promise<Response>\n  public getQuotaInfo: (request: Request) => Promise<Response>\n\n  constructor(private env: Env) {\n    const dbService = new DatabaseService(env.DB)\n    this.emailService = new EmailService(env, dbService)\n    this.turnstileService = new TurnstileService(env)\n\n    // 初始化需要认证的方法\n    this.createTempEmail = withAuth(this.env)((request: AuthenticatedRequest, user: JWTPayload) => {\n      return this.handleCreateTempEmail(request, user)\n    })\n\n    this.getTempEmails = withAuth(this.env)((request: AuthenticatedRequest, user: JWTPayload) => {\n      return this.handleGetTempEmails(request, user)\n    })\n\n    this.deleteTempEmail = withAuth(this.env)((request: AuthenticatedRequest, user: JWTPayload) => {\n      return this.handleDeleteTempEmail(request, user)\n    })\n\n    this.getEmailsForTempEmail = withAuth(this.env)((request: AuthenticatedRequest, user: JWTPayload) => {\n      return this.handleGetEmailsForTempEmail(request, user)\n    })\n\n    this.deleteEmail = withAuth(this.env)((request: AuthenticatedRequest, user: JWTPayload) => {\n      return this.handleDeleteEmail(request, user)\n    })\n\n    this.redeemCode = withAuth(this.env)((request: AuthenticatedRequest, user: JWTPayload) => {\n      return this.handleRedeemCode(request, user)\n    })\n\n    this.getQuotaInfo = withAuth(this.env)((request: AuthenticatedRequest, user: JWTPayload) => {\n      return this.handleGetQuotaInfo(request, user)\n    })\n  }\n\n  // 需要认证的路由处理器已在构造函数中初始化\n\n  // 公开路由\n  async getDomains(request: Request): Promise<Response> {\n    try {\n      const domains = await this.emailService.getActiveDomains()\n      return this.successResponse(domains)\n    } catch (error: any) {\n      console.error('Get domains error:', error)\n      return this.errorResponse(error.message || '获取域名列表失败', error.statusCode || 500)\n    }\n  }\n\n  // 邮件接收处理（由Email Routing触发）\n  async handleIncomingEmail(request: Request): Promise<Response> {\n    try {\n      const url = new URL(request.url)\n      const recipientEmail = url.searchParams.get('to')\n      \n      if (!recipientEmail) {\n        return this.errorResponse('缺少收件人邮箱', 400)\n      }\n\n      const rawEmail = await request.arrayBuffer()\n      await this.emailService.handleIncomingEmail(rawEmail, recipientEmail)\n\n      return this.successResponse(null, '邮件处理成功')\n    } catch (error: any) {\n      console.error('Handle incoming email error:', error)\n      return this.errorResponse(error.message || '邮件处理失败', error.statusCode || 500)\n    }\n  }\n\n  private async handleCreateTempEmail(request: AuthenticatedRequest, user: JWTPayload): Promise<Response> {\n    try {\n      const data: CreateEmailRequest = await request.json()\n\n      // 验证Turnstile\n      const clientIP = request.headers.get('CF-Connecting-IP') || \n                      request.headers.get('X-Forwarded-For')\n      \n      const isTurnstileValid = await this.turnstileService.verifyToken(\n        data.turnstileToken, \n        clientIP || undefined\n      )\n\n      if (!isTurnstileValid) {\n        return this.errorResponse('人机验证失败', 400)\n      }\n\n      const tempEmail = await this.emailService.createTempEmail(user.userId, data)\n      return this.successResponse(tempEmail, '临时邮箱创建成功')\n    } catch (error: any) {\n      console.error('Create temp email error:', error)\n      return this.errorResponse(error.message || '创建临时邮箱失败', error.statusCode || 500)\n    }\n  }\n\n  private async handleGetTempEmails(request: AuthenticatedRequest, user: JWTPayload): Promise<Response> {\n    try {\n      const tempEmails = await this.emailService.getTempEmails(user.userId)\n      return this.successResponse(tempEmails)\n    } catch (error: any) {\n      console.error('Get temp emails error:', error)\n      return this.errorResponse(error.message || '获取临时邮箱列表失败', error.statusCode || 500)\n    }\n  }\n\n  private async handleDeleteTempEmail(request: AuthenticatedRequest, user: JWTPayload): Promise<Response> {\n    try {\n      const url = new URL(request.url)\n      const emailId = parseInt(url.pathname.split('/').pop() || '0')\n\n      if (!emailId) {\n        return this.errorResponse('无效的邮箱ID', 400)\n      }\n\n      await this.emailService.deleteTempEmail(user.userId, emailId)\n      return this.successResponse(null, '临时邮箱删除成功')\n    } catch (error: any) {\n      console.error('Delete temp email error:', error)\n      return this.errorResponse(error.message || '删除临时邮箱失败', error.statusCode || 500)\n    }\n  }\n\n  private async handleGetEmailsForTempEmail(request: AuthenticatedRequest, user: JWTPayload): Promise<Response> {\n    try {\n      const url = new URL(request.url)\n      const pathParts = url.pathname.split('/')\n      const tempEmailId = parseInt(pathParts[pathParts.length - 2] || '0')\n\n      if (!tempEmailId) {\n        return this.errorResponse('无效的临时邮箱ID', 400)\n      }\n\n      // 解析分页参数\n      const page = parseInt(url.searchParams.get('page') || '1')\n      const limit = parseInt(url.searchParams.get('limit') || '20')\n      const offset = (page - 1) * limit\n\n      const pagination: PaginationParams = { page, limit, offset }\n      const emails = await this.emailService.getEmailsForTempEmail(user.userId, tempEmailId, pagination)\n      \n      return this.successResponse(emails)\n    } catch (error: any) {\n      console.error('Get emails for temp email error:', error)\n      return this.errorResponse(error.message || '获取邮件列表失败', error.statusCode || 500)\n    }\n  }\n\n  private async handleDeleteEmail(request: AuthenticatedRequest, user: JWTPayload): Promise<Response> {\n    try {\n      const url = new URL(request.url)\n      const emailId = parseInt(url.pathname.split('/').pop() || '0')\n\n      if (!emailId) {\n        return this.errorResponse('无效的邮件ID', 400)\n      }\n\n      await this.emailService.deleteEmail(user.userId, emailId)\n      return this.successResponse(null, '邮件删除成功')\n    } catch (error: any) {\n      console.error('Delete email error:', error)\n      return this.errorResponse(error.message || '删除邮件失败', error.statusCode || 500)\n    }\n  }\n\n  private async handleRedeemCode(request: AuthenticatedRequest, user: JWTPayload): Promise<Response> {\n    try {\n      const data: RedeemRequest = await request.json()\n\n      // 验证Turnstile\n      const clientIP = request.headers.get('CF-Connecting-IP') || \n                      request.headers.get('X-Forwarded-For')\n      \n      const isTurnstileValid = await this.turnstileService.verifyToken(\n        data.turnstileToken, \n        clientIP || undefined\n      )\n\n      if (!isTurnstileValid) {\n        return this.errorResponse('人机验证失败', 400)\n      }\n\n      const result = await this.emailService.redeemCode(user.userId, data)\n      return this.successResponse(result, '兑换码使用成功')\n    } catch (error: any) {\n      console.error('Redeem code error:', error)\n      return this.errorResponse(error.message || '兑换码使用失败', error.statusCode || 500)\n    }\n  }\n\n  private async handleGetQuotaInfo(request: AuthenticatedRequest, user: JWTPayload): Promise<Response> {\n    try {\n      const quotaInfo = await this.emailService.getQuotaInfo(user.userId)\n      return this.successResponse(quotaInfo)\n    } catch (error: any) {\n      console.error('Get quota info error:', error)\n      return this.errorResponse(error.message || '获取配额信息失败', error.statusCode || 500)\n    }\n  }\n\n  private successResponse<T>(data: T, message?: string): Response {\n    const response: ApiResponse<T> = {\n      success: true,\n      data,\n      message\n    }\n\n    return new Response(JSON.stringify(response), {\n      status: 200,\n      headers: {\n        'Content-Type': 'application/json',\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n      }\n    })\n  }\n\n  private errorResponse(error: string, status: number = 500): Response {\n    const response: ApiResponse = {\n      success: false,\n      error\n    }\n\n    return new Response(JSON.stringify(response), {\n      status,\n      headers: {\n        'Content-Type': 'application/json',\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n      }\n    })\n  }\n}\n", "import type { Env } from '@/types'\nimport { AuthHandler } from '@/handlers/auth.handler'\nimport { EmailHandler } from '@/handlers/email.handler'\n\nexport default {\n  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {\n    const url = new URL(request.url)\n    const { pathname } = url\n    const method = request.method\n\n    // CORS 预检请求处理\n    if (method === 'OPTIONS') {\n      return new Response(null, {\n        status: 200,\n        headers: {\n          'Access-Control-Allow-Origin': '*',\n          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n          'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n          'Access-Control-Max-Age': '86400'\n        }\n      })\n    }\n\n    try {\n      // 初始化处理器\n      const authHandler = new AuthHandler(env)\n      const emailHandler = new EmailHandler(env)\n\n      // 路由匹配\n      if (pathname.startsWith('/api/auth/')) {\n        return await handleAuthRoutes(pathname, method, request, authHandler)\n      } else if (pathname.startsWith('/api/email/')) {\n        return await handleEmailRoutes(pathname, method, request, emailHandler)\n      } else if (pathname === '/api/webhook/email') {\n        // Email Routing webhook\n        return await emailHandler.handleIncomingEmail(request)\n      } else if (pathname === '/api/health') {\n        return new Response(JSON.stringify({ \n          success: true, \n          message: 'Service is healthy',\n          timestamp: new Date().toISOString()\n        }), {\n          status: 200,\n          headers: { 'Content-Type': 'application/json' }\n        })\n      } else {\n        return new Response(JSON.stringify({\n          success: false,\n          error: 'Not Found'\n        }), {\n          status: 404,\n          headers: { 'Content-Type': 'application/json' }\n        })\n      }\n    } catch (error) {\n      console.error('Unhandled error:', error)\n      return new Response(JSON.stringify({\n        success: false,\n        error: 'Internal Server Error'\n      }), {\n        status: 500,\n        headers: { 'Content-Type': 'application/json' }\n      })\n    }\n  }\n}\n\nasync function handleAuthRoutes(\n  pathname: string, \n  method: string, \n  request: Request, \n  handler: AuthHandler\n): Promise<Response> {\n  switch (pathname) {\n    case '/api/auth/register':\n      if (method === 'POST') return await handler.register(request)\n      break\n    \n    case '/api/auth/login':\n      if (method === 'POST') return await handler.login(request)\n      break\n    \n    case '/api/auth/refresh':\n      if (method === 'POST') return await handler.refreshToken(request)\n      break\n    \n    case '/api/auth/logout':\n      if (method === 'POST') return await handler.logout(request)\n      break\n    \n    case '/api/auth/me':\n      if (method === 'GET') return await handler.getCurrentUser(request)\n      break\n    \n    case '/api/auth/change-password':\n      if (method === 'POST') return await handler.changePassword(request)\n      break\n  }\n\n  return new Response(JSON.stringify({\n    success: false,\n    error: 'Method Not Allowed'\n  }), {\n    status: 405,\n    headers: { 'Content-Type': 'application/json' }\n  })\n}\n\nasync function handleEmailRoutes(\n  pathname: string, \n  method: string, \n  request: Request, \n  handler: EmailHandler\n): Promise<Response> {\n  // 域名相关路由（公开）\n  if (pathname === '/api/email/domains') {\n    if (method === 'GET') return await handler.getDomains(request)\n  }\n\n  // 临时邮箱相关路由（需要认证）\n  if (pathname === '/api/email/temp-emails') {\n    if (method === 'GET') return await handler.getTempEmails(request)\n  }\n\n  if (pathname === '/api/email/create') {\n    if (method === 'POST') return await handler.createTempEmail(request)\n  }\n\n  // 删除临时邮箱 /api/email/temp-emails/:id\n  if (pathname.match(/^\\/api\\/email\\/temp-emails\\/\\d+$/)) {\n    if (method === 'DELETE') return await handler.deleteTempEmail(request)\n  }\n\n  // 获取临时邮箱的邮件列表 /api/email/temp-emails/:id/emails\n  if (pathname.match(/^\\/api\\/email\\/temp-emails\\/\\d+\\/emails$/)) {\n    if (method === 'GET') return await handler.getEmailsForTempEmail(request)\n  }\n\n  // 删除邮件 /api/email/emails/:id\n  if (pathname.match(/^\\/api\\/email\\/emails\\/\\d+$/)) {\n    if (method === 'DELETE') return await handler.deleteEmail(request)\n  }\n\n  // 兑换码相关路由\n  if (pathname === '/api/email/redeem') {\n    if (method === 'POST') return await handler.redeemCode(request)\n  }\n\n  // 配额信息\n  if (pathname === '/api/email/quota') {\n    if (method === 'GET') return await handler.getQuotaInfo(request)\n  }\n\n  return new Response(JSON.stringify({\n    success: false,\n    error: 'Method Not Allowed'\n  }), {\n    status: 405,\n    headers: { 'Content-Type': 'application/json' }\n  })\n}\n\n// Email Routing 处理器\nexport async function email(message: any, env: Env, ctx: ExecutionContext) {\n  try {\n    const emailHandler = new EmailHandler(env)\n    \n    // 构造一个模拟的Request对象来处理邮件\n    const url = new URL(`https://temp-email.workers.dev/api/webhook/email?to=${message.to}`)\n    const request = new Request(url.toString(), {\n      method: 'POST',\n      body: message.raw\n    })\n\n    await emailHandler.handleIncomingEmail(request)\n  } catch (error) {\n    console.error('Email routing error:', error)\n  }\n}\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTH<PERSON>_EXPORTS from \"/Users/<USER>/Desktop/code/temp_mail/backend/src/index.ts\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"/usr/local/lib/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"/usr/local/lib/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"/Users/<USER>/Desktop/code/temp_mail/backend/src/index.ts\";\n\t\t\t\tconst MIDDLEWARE_TEST_INJECT = \"__INJECT_FOR_TESTING_WRANGLER_MIDDLEWARE__\";\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"/Users/<USER>/Desktop/code/temp_mail/backend/.wrangler/tmp/bundle-rlXE1v/middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"/usr/local/lib/node_modules/wrangler/templates/middleware/common.ts\";\nimport type { WorkerEntrypointConstructor } from \"/Users/<USER>/Desktop/code/temp_mail/backend/.wrangler/tmp/bundle-rlXE1v/middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"/Users/<USER>/Desktop/code/temp_mail/backend/.wrangler/tmp/bundle-rlXE1v/middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;AAAA,IAAM,OAAO,oBAAI,IAAI;AAErB,SAAS,SAAS,SAAS,MAAM;AAChC,QAAM,MACL,mBAAmB,MAChB,UACA,IAAI;AAAA,KACH,OAAO,YAAY,WACjB,IAAI,QAAQ,SAAS,IAAI,IACzB,SACD;AAAA,EACH;AACH,MAAI,IAAI,QAAQ,IAAI,SAAS,SAAS,IAAI,aAAa,UAAU;AAChE,QAAI,CAAC,KAAK,IAAI,IAAI,SAAS,CAAC,GAAG;AAC9B,WAAK,IAAI,IAAI,SAAS,CAAC;AACvB,cAAQ;AAAA,QACP;AAAA,KACO,IAAI,SAAS,CAAC;AAAA;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AACD;AAnBS;AAqBT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,UAAM,CAAC,SAAS,IAAI,IAAI;AACxB,aAAS,SAAS,IAAI;AACtB,WAAO,QAAQ,MAAM,QAAQ,SAAS,QAAQ;AAAA,EAC/C;AACD,CAAC;;;AC0JM,IAAM,WAAN,cAAuB,MAAM;AAAA,EAClC,YACkB,SACT,aAAqB,KACrB,MACP;AACA,UAAM,OAAO;AAJG;AACT;AACA;AAGP,SAAK,OAAO;AAAA,EACd;AAAA,EA/LF,OAuLoC;AAAA;AAAA;AASpC;AAEO,IAAM,kBAAN,cAA8B,SAAS;AAAA,EAlM9C,OAkM8C;AAAA;AAAA;AAAA,EAC5C,YAAY,SAAiB;AAC3B,UAAM,SAAS,KAAK,kBAAkB;AAAA,EACxC;AACF;AAEO,IAAM,sBAAN,cAAkC,SAAS;AAAA,EAxMlD,OAwMkD;AAAA;AAAA;AAAA,EAChD,YAAY,UAAkB,4BAAQ;AACpC,UAAM,SAAS,KAAK,YAAY;AAAA,EAClC;AACF;AAQO,IAAM,gBAAN,cAA4B,SAAS;AAAA,EApN5C,OAoN4C;AAAA;AAAA;AAAA,EAC1C,YAAY,UAAkB,kCAAS;AACrC,UAAM,SAAS,KAAK,iBAAiB;AAAA,EACvC;AACF;;;ACrNO,IAAM,aAAN,MAAiB;AAAA;AAAA,EAItB,YACU,KACA,WACR;AAFQ;AACA;AAAA,EACP;AAAA,EAVL,OAGwB;AAAA;AAAA;AAAA,EACL,uBAAuB,KAAK,KAAK,KAAK;AAAA;AAAA,EACtC,wBAAwB,KAAK,KAAK,KAAK;AAAA,EAOxD,MAAM,kBAAkB,MAAgC;AACtD,UAAM,cAAc,MAAM,KAAK,oBAAoB,IAAI;AACvD,UAAM,eAAe,MAAM,KAAK,qBAAqB,IAAI;AAGzD,UAAM,KAAK,kBAAkB,KAAK,IAAI,YAAY;AAElD,WAAO,EAAE,aAAa,aAAa;AAAA,EACrC;AAAA,EAEA,MAAc,oBAAoB,MAA6B;AAC7D,UAAM,UAAsB;AAAA,MAC1B,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,MAAM,KAAK;AAAA,MACX,MAAM;AAAA,MACN,KAAK,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AAAA,MACjC,KAAK,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI,IAAI,KAAK;AAAA,IAC5C;AAEA,WAAO,MAAM,KAAK,QAAQ,OAAO;AAAA,EACnC;AAAA,EAEA,MAAc,qBAAqB,MAA6B;AAC9D,UAAM,UAAsB;AAAA,MAC1B,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,MAAM,KAAK;AAAA,MACX,MAAM;AAAA,MACN,KAAK,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AAAA,MACjC,KAAK,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI,IAAI,KAAK;AAAA,IAC5C;AAEA,WAAO,MAAM,KAAK,QAAQ,OAAO;AAAA,EACnC;AAAA,EAEA,MAAc,QAAQ,SAAsC;AAC1D,QAAI,CAAC,KAAK,IAAI,YAAY;AACxB,YAAM,IAAI,MAAM,8BAA8B;AAAA,IAChD;AAEA,UAAM,UAAU,IAAI,YAAY;AAChC,UAAM,UAAU,QAAQ,OAAO,KAAK,IAAI,UAAU;AAElD,UAAM,MAAM,MAAM,OAAO,OAAO;AAAA,MAC9B;AAAA,MACA;AAAA,MACA,EAAE,MAAM,QAAQ,MAAM,UAAU;AAAA,MAChC;AAAA,MACA,CAAC,MAAM;AAAA,IACT;AAEA,UAAM,SAAS;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAEA,UAAM,gBAAgB,KAAK,gBAAgB,KAAK,UAAU,MAAM,CAAC;AACjE,UAAM,iBAAiB,KAAK,gBAAgB,KAAK,UAAU,OAAO,CAAC;AAEnE,UAAM,OAAO,QAAQ,OAAO,GAAG,aAAa,IAAI,cAAc,EAAE;AAChE,UAAM,YAAY,MAAM,OAAO,OAAO,KAAK,QAAQ,KAAK,IAAI;AAC5D,UAAM,mBAAmB,KAAK,gBAAgB,SAAS;AAEvD,WAAO,GAAG,aAAa,IAAI,cAAc,IAAI,gBAAgB;AAAA,EAC/D;AAAA,EAEA,MAAM,UAAU,OAA2C;AACzD,QAAI;AACF,YAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,UAAI,MAAM,WAAW,GAAG;AACtB,eAAO;AAAA,MACT;AAEA,YAAM,CAAC,eAAe,gBAAgB,gBAAgB,IAAI;AAG1D,UAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,kBAAkB;AAC1D,eAAO;AAAA,MACT;AAGA,UAAI,CAAC,KAAK,IAAI,YAAY;AACxB,cAAM,IAAI,MAAM,8BAA8B;AAAA,MAChD;AAEA,YAAM,UAAU,IAAI,YAAY;AAChC,YAAM,UAAU,QAAQ,OAAO,KAAK,IAAI,UAAU;AAElD,YAAM,MAAM,MAAM,OAAO,OAAO;AAAA,QAC9B;AAAA,QACA;AAAA,QACA,EAAE,MAAM,QAAQ,MAAM,UAAU;AAAA,QAChC;AAAA,QACA,CAAC,QAAQ;AAAA,MACX;AAEA,YAAM,OAAO,QAAQ,OAAO,GAAG,aAAa,IAAI,cAAc,EAAE;AAChE,YAAM,YAAY,KAAK,gBAAgB,gBAAgB;AAEvD,YAAM,UAAU,MAAM,OAAO,OAAO,OAAO,QAAQ,KAAK,WAAW,IAAI;AACvE,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,MACT;AAGA,YAAM,UAAsB,KAAK,MAAM,KAAK,sBAAsB,cAAc,CAAC;AAGjF,UAAI,QAAQ,MAAM,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI,GAAG;AAC/C,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT,SAAS,OAAO;AACd,cAAQ,MAAM,2BAA2B,KAAK;AAC9C,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,MAAM,cAAc,cAAiD;AAEnE,UAAM,UAAU,MAAM,KAAK,UAAU,YAAY;AACjD,QAAI,CAAC,WAAW,QAAQ,SAAS,WAAW;AAC1C,aAAO;AAAA,IACT;AAGA,UAAM,YAAY,MAAM,KAAK,UAAU,YAAY;AACnD,UAAM,cAAc,MAAM,KAAK,UAAU,gBAAgB,SAAS;AAClE,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT;AAGA,UAAM,OAAO,MAAM,KAAK,UAAU,YAAY,QAAQ,MAAM;AAC5D,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AAGA,UAAM,KAAK,UAAU,mBAAmB,SAAS;AAGjD,WAAO,MAAM,KAAK,kBAAkB,IAAI;AAAA,EAC1C;AAAA,EAEA,MAAc,kBAAkB,QAAgB,cAAqC;AACnF,UAAM,YAAY,MAAM,KAAK,UAAU,YAAY;AACnD,UAAM,YAAY,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,wBAAwB,GAAI,EAAE,YAAY;AAEvF,UAAM,KAAK,UAAU,kBAAkB,QAAQ,WAAW,SAAS;AAAA,EACrE;AAAA,EAEA,MAAM,mBAAmB,cAAqC;AAC5D,UAAM,YAAY,MAAM,KAAK,UAAU,YAAY;AACnD,UAAM,KAAK,UAAU,mBAAmB,SAAS;AAAA,EACnD;AAAA,EAEA,MAAc,UAAU,OAAgC;AACtD,UAAM,UAAU,IAAI,YAAY;AAChC,UAAM,OAAO,QAAQ,OAAO,KAAK;AACjC,UAAM,aAAa,MAAM,OAAO,OAAO,OAAO,WAAW,IAAI;AAC7D,UAAM,YAAY,MAAM,KAAK,IAAI,WAAW,UAAU,CAAC;AACvD,WAAO,UAAU,IAAI,OAAK,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE;AAAA,EACpE;AAAA,EAEQ,gBAAgB,MAAoC;AAC1D,QAAI;AAEJ,QAAI,OAAO,SAAS,UAAU;AAC5B,eAAS,KAAK,IAAI;AAAA,IACpB,OAAO;AACL,YAAM,QAAQ,IAAI,WAAW,IAAI;AACjC,YAAM,SAAS,MAAM,KAAK,OAAO,UAAQ,OAAO,aAAa,IAAI,CAAC,EAAE,KAAK,EAAE;AAC3E,eAAS,KAAK,MAAM;AAAA,IACtB;AAEA,WAAO,OAAO,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,MAAM,EAAE;AAAA,EACxE;AAAA,EAEQ,gBAAgB,MAA2B;AACjD,UAAM,SAAS,KAAK,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AACxD,UAAM,SAAS,OAAO,OAAO,OAAO,UAAU,IAAI,OAAO,SAAS,KAAK,GAAG,GAAG;AAC7E,UAAM,SAAS,KAAK,MAAM;AAC1B,UAAM,QAAQ,IAAI,WAAW,OAAO,MAAM;AAC1C,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,CAAC,IAAI,OAAO,WAAW,CAAC;AAAA,IAChC;AACA,WAAO,MAAM;AAAA,EACf;AAAA,EAEQ,sBAAsB,MAAsB;AAClD,UAAM,SAAS,KAAK,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AACxD,UAAM,SAAS,OAAO,OAAO,OAAO,UAAU,IAAI,OAAO,SAAS,KAAK,GAAG,GAAG;AAC7E,WAAO,KAAK,MAAM;AAAA,EACpB;AACF;;;ACrMO,IAAM,cAAN,MAAkB;AAAA,EAGvB,YACU,KACA,WACR;AAFQ;AACA;AAER,SAAK,aAAa,IAAI,WAAW,KAAK,SAAS;AAAA,EACjD;AAAA,EApBF,OAYyB;AAAA;AAAA;AAAA,EACf;AAAA,EASR,MAAM,SAAS,MAAmE;AAEhF,SAAK,qBAAqB,IAAI;AAG9B,UAAM,eAAe,MAAM,KAAK,UAAU,eAAe,KAAK,KAAK;AACnE,QAAI,cAAc;AAChB,YAAM,IAAI,gBAAgB,sCAAQ;AAAA,IACpC;AAGA,QAAI,KAAK,aAAa,KAAK,iBAAiB;AAC1C,YAAM,IAAI,gBAAgB,8DAAY;AAAA,IACxC;AAGA,UAAM,eAAe,MAAM,KAAK,aAAa,KAAK,QAAQ;AAC1D,UAAM,WAA2B;AAAA,MAC/B,OAAO,KAAK;AAAA,MACZ,eAAe;AAAA,MACf,OAAO;AAAA;AAAA,MACP,MAAM;AAAA,IACR;AAEA,UAAM,OAAO,MAAM,KAAK,UAAU,WAAW,QAAQ;AAGrD,UAAM,SAAS,MAAM,KAAK,WAAW,kBAAkB,IAAI;AAG3D,UAAM,KAAK,UAAU,UAAU;AAAA,MAC7B,QAAQ,KAAK;AAAA,MACb,QAAQ;AAAA,MACR,SAAS,oBAAoB,KAAK,KAAK;AAAA,IACzC,CAAC;AAGD,UAAM,EAAE,eAAe,GAAG,oBAAoB,IAAI;AAClD,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,MAAM,MAAgE;AAE1E,SAAK,kBAAkB,IAAI;AAG3B,UAAM,OAAO,MAAM,KAAK,UAAU,eAAe,KAAK,KAAK;AAC3D,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,oBAAoB,4CAAS;AAAA,IACzC;AAGA,UAAM,kBAAkB,MAAM,KAAK,eAAe,KAAK,UAAU,KAAK,aAAa;AACnF,QAAI,CAAC,iBAAiB;AACpB,YAAM,IAAI,oBAAoB,4CAAS;AAAA,IACzC;AAGA,QAAI,CAAC,KAAK,WAAW;AACnB,YAAM,IAAI,oBAAoB,sCAAQ;AAAA,IACxC;AAGA,UAAM,SAAS,MAAM,KAAK,WAAW,kBAAkB,IAAI;AAG3D,UAAM,KAAK,UAAU,UAAU;AAAA,MAC7B,QAAQ,KAAK;AAAA,MACb,QAAQ;AAAA,MACR,SAAS,mBAAmB,KAAK,KAAK;AAAA,IACxC,CAAC;AAGD,UAAM,EAAE,eAAe,GAAG,oBAAoB,IAAI;AAClD,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,cAAc,cAA0C;AAC5D,UAAM,SAAS,MAAM,KAAK,WAAW,cAAc,YAAY;AAC/D,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,oBAAoB,4CAAS;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,OAAO,cAAqC;AAChD,UAAM,KAAK,WAAW,mBAAmB,YAAY;AAAA,EACvD;AAAA,EAEA,MAAM,eAAe,QAA+B;AAClD,UAAM,OAAO,MAAM,KAAK,UAAU,YAAY,MAAM;AACpD,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,oBAAoB,gCAAO;AAAA,IACvC;AAEA,UAAM,EAAE,eAAe,GAAG,oBAAoB,IAAI;AAClD,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,eACJ,QACA,iBACA,aACe;AAEf,UAAM,OAAO,MAAM,KAAK,UAAU,YAAY,MAAM;AACpD,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,oBAAoB,gCAAO;AAAA,IACvC;AAGA,UAAM,yBAAyB,MAAM,KAAK,eAAe,iBAAiB,KAAK,aAAa;AAC5F,QAAI,CAAC,wBAAwB;AAC3B,YAAM,IAAI,oBAAoB,sCAAQ;AAAA,IACxC;AAGA,SAAK,iBAAiB,WAAW;AAGjC,UAAM,kBAAkB,MAAM,KAAK,aAAa,WAAW;AAC3D,UAAM,KAAK,UAAU,mBAAmB,QAAQ,eAAe;AAG/D,UAAM,KAAK,UAAU,UAAU;AAAA,MAC7B;AAAA,MACA,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAAA,EAEQ,qBAAqB,MAA6B;AACxD,QAAI,CAAC,KAAK,aAAa,KAAK,KAAK,GAAG;AAClC,YAAM,IAAI,gBAAgB,4CAAS;AAAA,IACrC;AAEA,SAAK,iBAAiB,KAAK,QAAQ;AAEnC,QAAI,CAAC,KAAK,gBAAgB;AACxB,YAAM,IAAI,gBAAgB,4CAAS;AAAA,IACrC;AAAA,EACF;AAAA,EAEQ,kBAAkB,MAA0B;AAClD,QAAI,CAAC,KAAK,SAAS,CAAC,KAAK,UAAU;AACjC,YAAM,IAAI,gBAAgB,wDAAW;AAAA,IACvC;AAEA,QAAI,CAAC,KAAK,gBAAgB;AACxB,YAAM,IAAI,gBAAgB,4CAAS;AAAA,IACrC;AAAA,EACF;AAAA,EAEQ,iBAAiB,UAAwB;AAC/C,QAAI,CAAC,YAAY,SAAS,SAAS,GAAG;AACpC,YAAM,IAAI,gBAAgB,6CAAU;AAAA,IACtC;AAEA,QAAI,SAAS,SAAS,KAAK;AACzB,YAAM,IAAI,gBAAgB,2DAAc;AAAA,IAC1C;AAAA,EAMF;AAAA,EAEQ,aAAaA,QAAwB;AAC3C,UAAM,aAAa;AACnB,WAAO,WAAW,KAAKA,MAAK,KAAKA,OAAM,UAAU;AAAA,EACnD;AAAA,EAEA,MAAc,aAAa,UAAmC;AAE5D,UAAM,UAAU,IAAI,YAAY;AAChC,UAAM,OAAO,OAAO,gBAAgB,IAAI,WAAW,EAAE,CAAC;AACtD,UAAM,eAAe,QAAQ,OAAO,QAAQ;AAG5C,UAAM,WAAW,IAAI,WAAW,aAAa,SAAS,KAAK,MAAM;AACjE,aAAS,IAAI,YAAY;AACzB,aAAS,IAAI,MAAM,aAAa,MAAM;AAGtC,QAAI,OAAO,MAAM,OAAO,OAAO,OAAO,WAAW,QAAQ;AACzD,aAAS,IAAI,GAAG,IAAI,KAAO,KAAK;AAC9B,aAAO,MAAM,OAAO,OAAO,OAAO,WAAW,IAAI;AAAA,IACnD;AAGA,UAAM,SAAS,IAAI,WAAW,KAAK,SAAS,KAAK,UAAU;AAC3D,WAAO,IAAI,IAAI;AACf,WAAO,IAAI,IAAI,WAAW,IAAI,GAAG,KAAK,MAAM;AAE5C,WAAO,KAAK,OAAO,aAAa,GAAG,MAAM,CAAC;AAAA,EAC5C;AAAA,EAEA,MAAc,eAAe,UAAkB,gBAA0C;AACvF,QAAI;AACF,YAAM,UAAU,IAAI,YAAY;AAChC,YAAM,eAAe,QAAQ,OAAO,QAAQ;AAG5C,YAAM,SAAS,WAAW,KAAK,KAAK,cAAc,GAAG,OAAK,EAAE,WAAW,CAAC,CAAC;AACzE,YAAM,OAAO,OAAO,MAAM,GAAG,EAAE;AAC/B,YAAM,aAAa,OAAO,MAAM,EAAE;AAGlC,YAAM,WAAW,IAAI,WAAW,aAAa,SAAS,KAAK,MAAM;AACjE,eAAS,IAAI,YAAY;AACzB,eAAS,IAAI,MAAM,aAAa,MAAM;AAEtC,UAAI,OAAO,MAAM,OAAO,OAAO,OAAO,WAAW,QAAQ;AACzD,eAAS,IAAI,GAAG,IAAI,KAAO,KAAK;AAC9B,eAAO,MAAM,OAAO,OAAO,OAAO,WAAW,IAAI;AAAA,MACnD;AAGA,YAAM,eAAe,IAAI,WAAW,IAAI;AACxC,UAAI,aAAa,WAAW,WAAW,QAAQ;AAC7C,eAAO;AAAA,MACT;AAEA,eAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,YAAI,aAAa,CAAC,MAAM,WAAW,CAAC,GAAG;AACrC,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT,SAAS,OAAO;AACd,cAAQ,MAAM,gCAAgC,KAAK;AACnD,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;ACzPO,IAAM,kBAAN,MAAsB;AAAA,EAC3B,YAAoB,IAAgB;AAAhB;AAAA,EAAiB;AAAA,EAhBvC,OAe6B;AAAA;AAAA;AAAA;AAAA,EAI3B,MAAM,WAAW,UAAyC;AACxD,UAAM,SAAS,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA,KAIpC,EAAE;AAAA,MACD,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS,SAAS;AAAA,MAClB,SAAS,QAAQ;AAAA,IACnB,EAAE,MAAY;AAEd,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,eAAeC,QAAqC;AACxD,WAAO,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA,KAE5B,EAAE,KAAKA,MAAK,EAAE,MAAY;AAAA,EAC7B;AAAA,EAEA,MAAM,YAAY,IAAkC;AAClD,WAAO,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA,KAE5B,EAAE,KAAK,EAAE,EAAE,MAAY;AAAA,EAC1B;AAAA,EAEA,MAAM,gBAAgB,QAAgB,OAA8B;AAClE,UAAM,KAAK,GAAG,QAAQ;AAAA;AAAA,KAErB,EAAE,KAAK,OAAO,MAAM,EAAE,IAAI;AAAA,EAC7B;AAAA,EAEA,MAAM,mBAAmB,QAAkC;AACzD,UAAM,SAAS,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA;AAAA,KAGpC,EAAE,KAAK,MAAM,EAAE,IAAI;AAEpB,YAAQ,OAAO,MAAM,WAAW,KAAK;AAAA,EACvC;AAAA,EAEA,MAAM,mBAAmB,QAAgB,cAAwC;AAC/E,UAAM,SAAS,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA,KAEpC,EAAE,KAAK,cAAc,MAAM,EAAE,IAAI;AAElC,YAAQ,OAAO,MAAM,WAAW,KAAK;AAAA,EACvC;AAAA;AAAA,EAGA,MAAM,gBAAgB,QAAgBA,QAAe,UAAsC;AACzF,UAAM,SAAS,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA,KAIpC,EAAE,KAAK,QAAQA,QAAO,QAAQ,EAAE,MAAiB;AAElD,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MAAM,6BAA6B;AAAA,IAC/C;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,sBAAsB,QAAsC;AAChE,UAAM,SAAS,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA,KAIpC,EAAE,KAAK,MAAM,EAAE,IAAe;AAE/B,WAAO,OAAO,WAAW,CAAC;AAAA,EAC5B;AAAA,EAEA,MAAM,oBAAoBA,QAA0C;AAClE,WAAO,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA,KAE5B,EAAE,KAAKA,MAAK,EAAE,MAAiB;AAAA,EAClC;AAAA,EAEA,MAAM,gBAAgB,IAAY,QAAkC;AAClE,UAAM,SAAS,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA,KAEpC,EAAE,KAAK,IAAI,MAAM,EAAE,IAAI;AAExB,YAAQ,OAAO,MAAM,WAAW,KAAK;AAAA,EACvC;AAAA;AAAA,EAGA,MAAM,YAAY,WAOC;AACjB,UAAM,SAAS,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA,KAIpC,EAAE;AAAA,MACD,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU,WAAW;AAAA,MACrB,UAAU,WAAW;AAAA,MACrB,UAAU,eAAe;AAAA,MACzB,UAAU,oBAAoB;AAAA,IAChC,EAAE,MAAa;AAEf,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC1C;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,sBACJ,aACA,YACmC;AAEnC,UAAM,cAAc,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA,KAEzC,EAAE,KAAK,WAAW,EAAE,MAAyB;AAE9C,UAAM,QAAQ,aAAa,SAAS;AAGpC,UAAM,SAAS,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,KAKpC,EAAE,KAAK,aAAa,WAAW,OAAO,WAAW,MAAM,EAAE,IAAW;AAErE,WAAO;AAAA,MACL,MAAM,OAAO,WAAW,CAAC;AAAA,MACzB;AAAA,MACA,MAAM,WAAW;AAAA,MACjB,OAAO,WAAW;AAAA,MAClB,YAAY,KAAK,KAAK,QAAQ,WAAW,KAAK;AAAA,IAChD;AAAA,EACF;AAAA,EAEA,MAAM,YAAY,IAA8B;AAC9C,UAAM,SAAS,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA,KAEpC,EAAE,KAAK,EAAE,EAAE,IAAI;AAEhB,YAAQ,OAAO,MAAM,WAAW,KAAK;AAAA,EACvC;AAAA;AAAA,EAGA,MAAM,mBAAsC;AAC1C,UAAM,SAAS,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA,KAEpC,EAAE,KAAK,EAAE,IAAY;AAEtB,WAAO,OAAO,WAAW,CAAC;AAAA,EAC5B;AAAA,EAEA,MAAM,cAAc,IAAoC;AACtD,WAAO,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA,KAE5B,EAAE,KAAK,EAAE,EAAE,MAAc;AAAA,EAC5B;AAAA;AAAA,EAGA,MAAM,cAAc,MAA0C;AAC5D,WAAO,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA,KAE5B,EAAE,KAAK,IAAI,EAAE,MAAkB;AAAA,EAClC;AAAA,EAEA,MAAM,cAAc,MAAc,QAAkC;AAClE,UAAM,SAAS,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA,KAIpC,EAAE,KAAK,QAAQ,IAAI,EAAE,IAAI;AAE1B,YAAQ,OAAO,MAAM,WAAW,KAAK;AAAA,EACvC;AAAA;AAAA,EAGA,MAAM,kBAAkB,QAAgB,WAAmB,WAAkC;AAC3F,UAAM,KAAK,GAAG,QAAQ;AAAA;AAAA;AAAA,KAGrB,EAAE,KAAK,QAAQ,WAAW,SAAS,EAAE,IAAI;AAAA,EAC5C;AAAA,EAEA,MAAM,gBAAgB,WAAiD;AACrE,WAAO,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA;AAAA,KAG5B,EAAE,KAAK,SAAS,EAAE,MAAoB;AAAA,EACzC;AAAA,EAEA,MAAM,mBAAmB,WAAkC;AACzD,UAAM,KAAK,GAAG,QAAQ;AAAA;AAAA,KAErB,EAAE,KAAK,SAAS,EAAE,IAAI;AAAA,EACzB;AAAA;AAAA,EAGA,MAAM,UAAU,SAME;AAChB,UAAM,KAAK,GAAG,QAAQ;AAAA;AAAA;AAAA,KAGrB,EAAE;AAAA,MACD,QAAQ,UAAU;AAAA,MAClB,QAAQ;AAAA,MACR,QAAQ,aAAa;AAAA,MACrB,QAAQ,aAAa;AAAA,MACrB,QAAQ,WAAW;AAAA,IACrB,EAAE,IAAI;AAAA,EACR;AAAA;AAAA,EAGA,MAAM,aAAa,YAAoB,UAA6C;AAClF,WAAO,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA,KAI5B,EAAE,KAAK,YAAY,QAAQ,EAAE,MAAiB;AAAA,EACjD;AAAA,EAEA,MAAM,wBAAwB,YAAoB,UAAmC;AAEnF,UAAM,eAAe,MAAM,KAAK,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,KAK1C,EAAE,KAAK,YAAY,QAAQ,EAAE,IAAI;AAElC,SAAK,aAAa,MAAM,WAAW,KAAK,GAAG;AAEzC,YAAM,SAAS,MAAM,KAAK,aAAa,YAAY,QAAQ;AAC3D,aAAO,QAAQ,iBAAiB;AAAA,IAClC,OAAO;AAEL,YAAM,KAAK,GAAG,QAAQ;AAAA;AAAA;AAAA,OAGrB,EAAE,KAAK,YAAY,QAAQ,EAAE,IAAI;AAClC,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;ACvRO,IAAM,mBAAN,MAAuB;AAAA,EAC5B,YAAoB,KAAU;AAAV;AAAA,EAAW;AAAA,EAHjC,OAE8B;AAAA;AAAA;AAAA,EAG5B,MAAM,YAAY,OAAe,UAAqC;AAEpE,QAAI,KAAK,IAAI,gBAAgB,eAAe;AAC1C,aAAO;AAAA,IACT;AAEA,QAAI;AACF,YAAM,WAAW,MAAM,MAAM,6DAA6D;AAAA,QACxF,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,QAAQ,KAAK,IAAI;AAAA,UACjB,UAAU;AAAA,UACV,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,gBAAQ,MAAM,wBAAwB,SAAS,QAAQ,SAAS,UAAU;AAC1E,eAAO;AAAA,MACT;AAEA,YAAM,SAA4B,MAAM,SAAS,KAAK;AAEtD,UAAI,CAAC,OAAO,SAAS;AACnB,gBAAQ,MAAM,kCAAkC,OAAO,aAAa,CAAC;AACrE,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT,SAAS,OAAO;AACd,cAAQ,MAAM,iCAAiC,KAAK;AACpD,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;AClCO,SAAS,qBAAqB,KAAU;AAC7C,QAAM,YAAY,IAAI,gBAAgB,IAAI,EAAE;AAC5C,QAAM,aAAa,IAAI,WAAW,KAAK,SAAS;AAEhD,SAAO;AAAA;AAAA,IAEL,MAAM,aAAa,SAAgF;AACjG,YAAM,aAAa,QAAQ,QAAQ,IAAI,eAAe;AAEtD,UAAI,CAAC,cAAc,CAAC,WAAW,WAAW,SAAS,GAAG;AACpD,cAAM,IAAI,SAAS,KAAK,UAAU;AAAA,UAChC,SAAS;AAAA,UACT,OAAO;AAAA,QACT,CAAC,GAAG;AAAA,UACF,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD,CAAC;AAAA,MACH;AAEA,YAAM,QAAQ,WAAW,UAAU,CAAC;AACpC,YAAM,UAAU,MAAM,WAAW,UAAU,KAAK;AAEhD,UAAI,CAAC,WAAW,QAAQ,SAAS,UAAU;AACzC,cAAM,IAAI,SAAS,KAAK,UAAU;AAAA,UAChC,SAAS;AAAA,UACT,OAAO;AAAA,QACT,CAAC,GAAG;AAAA,UACF,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD,CAAC;AAAA,MACH;AAGA,YAAM,OAAO,MAAM,UAAU,YAAY,QAAQ,MAAM;AACvD,UAAI,CAAC,QAAQ,CAAC,KAAK,WAAW;AAC5B,cAAM,IAAI,SAAS,KAAK,UAAU;AAAA,UAChC,SAAS;AAAA,UACT,OAAO;AAAA,QACT,CAAC,GAAG;AAAA,UACF,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD,CAAC;AAAA,MACH;AAGA,YAAM,uBAAuB;AAC7B,2BAAqB,OAAO;AAE5B,aAAO,EAAE,SAAS,sBAAsB,MAAM,QAAQ;AAAA,IACxD;AAAA;AAAA,IAGA,MAAM,aAAa,SAAgF;AACjG,YAAM,EAAE,SAAS,aAAa,KAAK,IAAI,MAAM,KAAK,aAAa,OAAO;AAEtE,UAAI,KAAK,SAAS,SAAS;AACzB,cAAM,IAAI,SAAS,KAAK,UAAU;AAAA,UAChC,SAAS;AAAA,UACT,OAAO;AAAA,QACT,CAAC,GAAG;AAAA,UACF,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD,CAAC;AAAA,MACH;AAEA,aAAO,EAAE,SAAS,aAAa,KAAK;AAAA,IACtC;AAAA;AAAA,IAGA,MAAM,aAAa,SAAiF;AAClG,UAAI;AACF,cAAM,EAAE,SAAS,aAAa,KAAK,IAAI,MAAM,KAAK,aAAa,OAAO;AACtE,eAAO,EAAE,SAAS,aAAa,KAAK;AAAA,MACtC,SAAS,OAAO;AAEd,cAAM,uBAAuB;AAC7B,eAAO,EAAE,SAAS,sBAAsB,MAAM,OAAU;AAAA,MAC1D;AAAA,IACF;AAAA,EACF;AACF;AAhFgB;AAmFT,SAAS,SAAS,KAAU;AACjC,QAAM,iBAAiB,qBAAqB,GAAG;AAE/C,SAAO,gCAAS,cACd,SACA;AACA,WAAO,eAAe,SAAqC;AACzD,UAAI;AACF,cAAM,EAAE,SAAS,aAAa,KAAK,IAAI,MAAM,eAAe,aAAa,OAAO;AAChF,eAAO,MAAM,QAAQ,aAAa,MAAM,GAAG;AAAA,MAC7C,SAAS,OAAO;AACd,YAAI,iBAAiB,UAAU;AAC7B,iBAAO;AAAA,QACT;AAEA,gBAAQ,MAAM,0BAA0B,KAAK;AAC7C,eAAO,IAAI,SAAS,KAAK,UAAU;AAAA,UACjC,SAAS;AAAA,UACT,OAAO;AAAA,QACT,CAAC,GAAG;AAAA,UACF,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAtBO;AAuBT;AA1BgB;;;ACpFT,IAAM,cAAN,MAAkB;AAAA,EAMvB,YAAoB,KAAU;AAAV;AAClB,UAAM,YAAY,IAAI,gBAAgB,IAAI,EAAE;AAC5C,SAAK,cAAc,IAAI,YAAY,KAAK,SAAS;AACjD,SAAK,mBAAmB,IAAI,iBAAiB,GAAG;AAGhD,SAAK,iBAAiB,SAAS,KAAK,GAAG,EAAE,CAAC,SAA+B,SAAqB;AAC5F,aAAO,KAAK,qBAAqB,SAAS,IAAI;AAAA,IAChD,CAAC;AAED,SAAK,iBAAiB,SAAS,KAAK,GAAG,EAAE,CAAC,SAA+B,SAAqB;AAC5F,aAAO,KAAK,qBAAqB,SAAS,IAAI;AAAA,IAChD,CAAC;AAAA,EACH;AAAA,EA1BF,OAOyB;AAAA;AAAA;AAAA,EACf;AAAA,EACA;AAAA,EACD;AAAA,EACA;AAAA,EAiBP,MAAM,SAAS,SAAqC;AAClD,QAAI;AACF,YAAM,OAAwB,MAAM,QAAQ,KAAK;AAGjD,YAAM,WAAW,QAAQ,QAAQ,IAAI,kBAAkB,KACvC,QAAQ,QAAQ,IAAI,iBAAiB;AAErD,YAAM,mBAAmB,MAAM,KAAK,iBAAiB;AAAA,QACnD,KAAK;AAAA,QACL,YAAY;AAAA,MACd;AAEA,UAAI,CAAC,kBAAkB;AACrB,eAAO,KAAK,cAAc,wCAAU,GAAG;AAAA,MACzC;AAGA,YAAM,SAAS,MAAM,KAAK,YAAY,SAAS,IAAI;AAEnD,aAAO,KAAK,gBAAgB,QAAQ,0BAAM;AAAA,IAC5C,SAAS,OAAY;AACnB,cAAQ,MAAM,mBAAmB,KAAK;AACtC,aAAO,KAAK,cAAc,MAAM,WAAW,4BAAQ,MAAM,cAAc,GAAG;AAAA,IAC5E;AAAA,EACF;AAAA,EAEA,MAAM,MAAM,SAAqC;AAC/C,QAAI;AACF,YAAM,OAAqB,MAAM,QAAQ,KAAK;AAG9C,YAAM,WAAW,QAAQ,QAAQ,IAAI,kBAAkB,KACvC,QAAQ,QAAQ,IAAI,iBAAiB;AAErD,YAAM,mBAAmB,MAAM,KAAK,iBAAiB;AAAA,QACnD,KAAK;AAAA,QACL,YAAY;AAAA,MACd;AAEA,UAAI,CAAC,kBAAkB;AACrB,eAAO,KAAK,cAAc,wCAAU,GAAG;AAAA,MACzC;AAGA,YAAM,SAAS,MAAM,KAAK,YAAY,MAAM,IAAI;AAEhD,aAAO,KAAK,gBAAgB,QAAQ,0BAAM;AAAA,IAC5C,SAAS,OAAY;AACnB,cAAQ,MAAM,gBAAgB,KAAK;AACnC,aAAO,KAAK,cAAc,MAAM,WAAW,4BAAQ,MAAM,cAAc,GAAG;AAAA,IAC5E;AAAA,EACF;AAAA,EAEA,MAAM,aAAa,SAAqC;AACtD,QAAI;AACF,YAAM,EAAE,aAAa,IAAI,MAAM,QAAQ,KAAK;AAE5C,UAAI,CAAC,cAAc;AACjB,eAAO,KAAK,cAAc,wCAAU,GAAG;AAAA,MACzC;AAEA,YAAM,SAAS,MAAM,KAAK,YAAY,cAAc,YAAY;AAEhE,aAAO,KAAK,gBAAgB,QAAQ,sCAAQ;AAAA,IAC9C,SAAS,OAAY;AACnB,cAAQ,MAAM,wBAAwB,KAAK;AAC3C,aAAO,KAAK,cAAc,MAAM,WAAW,wCAAU,MAAM,cAAc,GAAG;AAAA,IAC9E;AAAA,EACF;AAAA,EAEA,MAAM,OAAO,SAAqC;AAChD,QAAI;AACF,YAAM,EAAE,aAAa,IAAI,MAAM,QAAQ,KAAK;AAE5C,UAAI,cAAc;AAChB,cAAM,KAAK,YAAY,OAAO,YAAY;AAAA,MAC5C;AAEA,aAAO,KAAK,gBAAgB,MAAM,0BAAM;AAAA,IAC1C,SAAS,OAAY;AACnB,cAAQ,MAAM,iBAAiB,KAAK;AACpC,aAAO,KAAK,cAAc,MAAM,WAAW,4BAAQ,MAAM,cAAc,GAAG;AAAA,IAC5E;AAAA,EACF;AAAA;AAAA,EAIA,MAAc,qBAAqB,SAA+B,MAAqC;AACrG,QAAI;AACF,YAAM,cAAc,MAAM,KAAK,YAAY,eAAe,KAAK,MAAM;AACrE,aAAO,KAAK,gBAAgB,WAAW;AAAA,IACzC,SAAS,OAAY;AACnB,cAAQ,MAAM,2BAA2B,KAAK;AAC9C,aAAO,KAAK,cAAc,MAAM,WAAW,oDAAY,MAAM,cAAc,GAAG;AAAA,IAChF;AAAA,EACF;AAAA,EAEA,MAAc,qBAAqB,SAA+B,MAAqC;AACrG,QAAI;AACF,YAAM,EAAE,iBAAiB,aAAa,gBAAgB,IAAI,MAAM,QAAQ,KAAK;AAE7E,UAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,iBAAiB;AACxD,eAAO,KAAK,cAAc,wCAAU,GAAG;AAAA,MACzC;AAEA,UAAI,gBAAgB,iBAAiB;AACnC,eAAO,KAAK,cAAc,oDAAY,GAAG;AAAA,MAC3C;AAEA,YAAM,KAAK,YAAY,eAAe,KAAK,QAAQ,iBAAiB,WAAW;AAE/E,aAAO,KAAK,gBAAgB,MAAM,sCAAQ;AAAA,IAC5C,SAAS,OAAY;AACnB,cAAQ,MAAM,0BAA0B,KAAK;AAC7C,aAAO,KAAK,cAAc,MAAM,WAAW,wCAAU,MAAM,cAAc,GAAG;AAAA,IAC9E;AAAA,EACF;AAAA,EAEQ,gBAAmB,MAAS,SAA4B;AAC9D,UAAM,WAA2B;AAAA,MAC/B,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAEA,WAAO,IAAI,SAAS,KAAK,UAAU,QAAQ,GAAG;AAAA,MAC5C,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,+BAA+B;AAAA,QAC/B,gCAAgC;AAAA,QAChC,gCAAgC;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEQ,cAAc,OAAe,SAAiB,KAAe;AACnE,UAAM,WAAwB;AAAA,MAC5B,SAAS;AAAA,MACT;AAAA,IACF;AAEA,WAAO,IAAI,SAAS,KAAK,UAAU,QAAQ,GAAG;AAAA,MAC5C;AAAA,MACA,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,+BAA+B;AAAA,QAC/B,gCAAgC;AAAA,QAChC,gCAAgC;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;ACrLO,IAAM,cAAc,IAAI,YAAY;AAE3C,IAAM,cAAc;AAGpB,IAAM,eAAe,IAAI,WAAW,GAAG;AACvC,KAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACzC,eAAa,YAAY,WAAW,CAAC,CAAC,IAAI;AAC9C;AAFS;AAIF,SAAS,aAAa,QAAQ;AACjC,MAAI,eAAe,KAAK,KAAK,OAAO,SAAS,CAAC,IAAI;AAClD,QAAM,MAAM,OAAO;AAEnB,MAAI,IAAI;AAER,MAAI,OAAO,SAAS,MAAM,GAAG;AACzB;AAAA,EACJ,WAAW,OAAO,SAAS,MAAM,GAAG;AAChC,oBAAgB;AAAA,EACpB,WAAW,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK;AAC1C;AACA,QAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK;AACnC;AAAA,IACJ;AAAA,EACJ;AAEA,QAAM,cAAc,IAAI,YAAY,YAAY;AAChD,QAAM,QAAQ,IAAI,WAAW,WAAW;AAExC,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC7B,QAAI,WAAW,aAAa,OAAO,WAAW,CAAC,CAAC;AAChD,QAAI,WAAW,aAAa,OAAO,WAAW,IAAI,CAAC,CAAC;AACpD,QAAI,WAAW,aAAa,OAAO,WAAW,IAAI,CAAC,CAAC;AACpD,QAAI,WAAW,aAAa,OAAO,WAAW,IAAI,CAAC,CAAC;AAEpD,UAAM,GAAG,IAAK,YAAY,IAAM,YAAY;AAC5C,UAAM,GAAG,KAAM,WAAW,OAAO,IAAM,YAAY;AACnD,UAAM,GAAG,KAAM,WAAW,MAAM,IAAM,WAAW;AAAA,EACrD;AAEA,SAAO;AACX;AAhCgB;AAkCT,SAAS,WAAW,SAAS;AAChC,YAAU,WAAW;AACrB,MAAI;AAEJ,MAAI;AACA,cAAU,IAAI,YAAY,OAAO;AAAA,EACrC,SAAS,KAAK;AACV,cAAU,IAAI,YAAY,cAAc;AAAA,EAC5C;AAEA,SAAO;AACX;AAXgB;AAkBhB,eAAsB,kBAAkB,MAAM;AAC1C,MAAI,iBAAiB,MAAM;AACvB,WAAO,MAAM,KAAK,YAAY;AAAA,EAClC;AAEA,QAAM,KAAK,IAAI,WAAW;AAE1B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,OAAG,SAAS,SAAU,GAAG;AACrB,cAAQ,EAAE,OAAO,MAAM;AAAA,IAC3B;AAEA,OAAG,UAAU,SAAU,GAAG;AACtB,aAAO,GAAG,KAAK;AAAA,IACnB;AAEA,OAAG,kBAAkB,IAAI;AAAA,EAC7B,CAAC;AACL;AAlBsB;AAoBf,SAAS,OAAO,GAAG;AACtB,MAAK,KAAK,MAAgB,KAAK,MAAkB,KAAK,MAAgB,KAAK,OAAkB,KAAK,MAAgB,KAAK,IAAe;AAClI,WAAO,OAAO,aAAa,CAAC;AAAA,EAChC;AACA,SAAO;AACX;AALgB;AAaT,SAAS,WAAW,SAAS,UAAU,KAAK;AAI/C,MAAI,WAAW,QAAQ,QAAQ,GAAG;AAClC,MAAI,YAAY,GAAG;AACf,cAAU,QAAQ,OAAO,GAAG,QAAQ;AAAA,EACxC;AAEA,aAAW,SAAS,YAAY;AAEhC,MAAI;AAEJ,MAAI,aAAa,KAAK;AAClB,UAAM,IAED,QAAQ,sBAAsB,KAAK,EAEnC,QAAQ,UAAU,GAAG;AAE1B,QAAI,MAAM,YAAY,OAAO,GAAG;AAChC,QAAI,eAAe,CAAC;AACpB,aAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC5C,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,KAAK,MAAM,KAAK,MAAM,IAAc;AACpC,YAAI,KAAK,OAAO,IAAI,IAAI,CAAC,CAAC;AAC1B,YAAI,KAAK,OAAO,IAAI,IAAI,CAAC,CAAC;AAC1B,YAAI,MAAM,IAAI;AACV,cAAIC,KAAI,SAAS,KAAK,IAAI,EAAE;AAC5B,uBAAa,KAAKA,EAAC;AACnB,eAAK;AACL;AAAA,QACJ;AAAA,MACJ;AACA,mBAAa,KAAK,CAAC;AAAA,IACvB;AACA,cAAU,IAAI,YAAY,aAAa,MAAM;AAC7C,QAAI,WAAW,IAAI,SAAS,OAAO;AACnC,aAAS,IAAI,GAAG,MAAM,aAAa,QAAQ,IAAI,KAAK,KAAK;AACrD,eAAS,SAAS,GAAG,aAAa,CAAC,CAAC;AAAA,IACxC;AAAA,EACJ,WAAW,aAAa,KAAK;AACzB,cAAU,aAAa,IAAI,QAAQ,uBAAuB,EAAE,CAAC;AAAA,EACjE,OAAO;AAEH,cAAU,YAAY,OAAO,GAAG;AAAA,EACpC;AAEA,SAAO,WAAW,OAAO,EAAE,OAAO,OAAO;AAC7C;AAjDgB;AAmDT,SAAS,YAAY,KAAK;AAC7B,MAAI,aAAa;AACjB,MAAI,OAAO;AAEX,SAAO,CAAC,MAAM;AACV,QAAI,UAAU,OAAO,IAChB,SAAS,EAET,QAAQ,oEAAoE,CAAC,OAAO,MAAM,QAAQ,gBAAgB,YAAY;AAC3H,UAAI,CAAC,YAAY;AACb,eAAO;AAAA,MACX;AAEA,UAAI,WAAW,WAAW,eAAe,SAAS,MAAM,KAAK,CAAC,KAAK,KAAK,cAAc,GAAG;AAErF,eAAO,OAAO;AAAA,MAClB;AAEA,aAAO;AAAA,IACX,CAAC,EAEA,QAAQ,kEAAkE,CAAC,OAAO,MAAM,QAAQ,YAAY;AACzG,UAAI,CAAC,YAAY;AACb,eAAO;AAAA,MACX;AAEA,UAAI,WAAW,SAAS;AAEpB,eAAO,OAAO;AAAA,MAClB;AACA,aAAO;AAAA,IACX,CAAC,EAEA,QAAQ,kDAAkD,EAAE,EAE5D,QAAQ,kEAAkE,IAAI,EAE9E,QAAQ,yCAAyC,CAAC,GAAG,SAAS,UAAU,SAAS,WAAW,SAAS,UAAU,IAAI,CAAC;AAEzH,QAAI,cAAc,OAAO,QAAQ,QAAQ,KAAK,GAAG;AAE7C,mBAAa;AAAA,IACjB,OAAO;AACH,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AA9CgB;AAgDT,SAAS,8BAA8B,YAAY,SAAS;AAC/D,YAAU,WAAW;AAErB,MAAI,eAAe,CAAC;AACpB,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,QAAI,IAAI,WAAW,OAAO,CAAC;AAC3B,QAAI,MAAM,OAAO,gBAAgB,KAAK,WAAW,OAAO,IAAI,GAAG,CAAC,CAAC,GAAG;AAEhE,UAAI,OAAO,WAAW,OAAO,IAAI,GAAG,CAAC;AACrC,WAAK;AACL,mBAAa,KAAK,SAAS,MAAM,EAAE,CAAC;AAAA,IACxC,WAAW,EAAE,WAAW,CAAC,IAAI,KAAK;AAC9B,UAAI,YAAY,OAAO,CAAC;AACxB,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC/B,qBAAa,KAAK,EAAE,CAAC,CAAC;AAAA,MAC1B;AAAA,IACJ,OAAO;AAEH,mBAAa,KAAK,EAAE,WAAW,CAAC,CAAC;AAAA,IACrC;AAAA,EACJ;AAEA,QAAM,UAAU,IAAI,YAAY,aAAa,MAAM;AACnD,QAAM,WAAW,IAAI,SAAS,OAAO;AACrC,WAAS,IAAI,GAAG,MAAM,aAAa,QAAQ,IAAI,KAAK,KAAK;AACrD,aAAS,SAAS,GAAG,aAAa,CAAC,CAAC;AAAA,EACxC;AAEA,SAAO,WAAW,OAAO,EAAE,OAAO,OAAO;AAC7C;AA7BgB;AA+BT,SAAS,kCAAkC,QAAQ;AAKtD,MAAI,YAAY,oBAAI,IAAI;AAExB,SAAO,KAAK,OAAO,MAAM,EAAE,QAAQ,SAAO;AACtC,QAAI,QAAQ,IAAI,MAAM,gBAAgB;AACtC,QAAI,CAAC,OAAO;AAER;AAAA,IACJ;AAEA,QAAI,YAAY,IAAI,OAAO,GAAG,MAAM,KAAK,EAAE,YAAY;AACvD,QAAI,KAAK,OAAO,MAAM,CAAC,CAAC,KAAK;AAE7B,QAAI;AACJ,QAAI,CAAC,UAAU,IAAI,SAAS,GAAG;AAC3B,iBAAW;AAAA,QACP,SAAS;AAAA,QACT,QAAQ,CAAC;AAAA,MACb;AACA,gBAAU,IAAI,WAAW,QAAQ;AAAA,IACrC,OAAO;AACH,iBAAW,UAAU,IAAI,SAAS;AAAA,IACtC;AAEA,QAAI,QAAQ,OAAO,OAAO,GAAG;AAC7B,QAAI,OAAO,KAAK,MAAM,CAAC,EAAE,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,MAAM,QAAQ,QAAQ,MAAM,MAAM,sBAAsB,IAAI;AAC3G,eAAS,UAAU,MAAM,CAAC,KAAK;AAC/B,cAAQ,MAAM,CAAC;AAAA,IACnB;AAEA,aAAS,OAAO,KAAK,EAAE,IAAI,MAAM,CAAC;AAGlC,WAAO,OAAO,OAAO,GAAG;AAAA,EAC5B,CAAC;AAED,YAAU,QAAQ,CAAC,UAAU,QAAQ;AACjC,WAAO,OAAO,GAAG,IAAI;AAAA,MACjB,SAAS,OACJ,KAAK,CAAC,GAAG,MAAM,EAAE,KAAK,EAAE,EAAE,EAC1B,IAAI,OAAK,EAAE,KAAK,EAChB,KAAK,EAAE;AAAA,MACZ,SAAS;AAAA,IACb;AAAA,EACJ,CAAC;AACL;AAjDgB;;;AC/NhB,IAAqB,qBAArB,MAAwC;AAAA,EAFxC,OAEwC;AAAA;AAAA;AAAA,EACpC,cAAc;AACV,SAAK,SAAS,CAAC;AAAA,EACnB;AAAA,EAEA,OAAO,MAAM;AACT,SAAK,OAAO,KAAK,IAAI;AACrB,SAAK,OAAO,KAAK,IAAI;AAAA,EACzB;AAAA,EAEA,WAAW;AAEP,WAAO,kBAAkB,IAAI,KAAK,KAAK,QAAQ,EAAE,MAAM,2BAA2B,CAAC,CAAC;AAAA,EACxF;AACJ;;;ACdA,IAAqB,gBAArB,MAAmC;AAAA,EAFnC,OAEmC;AAAA;AAAA;AAAA,EAC/B,YAAY,MAAM;AACd,WAAO,QAAQ,CAAC;AAEhB,SAAK,UAAU,KAAK,WAAW,IAAI,YAAY;AAE/C,SAAK,eAAe,MAAM;AAE1B,SAAK,SAAS,CAAC;AAEf,SAAK,YAAY;AAAA,EACrB;AAAA,EAEA,OAAO,QAAQ;AACX,QAAI,MAAM,KAAK,QAAQ,OAAO,MAAM;AAEpC,QAAI,kBAAkB,KAAK,GAAG,GAAG;AAC7B,YAAM,IAAI,QAAQ,qBAAqB,EAAE;AAAA,IAC7C;AAEA,SAAK,aAAa;AAElB,QAAI,KAAK,UAAU,UAAU,KAAK,cAAc;AAC5C,UAAI,eAAe,KAAK,MAAM,KAAK,UAAU,SAAS,CAAC,IAAI;AAC3D,UAAI;AAEJ,UAAI,iBAAiB,KAAK,UAAU,QAAQ;AACxC,oBAAY,KAAK;AACjB,aAAK,YAAY;AAAA,MACrB,OAAO;AACH,oBAAY,KAAK,UAAU,OAAO,GAAG,YAAY;AACjD,aAAK,YAAY,KAAK,UAAU,OAAO,YAAY;AAAA,MACvD;AAEA,UAAI,UAAU,QAAQ;AAClB,aAAK,OAAO,KAAK,aAAa,SAAS,CAAC;AAAA,MAC5C;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,WAAW;AACP,QAAI,KAAK,aAAa,CAAC,OAAO,KAAK,KAAK,SAAS,GAAG;AAChD,WAAK,OAAO,KAAK,aAAa,KAAK,SAAS,CAAC;AAAA,IACjD;AAEA,WAAO,kBAAkB,IAAI,KAAK,KAAK,QAAQ,EAAE,MAAM,2BAA2B,CAAC,CAAC;AAAA,EACxF;AACJ;;;AC/CA,IAAqB,YAArB,MAA+B;AAAA,EAF/B,OAE+B;AAAA;AAAA;AAAA,EAC3B,YAAY,MAAM;AACd,WAAO,QAAQ,CAAC;AAEhB,SAAK,UAAU,KAAK,WAAW,IAAI,YAAY;AAE/C,SAAK,eAAe,MAAM;AAE1B,SAAK,YAAY;AAEjB,SAAK,SAAS,CAAC;AAAA,EACnB;AAAA,EAEA,cAAc,cAAc;AACxB,QAAI,MAAM,IAAI,YAAY,aAAa,MAAM;AAC7C,QAAI,WAAW,IAAI,SAAS,GAAG;AAC/B,aAAS,IAAI,GAAG,MAAM,aAAa,QAAQ,IAAI,KAAK,KAAK;AACrD,eAAS,SAAS,GAAG,SAAS,aAAa,CAAC,GAAG,EAAE,CAAC;AAAA,IACtD;AACA,WAAO;AAAA,EACX;AAAA,EAEA,aAAa,KAAK;AAEd,UAAM,IAAI,QAAQ,WAAW,EAAE;AAE/B,QAAI,OAAO,IAAI,MAAM,OAAO;AAC5B,QAAI,eAAe,CAAC;AACpB,aAAS,QAAQ,MAAM;AACnB,UAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AACxB,YAAI,aAAa,QAAQ;AACrB,eAAK,OAAO,KAAK,KAAK,cAAc,YAAY,CAAC;AACjD,yBAAe,CAAC;AAAA,QACpB;AACA,aAAK,OAAO,KAAK,IAAI;AACrB;AAAA,MACJ;AAEA,UAAI,KAAK,WAAW,GAAG;AACnB,qBAAa,KAAK,KAAK,OAAO,CAAC,CAAC;AAChC;AAAA,MACJ;AAEA,UAAI,KAAK,SAAS,GAAG;AACjB,qBAAa,KAAK,KAAK,OAAO,GAAG,CAAC,CAAC;AACnC,aAAK,OAAO,KAAK,KAAK,cAAc,YAAY,CAAC;AACjD,uBAAe,CAAC;AAEhB,eAAO,KAAK,OAAO,CAAC;AACpB,aAAK,OAAO,KAAK,IAAI;AAAA,MACzB;AAAA,IACJ;AACA,QAAI,aAAa,QAAQ;AACrB,WAAK,OAAO,KAAK,KAAK,cAAc,YAAY,CAAC;AACjD,qBAAe,CAAC;AAAA,IACpB;AAAA,EACJ;AAAA,EAEA,OAAO,QAAQ;AAEX,QAAI,MAAM,KAAK,QAAQ,OAAO,MAAM,IAAI;AAExC,UAAM,KAAK,YAAY;AAEvB,QAAI,IAAI,SAAS,KAAK,cAAc;AAChC,WAAK,YAAY;AACjB;AAAA,IACJ;AAEA,SAAK,YAAY;AAEjB,QAAI,gBAAgB,IAAI,MAAM,gBAAgB;AAC9C,QAAI,eAAe;AACf,UAAI,cAAc,UAAU,GAAG;AAC3B,aAAK,YAAY;AACjB;AAAA,MACJ;AACA,WAAK,YAAY,IAAI,OAAO,cAAc,KAAK;AAC/C,YAAM,IAAI,OAAO,GAAG,cAAc,KAAK;AAAA,IAC3C;AAEA,SAAK,aAAa,GAAG;AAAA,EACzB;AAAA,EAEA,WAAW;AACP,QAAI,KAAK,UAAU,QAAQ;AACvB,WAAK,aAAa,KAAK,SAAS;AAChC,WAAK,YAAY;AAAA,IACrB;AAGA,WAAO,kBAAkB,IAAI,KAAK,KAAK,QAAQ,EAAE,MAAM,2BAA2B,CAAC,CAAC;AAAA,EACxF;AACJ;;;AC1FA,IAAqB,WAArB,MAA8B;AAAA,EAL9B,OAK8B;AAAA;AAAA;AAAA,EAC1B,YAAY,MAAM;AACd,WAAO,QAAQ,CAAC;AAEhB,SAAK,aAAa,KAAK;AAEvB,SAAK,OAAO,CAAC,CAAC,KAAK;AACnB,SAAK,aAAa,CAAC;AACnB,QAAI,KAAK,YAAY;AACjB,WAAK,WAAW,WAAW,KAAK,IAAI;AAAA,IACxC;AAEA,SAAK,QAAQ;AAEb,SAAK,cAAc,CAAC;AAEpB,SAAK,cAAc;AAAA,MACf,OAAO;AAAA,MACP,SAAS;AAAA,IACb;AAEA,SAAK,0BAA0B;AAAA,MAC3B,OAAO;AAAA,IACX;AAEA,SAAK,qBAAqB;AAAA,MACtB,OAAO;AAAA,IACX;AAEA,SAAK,UAAU,CAAC;AAEhB,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EAEA,oBAAoB,kBAAkB;AAClC,QAAI,UAAU,KAAK,gBAAgB,GAAG;AAClC,WAAK,iBAAiB,IAAI,cAAc;AAAA,IAC5C,WAAW,oBAAoB,KAAK,gBAAgB,GAAG;AACnD,WAAK,iBAAiB,IAAI,UAAU,EAAE,SAAS,WAAW,KAAK,YAAY,OAAO,OAAO,OAAO,EAAE,CAAC;AAAA,IACvG,OAAO;AACH,WAAK,iBAAiB,IAAI,mBAAmB;AAAA,IACjD;AAAA,EACJ;AAAA,EAEA,MAAM,WAAW;AACb,QAAI,KAAK,UAAU,YAAY;AAC3B;AAAA,IACJ;AAEA,QAAI,KAAK,UAAU,UAAU;AACzB,WAAK,eAAe;AAAA,IACxB;AAGA,QAAI,aAAa,KAAK,WAAW;AACjC,aAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,UAAI,WAAW,WAAW,CAAC;AAC3B,UAAI,SAAS,SAAS,MAAM;AACxB,mBAAW,OAAO,GAAG,CAAC;AACtB;AAAA,MACJ;AAAA,IACJ;AAEA,UAAM,KAAK,mBAAmB;AAE9B,SAAK,UAAU,KAAK,iBAAiB,MAAM,KAAK,eAAe,SAAS,IAAI;AAE5E,SAAK,QAAQ;AAAA,EACjB;AAAA,EAEA,MAAM,qBAAqB;AACvB,aAAS,aAAa,KAAK,YAAY;AACnC,YAAM,UAAU,SAAS;AAAA,IAC7B;AAAA,EACJ;AAAA,EAEA,sBAAsB,KAAK;AACvB,QAAI,WAAW;AAAA,MACX,OAAO;AAAA,MACP,QAAQ,CAAC;AAAA,IACb;AAEA,QAAI,MAAM;AACV,QAAI,QAAQ;AACZ,QAAI,QAAQ;AAEZ,QAAI,QAAQ;AACZ,QAAI,UAAU;AACd,QAAI;AAEJ,aAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC5C,YAAM,IAAI,OAAO,CAAC;AAClB,cAAQ,OAAO;AAAA,QACX,KAAK;AACD,cAAI,QAAQ,KAAK;AACb,kBAAM,MAAM,KAAK,EAAE,YAAY;AAC/B,oBAAQ;AACR,oBAAQ;AACR;AAAA,UACJ;AACA,mBAAS;AACT;AAAA,QACJ,KAAK;AACD,cAAI,SAAS;AACT,qBAAS;AAAA,UACb,WAAW,QAAQ,MAAM;AACrB,sBAAU;AACV;AAAA,UACJ,WAAW,SAAS,QAAQ,OAAO;AAC/B,oBAAQ;AAAA,UACZ,WAAW,CAAC,SAAS,QAAQ,KAAK;AAC9B,oBAAQ;AAAA,UACZ,WAAW,CAAC,SAAS,QAAQ,KAAK;AAC9B,gBAAI,QAAQ,OAAO;AACf,uBAAS,QAAQ,MAAM,KAAK;AAAA,YAChC,OAAO;AACH,uBAAS,OAAO,GAAG,IAAI,MAAM,KAAK;AAAA,YACtC;AACA,oBAAQ;AACR,oBAAQ;AAAA,UACZ,OAAO;AACH,qBAAS;AAAA,UACb;AACA,oBAAU;AACV;AAAA,MACR;AAAA,IACJ;AAGA,YAAQ,MAAM,KAAK;AACnB,QAAI,UAAU,SAAS;AACnB,UAAI,QAAQ,OAAO;AAEf,iBAAS,QAAQ;AAAA,MACrB,OAAO;AAEH,iBAAS,OAAO,GAAG,IAAI;AAAA,MAC3B;AAAA,IACJ,WAAW,OAAO;AAGd,eAAS,OAAO,MAAM,YAAY,CAAC,IAAI;AAAA,IAC3C;AAEA,QAAI,SAAS,OAAO;AAChB,eAAS,QAAQ,SAAS,MAAM,YAAY;AAAA,IAChD;AAGA,sCAAkC,QAAQ;AAE1C,WAAO;AAAA,EACX;AAAA,EAEA,iBAAiB,KAAK,OAAO;AACzB,WACI,IACK,MAAM,OAAO,EAGb,OAAO,CAAC,eAAe,iBAAiB;AACrC,UAAI,KAAK,KAAK,aAAa,KAAK,CAAC,aAAa,KAAK,aAAa,GAAG;AAC/D,YAAI,OAAO;AAGP,iBAAO,cAAc,MAAM,GAAG,EAAE,IAAI;AAAA,QACxC,OAAO;AACH,iBAAO,gBAAgB;AAAA,QAC3B;AAAA,MACJ,OAAO;AACH,eAAO,gBAAgB,OAAO;AAAA,MAClC;AAAA,IACJ,CAAC,EAGA,QAAQ,QAAQ,EAAE;AAAA,EAE/B;AAAA,EAEA,iBAAiB;AACb,QAAI,CAAC,KAAK,SAAS;AACf,aAAO;AAAA,IACX;AAEA,QAAI,MAAM,WAAW,KAAK,YAAY,OAAO,OAAO,OAAO,EAAE,OAAO,KAAK,OAAO;AAEhF,QAAI,YAAY,KAAK,KAAK,YAAY,OAAO,OAAO,MAAM,GAAG;AACzD,YAAM,KAAK,iBAAiB,KAAK,SAAS,KAAK,KAAK,YAAY,OAAO,OAAO,KAAK,CAAC;AAAA,IACxF;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,iBAAiB;AACb,aAAS,IAAI,KAAK,YAAY,SAAS,GAAG,KAAK,GAAG,KAAK;AACnD,UAAI,OAAO,KAAK,YAAY,CAAC;AAC7B,UAAI,KAAK,MAAM,KAAK,IAAI,GAAG;AACvB,aAAK,YAAY,IAAI,CAAC,KAAK,OAAO;AAClC,aAAK,YAAY,OAAO,GAAG,CAAC;AAAA,MAChC,OAAO;AAEH,eAAO,KAAK,QAAQ,QAAQ,GAAG;AAC/B,YAAI,MAAM,KAAK,QAAQ,GAAG;AAC1B,YAAI,MAAM,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,OAAO,GAAG,GAAG,EAAE,KAAK;AAC3D,YAAI,QAAQ,MAAM,IAAI,KAAK,KAAK,OAAO,MAAM,CAAC,EAAE,KAAK;AACrD,aAAK,QAAQ,KAAK,EAAE,KAAK,IAAI,YAAY,GAAG,aAAa,KAAK,MAAM,CAAC;AAErE,gBAAQ,IAAI,YAAY,GAAG;AAAA,UACvB,KAAK;AACD,gBAAI,KAAK,YAAY,SAAS;AAC1B,mBAAK,cAAc,EAAE,OAAO,QAAQ,CAAC,EAAE;AAAA,YAC3C;AACA;AAAA,UACJ,KAAK;AACD,iBAAK,0BAA0B,EAAE,OAAO,QAAQ,CAAC,EAAE;AACnD;AAAA,UACJ,KAAK;AACD,iBAAK,qBAAqB,EAAE,OAAO,QAAQ,CAAC,EAAE;AAC9C;AAAA,UACJ,KAAK;AACD,iBAAK,YAAY;AACjB;AAAA,UACJ,KAAK;AACD,iBAAK,qBAAqB;AAC1B;AAAA,QACR;AAAA,MACJ;AAAA,IACJ;AAEA,SAAK,YAAY,SAAS,KAAK,sBAAsB,KAAK,YAAY,KAAK;AAC3E,SAAK,YAAY,YAAY,gBAAgB,KAAK,KAAK,YAAY,OAAO,KAAK,IACzE,KAAK,YAAY,OAAO,MAAM,OAAO,KAAK,YAAY,OAAO,MAAM,QAAQ,GAAG,IAAI,CAAC,IACnF;AAEN,QAAI,KAAK,YAAY,aAAa,KAAK,YAAY,OAAO,OAAO,UAAU;AAEvE,WAAK,WAAW,WAAW,KAAK;AAAA,QAC5B,OAAO,YAAY,OAAO,KAAK,YAAY,OAAO,OAAO,QAAQ;AAAA,QACjE,MAAM;AAAA,MACV,CAAC;AAAA,IACL;AAEA,SAAK,mBAAmB,SAAS,KAAK,sBAAsB,KAAK,mBAAmB,KAAK;AAEzF,SAAK,wBAAwB,WAAW,KAAK,wBAAwB,MAChE,YAAY,EACZ,MAAM,QAAQ,EACd,MAAM;AAEX,SAAK,oBAAoB,KAAK,wBAAwB,QAAQ;AAAA,EAClE;AAAA,EAEA,KAAK,MAAM;AACP,YAAQ,KAAK,OAAO;AAAA,MAChB,KAAK;AACD,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,QAAQ;AACb,iBAAO,KAAK,eAAe;AAAA,QAC/B;AACA,aAAK,YAAY,KAAK,WAAW,EAAE,OAAO,IAAI,CAAC;AAC/C;AAAA,MACJ,KAAK,QAAQ;AAET,aAAK,eAAe,OAAO,IAAI;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ;AACJ;;;AC/QO,IAAM,eAAe;AAAA,EACxB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AAAA,EACV,mBAAmB;AAAA,EACnB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,eAAe;AAAA,EACf,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,0BAA0B;AAAA,EAC1B,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,UAAU;AAAA,EACV,aAAa;AAAA,EACb,eAAe;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,qBAAqB;AAAA,EACrB,UAAU;AAAA,EACV,eAAe;AAAA,EACf,qCAAqC;AAAA,EACrC,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,2BAA2B;AAAA,EAC3B,eAAe;AAAA,EACf,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,WAAW;AAAA,EACX,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,kBAAkB;AAAA,EAClB,UAAU;AAAA,EACV,oBAAoB;AAAA,EACpB,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,UAAU;AAAA,EACV,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,SAAS;AAAA,EACT,eAAe;AAAA,EACf,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,oBAAoB;AAAA,EACpB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,aAAa;AAAA,EACb,sBAAsB;AAAA,EACtB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,0BAA0B;AAAA,EAC1B,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,6BAA6B;AAAA,EAC7B,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,sBAAsB;AAAA,EACtB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,UAAU;AAAA,EACV,0BAA0B;AAAA,EAC1B,oBAAoB;AAAA,EACpB,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,aAAa;AAAA,EACb,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,qBAAqB;AAAA,EACrB,cAAc;AAAA,EACd,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,mBAAmB;AAAA,EACnB,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,oBAAoB;AAAA,EACpB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,SAAS;AAAA,EACT,SAAS;AAAA,EACT,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EACtB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EACtB,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,yBAAyB;AAAA,EACzB,iBAAiB;AAAA,EACjB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,SAAS;AAAA,EACT,SAAS;AAAA,EACT,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AAAA,EACT,eAAe;AAAA,EACf,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,eAAe;AAAA,EACf,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,WAAW;AAAA,EACX,eAAe;AAAA,EACf,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,sBAAsB;AAAA,EACtB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,UAAU;AAAA,EACV,aAAa;AAAA,EACb,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,oBAAoB;AAAA,EACpB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,cAAc;AAAA,EACd,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,WAAW;AAAA,EACX,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,WAAW;AAAA,EACX,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,cAAc;AAAA,EACd,WAAW;AAAA,EACX,UAAU;AAAA,EACV,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,aAAa;AAAA,EACb,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,EACV,cAAc;AAAA,EACd,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,eAAe;AAAA,EACf,cAAc;AAAA,EACd,aAAa;AAAA,EACb,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,SAAS;AAAA,EACT,aAAa;AAAA,EACb,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,cAAc;AAAA,EACd,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS;AAAA,EACT,UAAU;AAAA,EACV,eAAe;AAAA,EACf,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,eAAe;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,UAAU;AAAA,EACV,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,UAAU;AAAA,EACV,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,WAAW;AAAA,EACX,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,aAAa;AAAA,EACb,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,cAAc;AAAA,EACd,cAAc;AAAA,EACd,aAAa;AAAA,EACb,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,UAAU;AAAA,EACV,cAAc;AAAA,EACd,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,cAAc;AAAA,EACd,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,cAAc;AAAA,EACd,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,cAAc;AAAA,EACd,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,cAAc;AAAA,EACd,cAAc;AAAA,EACd,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,aAAa;AAAA,EACb,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,cAAc;AAAA,EACd,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,cAAc;AAAA,EACd,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,aAAa;AAAA,EACb,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,cAAc;AAAA,EACd,aAAa;AAAA,EACb,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,mBAAmB;AAAA,EACnB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,WAAW;AAAA,EACX,aAAa;AAAA,EACb,cAAc;AAAA,EACd,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,cAAc;AAAA,EACd,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,aAAa;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AAAA,EACb,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AAAA,EACb,UAAU;AAAA,EACV,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,aAAa;AAAA,EACb,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,cAAc;AAAA,EACd,WAAW;AAAA,EACX,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,cAAc;AAAA,EACd,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,eAAe;AAAA,EACf,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,UAAU;AAAA,EACV,kBAAkB;AAAA,EAClB,WAAW;AAAA,EACX,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,cAAc;AAAA,EACd,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,WAAW;AAAA,EACX,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,qBAAqB;AAAA,EACrB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,aAAa;AAAA,EACb,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,aAAa;AAAA,EACb,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,UAAU;AAAA,EACV,aAAa;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AAAA,EACb,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,WAAW;AAAA,EACX,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,cAAc;AAAA,EACd,aAAa;AAAA,EACb,WAAW;AAAA,EACX,aAAa;AAAA,EACb,cAAc;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,eAAe;AAAA,EACf,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AACd;AAEA,IAAO,wBAAQ;;;ACzrER,SAAS,mBAAmB,KAAK;AACpC,SAAO,IAAI,QAAQ,qCAAqC,CAAC,OAAO,WAAW;AACvE,QAAI,OAAO,sBAAa,KAAK,MAAM,UAAU;AACzC,aAAO,sBAAa,KAAK;AAAA,IAC7B;AAEA,QAAI,OAAO,OAAO,CAAC,MAAM,OAAO,MAAM,OAAO,MAAM,SAAS,CAAC,MAAM,KAAK;AAEpE,aAAO;AAAA,IACX;AAEA,QAAI;AACJ,QAAI,OAAO,OAAO,CAAC,MAAM,KAAK;AAE1B,kBAAY,SAAS,OAAO,OAAO,CAAC,GAAG,EAAE;AAAA,IAC7C,OAAO;AAEH,kBAAY,SAAS,OAAO,OAAO,CAAC,GAAG,EAAE;AAAA,IAC7C;AAEA,QAAI,SAAS;AAEb,QAAK,aAAa,SAAU,aAAa,SAAW,YAAY,SAAU;AAEtE,aAAO;AAAA,IACX;AAEA,QAAI,YAAY,OAAQ;AACpB,mBAAa;AACb,gBAAU,OAAO,aAAe,cAAc,KAAM,OAAS,KAAM;AACnE,kBAAY,QAAU,YAAY;AAAA,IACtC;AAEA,cAAU,OAAO,aAAa,SAAS;AAEvC,WAAO;AAAA,EACX,CAAC;AACL;AArCgB;AAuCT,SAAS,WAAW,KAAK;AAC5B,SAAO,IAAI,KAAK,EAAE,QAAQ,aAAa,OAAK;AACxC,QAAI,MAAM,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE;AACrC,QAAI,IAAI,SAAS,GAAG;AAChB,YAAM,MAAM;AAAA,IAChB;AACA,WAAO,QAAQ,IAAI,YAAY,IAAI;AAAA,EACvC,CAAC;AACL;AARgB;AAUT,SAAS,WAAW,KAAK;AAC5B,MAAI,OAAO,WAAW,GAAG,EAAE,QAAQ,OAAO,QAAQ;AAClD,SAAO,UAAU,OAAO;AAC5B;AAHgB;AAKT,SAAS,WAAW,KAAK;AAC5B,QAAM,IAED,QAAQ,UAAU,GAAQ,EAC1B,QAAQ,qBAAqB,GAAG,EAEhC,QAAQ,iBAAiB,IAAI,EAC7B,QAAQ,wCAAwC,MAAM,EACtD,QAAQ,yCAAyC,GAAG,EACpD,QAAQ,qBAAqB,EAAE,EAC/B,QAAQ,uBAAuB,EAAE,EACjC,QAAQ,0BAA0B,EAAE,EACpC,QAAQ,uBAAuB,EAAE,EACjC,QAAQ,uBAAuB,EAAE,EAEjC,QAAQ,+CAA+C,QAAQ,EAE/D,QAAQ,0CAA0C,EAAE,EAEpD,QAAQ,8BAA8B,IAAI,EAE1C,QAAQ,gBAAgB,mBAAmB,EAE3C,QAAQ,YAAY,GAAG,EAGvB,QAAQ,WAAW,IAAI,EAEvB,QAAQ,WAAW,GAAG,EAEtB,QAAQ,WAAW,EAAE,EAErB,QAAQ,UAAU,MAAM,EACxB,QAAQ,QAAQ,IAAI,EACpB,QAAQ,QAAQ,IAAI;AAEzB,QAAM,mBAAmB,GAAG;AAE5B,SAAO;AACX;AAvCgB;AAyChB,SAAS,kBAAkB,SAAS;AAChC,SAAO,CAAC,EACH,OAAO,QAAQ,QAAQ,CAAC,CAAC,EACzB,OAAO,QAAQ,OAAO,IAAI,QAAQ,OAAO,MAAM,QAAQ,OAAO,EAC9D,KAAK,GAAG;AACjB;AALS;AAOT,SAAS,oBAAoB,WAAW;AACpC,MAAI,QAAQ,CAAC;AAEb,MAAI,iBAAiB,wBAAC,SAAS,gBAAgB;AAC3C,QAAI,aAAa;AACb,YAAM,KAAK,IAAI;AAAA,IACnB;AAEA,QAAI,QAAQ,OAAO;AACf,UAAI,aAAa,GAAG,QAAQ,IAAI;AAChC,UAAI,WAAW;AAEf,YAAM,KAAK,UAAU;AACrB,cAAQ,MAAM,QAAQ,cAAc;AACpC,YAAM,KAAK,QAAQ;AAAA,IACvB,OAAO;AACH,YAAM,KAAK,kBAAkB,OAAO,CAAC;AAAA,IACzC;AAAA,EACJ,GAfqB;AAiBrB,YAAU,QAAQ,cAAc;AAEhC,SAAO,MAAM,KAAK,EAAE;AACxB;AAvBS;AAyBT,SAAS,kBAAkB,SAAS;AAChC,SAAO,mBAAmB,WAAW,QAAQ,OAAO,CAAC,kCAAkC,WAAW,QAAQ,QAAQ,IAAI,QAAQ,OAAO,GAAG,CAAC;AAC7I;AAFS;AAIT,SAAS,oBAAoB,WAAW;AACpC,MAAI,QAAQ,CAAC;AAEb,MAAI,iBAAiB,wBAAC,SAAS,gBAAgB;AAC3C,QAAI,aAAa;AACb,YAAM,KAAK,wDAAwD;AAAA,IACvE;AAEA,QAAI,QAAQ,OAAO;AACf,UAAI,aAAa,4CAA4C,WAAW,QAAQ,IAAI,CAAC;AACrF,UAAI,WAAW;AAEf,YAAM,KAAK,UAAU;AACrB,cAAQ,MAAM,QAAQ,cAAc;AACpC,YAAM,KAAK,QAAQ;AAAA,IACvB,OAAO;AACH,YAAM,KAAK,kBAAkB,OAAO,CAAC;AAAA,IACzC;AAAA,EACJ,GAfqB;AAiBrB,YAAU,QAAQ,cAAc;AAEhC,SAAO,MAAM,KAAK,GAAG;AACzB;AAvBS;AAyBT,SAAS,UAAU,KAAK,YAAY,YAAY;AAC5C,SAAO,OAAO,IAAI,SAAS;AAC3B,eAAa,cAAc;AAE3B,MAAI,MAAM,GACN,MAAM,IAAI,QACV,SAAS,IACT,MACA;AAEJ,SAAO,MAAM,KAAK;AACd,WAAO,IAAI,OAAO,KAAK,UAAU;AACjC,QAAI,KAAK,SAAS,YAAY;AAC1B,gBAAU;AACV;AAAA,IACJ;AACA,QAAK,QAAQ,KAAK,MAAM,qBAAqB,GAAI;AAC7C,aAAO,MAAM,CAAC;AACd,gBAAU;AACV,aAAO,KAAK;AACZ;AAAA,IACJ,YAAY,QAAQ,KAAK,MAAM,cAAc,MAAM,MAAM,CAAC,EAAE,UAAU,cAAc,MAAM,CAAC,KAAK,IAAI,SAAS,KAAK,KAAK,QAAQ;AAC3H,aAAO,KAAK,OAAO,GAAG,KAAK,UAAU,MAAM,CAAC,EAAE,UAAU,cAAc,MAAM,CAAC,KAAK,IAAI,SAAS,GAAG;AAAA,IACtG,WAAY,QAAQ,IAAI,OAAO,MAAM,KAAK,MAAM,EAAE,MAAM,cAAc,GAAI;AACtE,aAAO,OAAO,MAAM,CAAC,EAAE,OAAO,GAAG,MAAM,CAAC,EAAE,UAAU,CAAC,cAAc,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;AAAA,IAClG;AAEA,cAAU;AACV,WAAO,KAAK;AACZ,QAAI,MAAM,KAAK;AACX,gBAAU;AAAA,IACd;AAAA,EACJ;AAEA,SAAO;AACX;AAnCS;AAqCF,SAAS,iBAAiB,SAAS;AACtC,MAAI,OAAO,CAAC;AAEZ,MAAI,QAAQ,MAAM;AACd,SAAK,KAAK,EAAE,KAAK,QAAQ,KAAK,kBAAkB,QAAQ,IAAI,EAAE,CAAC;AAAA,EACnE;AAEA,MAAI,QAAQ,SAAS;AACjB,SAAK,KAAK,EAAE,KAAK,WAAW,KAAK,QAAQ,QAAQ,CAAC;AAAA,EACtD;AAEA,MAAI,QAAQ,MAAM;AACd,QAAI,cAAc;AAAA,MACd,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,IACZ;AAEA,QAAI,UAAU,OAAO,SAAS,cAAc,QAAQ,OAAO,IAAI,KAAK,eAAe,WAAW,WAAW,EAAE,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC;AAExI,SAAK,KAAK,EAAE,KAAK,QAAQ,KAAK,QAAQ,CAAC;AAAA,EAC3C;AAEA,MAAI,QAAQ,MAAM,QAAQ,GAAG,QAAQ;AACjC,SAAK,KAAK,EAAE,KAAK,MAAM,KAAK,oBAAoB,QAAQ,EAAE,EAAE,CAAC;AAAA,EACjE;AAEA,MAAI,QAAQ,MAAM,QAAQ,GAAG,QAAQ;AACjC,SAAK,KAAK,EAAE,KAAK,MAAM,KAAK,oBAAoB,QAAQ,EAAE,EAAE,CAAC;AAAA,EACjE;AAEA,MAAI,QAAQ,OAAO,QAAQ,IAAI,QAAQ;AACnC,SAAK,KAAK,EAAE,KAAK,OAAO,KAAK,oBAAoB,QAAQ,GAAG,EAAE,CAAC;AAAA,EACnE;AAcA,MAAI,eAAe,KACd,IAAI,OAAK,EAAE,IAAI,MAAM,EACrB,OAAO,CAAC,KAAK,QAAQ;AAClB,WAAO,MAAM,MAAM,MAAM;AAAA,EAC7B,GAAG,CAAC;AAER,SAAO,KAAK,QAAQ,SAAO;AACvB,QAAI,SAAS,eAAe,IAAI,IAAI;AACpC,QAAI,SAAS,GAAG,IAAI,GAAG,KAAK,IAAI,OAAO,MAAM,CAAC;AAC9C,QAAI,cAAc,GAAG,IAAI,OAAO,IAAI,IAAI,SAAS,CAAC,CAAC,IAAI,IAAI,OAAO,MAAM,CAAC;AAEzE,QAAI,cAAc,UAAU,IAAI,KAAK,IAAI,IAAI,EACxC,MAAM,OAAO,EACb,IAAI,UAAQ,KAAK,KAAK,CAAC;AAE5B,WAAO,YAAY,IAAI,CAAC,MAAM,MAAM,GAAG,IAAI,cAAc,MAAM,GAAG,IAAI,EAAE;AAAA,EAC5E,CAAC;AAED,MAAI,gBAAgB,KACf,IAAI,OAAK,EAAE,MAAM,EACjB,OAAO,CAAC,KAAK,QAAQ;AAClB,WAAO,MAAM,MAAM,MAAM;AAAA,EAC7B,GAAG,CAAC;AAER,MAAI,aAAa,IAAI,OAAO,aAAa;AAEzC,MAAI,WAAW;AAAA,EACjB,UAAU;AAAA,EACV,KAAK,KAAK,IAAI,CAAC;AAAA,EACf,UAAU;AAAA;AAGR,SAAO;AACX;AApFgB;AAsFT,SAAS,iBAAiB,SAAS;AACtC,MAAI,OAAO,CAAC;AAEZ,MAAI,QAAQ,MAAM;AACd,SAAK,KAAK,yFAAyF,kBAAkB,QAAQ,IAAI,CAAC,QAAQ;AAAA,EAC9I;AAEA,MAAI,QAAQ,SAAS;AACjB,SAAK;AAAA,MACD,wHAAwH;AAAA,QACpH,QAAQ;AAAA,MACZ,CAAC;AAAA,IACL;AAAA,EACJ;AAEA,MAAI,QAAQ,MAAM;AACd,QAAI,cAAc;AAAA,MACd,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,IACZ;AAEA,QAAI,UAAU,OAAO,SAAS,cAAc,QAAQ,OAAO,IAAI,KAAK,eAAe,WAAW,WAAW,EAAE,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC;AAExI,SAAK;AAAA,MACD,6HAA6H;AAAA,QACzH,QAAQ;AAAA,MACZ,CAAC,KAAK,WAAW,OAAO,CAAC;AAAA,IAC7B;AAAA,EACJ;AAEA,MAAI,QAAQ,MAAM,QAAQ,GAAG,QAAQ;AACjC,SAAK,KAAK,uFAAuF,oBAAoB,QAAQ,EAAE,CAAC,QAAQ;AAAA,EAC5I;AAEA,MAAI,QAAQ,MAAM,QAAQ,GAAG,QAAQ;AACjC,SAAK,KAAK,uFAAuF,oBAAoB,QAAQ,EAAE,CAAC,QAAQ;AAAA,EAC5I;AAEA,MAAI,QAAQ,OAAO,QAAQ,IAAI,QAAQ;AACnC,SAAK,KAAK,wFAAwF,oBAAoB,QAAQ,GAAG,CAAC,QAAQ;AAAA,EAC9I;AAEA,MAAI,WAAW,oCAAoC,KAAK,SAAS,0CAA0C,EAAE,GAAG,KAAK;AAAA,IACjH;AAAA,EACJ,CAAC,GAAG,KAAK,SAAS,WAAW,EAAE;AAE/B,SAAO;AACX;AApDgB;;;ACjRhB,SAAS,eAAe,QAAQ;AAC5B,MAAI;AACJ,MAAI,UAAU;AACd,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI,YAAY,CAAC;AACjB,MAAI,OAAO;AAAA,IACP,SAAS,CAAC;AAAA,IACV,SAAS,CAAC;AAAA,IACV,OAAO,CAAC;AAAA,IACR,MAAM,CAAC;AAAA,EACX;AACA,MAAI;AACJ,MAAI;AAGJ,OAAK,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AAC3C,YAAQ,OAAO,CAAC;AAChB,QAAI,MAAM,SAAS,YAAY;AAC3B,cAAQ,MAAM,OAAO;AAAA,QACjB,KAAK;AACD,kBAAQ;AACR;AAAA,QACJ,KAAK;AACD,kBAAQ;AACR;AAAA,QACJ,KAAK;AACD,kBAAQ;AACR,oBAAU;AACV;AAAA,QACJ;AACI,kBAAQ;AAAA,MAChB;AAAA,IACJ,WAAW,MAAM,OAAO;AACpB,UAAI,UAAU,WAAW;AAIrB,cAAM,QAAQ,MAAM,MAAM,QAAQ,cAAc,EAAE;AAAA,MACtD;AACA,WAAK,KAAK,EAAE,KAAK,MAAM,KAAK;AAAA,IAChC;AAAA,EACJ;AAGA,MAAI,CAAC,KAAK,KAAK,UAAU,KAAK,QAAQ,QAAQ;AAC1C,SAAK,OAAO,KAAK;AACjB,SAAK,UAAU,CAAC;AAAA,EACpB;AAEA,MAAI,SAAS;AAET,SAAK,OAAO,KAAK,KAAK,KAAK,GAAG;AAC9B,cAAU,KAAK;AAAA,MACX,MAAM,YAAY,KAAK,QAAS,WAAW,QAAQ,IAAK;AAAA,MACxD,OAAO,KAAK,MAAM,SAAS,cAAc,KAAK,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC;AAAA,IACtE,CAAC;AAAA,EACL,OAAO;AAEH,QAAI,CAAC,KAAK,QAAQ,UAAU,KAAK,KAAK,QAAQ;AAC1C,WAAK,IAAI,KAAK,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,YAAI,KAAK,KAAK,CAAC,EAAE,MAAM,mBAAmB,GAAG;AACzC,eAAK,UAAU,KAAK,KAAK,OAAO,GAAG,CAAC;AACpC;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,gBAAgB,gCAAUC,UAAS;AACnC,YAAI,CAAC,KAAK,QAAQ,QAAQ;AACtB,eAAK,UAAU,CAACA,SAAQ,KAAK,CAAC;AAC9B,iBAAO;AAAA,QACX,OAAO;AACH,iBAAOA;AAAA,QACX;AAAA,MACJ,GAPoB;AAUpB,UAAI,CAAC,KAAK,QAAQ,QAAQ;AACtB,aAAK,IAAI,KAAK,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AAExC,eAAK,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE,QAAQ,4BAA4B,aAAa,EAAE,KAAK;AACpF,cAAI,KAAK,QAAQ,QAAQ;AACrB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAGA,QAAI,CAAC,KAAK,KAAK,UAAU,KAAK,QAAQ,QAAQ;AAC1C,WAAK,OAAO,KAAK;AACjB,WAAK,UAAU,CAAC;AAAA,IACpB;AAGA,QAAI,KAAK,QAAQ,SAAS,GAAG;AACzB,WAAK,OAAO,KAAK,KAAK,OAAO,KAAK,QAAQ,OAAO,CAAC,CAAC;AAAA,IACvD;AAGA,SAAK,OAAO,KAAK,KAAK,KAAK,GAAG;AAC9B,SAAK,UAAU,KAAK,QAAQ,KAAK,GAAG;AAEpC,QAAI,CAAC,KAAK,WAAW,eAAe,KAAK,KAAK,KAAK,KAAK,CAAC,GAAG;AAExD,YAAM,qBAAqB,cAAc,YAAY,KAAK,IAAI,CAAC;AAC/D,UAAI,sBAAsB,mBAAmB,QAAQ;AACjD,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,QAAI,CAAC,KAAK,WAAW,SAAS;AAC1B,aAAO,CAAC;AAAA,IACZ,OAAO;AACH,gBAAU;AAAA,QACN,SAAS,KAAK,WAAW,KAAK,QAAQ;AAAA,QACtC,MAAM,YAAY,KAAK,QAAQ,KAAK,WAAW,EAAE;AAAA,MACrD;AAEA,UAAI,QAAQ,YAAY,QAAQ,MAAM;AAClC,aAAK,QAAQ,WAAW,IAAI,MAAM,GAAG,GAAG;AACpC,kBAAQ,OAAO;AAAA,QACnB,OAAO;AACH,kBAAQ,UAAU;AAAA,QACtB;AAAA,MACJ;AAEA,gBAAU,KAAK,OAAO;AAAA,IAC1B;AAAA,EACJ;AAEA,SAAO;AACX;AApIS;AA4IT,IAAM,YAAN,MAAgB;AAAA,EApJhB,OAoJgB;AAAA;AAAA;AAAA,EACZ,YAAY,KAAK;AACb,SAAK,OAAO,OAAO,IAAI,SAAS;AAChC,SAAK,kBAAkB;AACvB,SAAK,oBAAoB;AACzB,SAAK,OAAO;AACZ,SAAK,UAAU;AAEf,SAAK,OAAO,CAAC;AAIb,SAAK,YAAY;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOL,KAAK;AAAA,IACT;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACP,QAAI,KACA,OAAO,CAAC;AACZ,aAAS,IAAI,GAAG,MAAM,KAAK,IAAI,QAAQ,IAAI,KAAK,KAAK;AACjD,YAAM,KAAK,IAAI,OAAO,CAAC;AACvB,WAAK,UAAU,GAAG;AAAA,IACtB;AAEA,SAAK,KAAK,QAAQ,UAAQ;AACtB,WAAK,SAAS,KAAK,SAAS,IAAI,SAAS,EAAE,KAAK;AAChD,UAAI,KAAK,OAAO;AACZ,aAAK,KAAK,IAAI;AAAA,MAClB;AAAA,IACJ,CAAC;AAED,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,KAAK;AACX,QAAI,KAAK,SAAS;AAAA,IAElB,WAAW,QAAQ,KAAK,mBAAmB;AACvC,WAAK,OAAO;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AACA,WAAK,KAAK,KAAK,KAAK,IAAI;AACxB,WAAK,OAAO;AACZ,WAAK,oBAAoB;AACzB,WAAK,UAAU;AACf;AAAA,IACJ,WAAW,CAAC,KAAK,qBAAqB,OAAO,KAAK,WAAW;AACzD,WAAK,OAAO;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AACA,WAAK,KAAK,KAAK,KAAK,IAAI;AACxB,WAAK,OAAO;AACZ,WAAK,oBAAoB,KAAK,UAAU,GAAG;AAC3C,WAAK,UAAU;AACf;AAAA,IACJ,WAAW,CAAC,KAAK,GAAG,EAAE,SAAS,KAAK,iBAAiB,KAAK,QAAQ,MAAM;AACpE,WAAK,UAAU;AACf;AAAA,IACJ;AAEA,QAAI,CAAC,KAAK,MAAM;AACZ,WAAK,OAAO;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AACA,WAAK,KAAK,KAAK,KAAK,IAAI;AAAA,IAC5B;AAEA,QAAI,QAAQ,MAAM;AAGd,YAAM;AAAA,IACV;AAEA,QAAI,IAAI,WAAW,CAAC,KAAK,MAAQ,CAAC,KAAK,GAAI,EAAE,SAAS,GAAG,GAAG;AAExD,WAAK,KAAK,SAAS;AAAA,IACvB;AAEA,SAAK,UAAU;AAAA,EACnB;AACJ;AAgBA,SAAS,cAAc,KAAK,SAAS;AACjC,YAAU,WAAW,CAAC;AAEtB,MAAI,YAAY,IAAI,UAAU,GAAG;AACjC,MAAI,SAAS,UAAU,SAAS;AAEhC,MAAI,YAAY,CAAC;AACjB,MAAI,UAAU,CAAC;AACf,MAAI,kBAAkB,CAAC;AAEvB,SAAO,QAAQ,WAAS;AACpB,QAAI,MAAM,SAAS,eAAe,MAAM,UAAU,OAAO,MAAM,UAAU,MAAM;AAC3E,UAAI,QAAQ,QAAQ;AAChB,kBAAU,KAAK,OAAO;AAAA,MAC1B;AACA,gBAAU,CAAC;AAAA,IACf,OAAO;AACH,cAAQ,KAAK,KAAK;AAAA,IACtB;AAAA,EACJ,CAAC;AAED,MAAI,QAAQ,QAAQ;AAChB,cAAU,KAAK,OAAO;AAAA,EAC1B;AAEA,YAAU,QAAQ,CAAAA,aAAW;AACzB,IAAAA,WAAU,eAAeA,QAAO;AAChC,QAAIA,SAAQ,QAAQ;AAChB,wBAAkB,gBAAgB,OAAOA,QAAO;AAAA,IACpD;AAAA,EACJ,CAAC;AAED,MAAI,QAAQ,SAAS;AACjB,QAAIC,aAAY,CAAC;AACjB,QAAI,kBAAkB,iCAAQ;AAC1B,WAAK,QAAQ,CAAAD,aAAW;AACpB,YAAIA,SAAQ,OAAO;AACf,iBAAO,gBAAgBA,SAAQ,KAAK;AAAA,QACxC,OAAO;AACH,UAAAC,WAAU,KAAKD,QAAO;AAAA,QAC1B;AAAA,MACJ,CAAC;AAAA,IACL,GARsB;AAStB,oBAAgB,eAAe;AAC/B,WAAOC;AAAA,EACX;AAEA,SAAO;AACX;AAhDS;AAmDT,IAAO,yBAAQ;;;AC9SR,SAAS,kBAAkB,aAAa;AAC3C,MAAI,SAAS;AACb,MAAI,YAAY;AAEhB,MAAI,QAAQ,IAAI,WAAW,WAAW;AACtC,MAAI,aAAa,MAAM;AACvB,MAAI,gBAAgB,aAAa;AACjC,MAAI,aAAa,aAAa;AAE9B,MAAI,GAAG,GAAG,GAAG;AACb,MAAI;AAGJ,WAAS,IAAI,GAAG,IAAI,YAAY,IAAI,IAAI,GAAG;AAEvC,YAAS,MAAM,CAAC,KAAK,KAAO,MAAM,IAAI,CAAC,KAAK,IAAK,MAAM,IAAI,CAAC;AAG5D,SAAK,QAAQ,aAAa;AAC1B,SAAK,QAAQ,WAAW;AACxB,SAAK,QAAQ,SAAS;AACtB,QAAI,QAAQ;AAGZ,cAAU,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,UAAU,CAAC;AAAA,EACtE;AAGA,MAAI,iBAAiB,GAAG;AACpB,YAAQ,MAAM,UAAU;AAExB,SAAK,QAAQ,QAAQ;AAGrB,SAAK,QAAQ,MAAM;AAEnB,cAAU,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AAAA,EAC5C,WAAW,iBAAiB,GAAG;AAC3B,YAAS,MAAM,UAAU,KAAK,IAAK,MAAM,aAAa,CAAC;AAEvD,SAAK,QAAQ,UAAU;AACvB,SAAK,QAAQ,SAAS;AAGtB,SAAK,QAAQ,OAAO;AAEpB,cAAU,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AAAA,EAC3D;AAEA,SAAO;AACX;AAlDgB;;;ACVhB,IAAqB,aAArB,MAAqB,YAAW;AAAA,EARhC,OAQgC;AAAA;AAAA;AAAA,EAC5B,OAAO,MAAM,KAAK,SAAS;AACvB,UAAM,SAAS,IAAI,YAAW,OAAO;AACrC,WAAO,OAAO,MAAM,GAAG;AAAA,EAC3B;AAAA,EAEA,YAAY,SAAS;AACjB,SAAK,UAAU,WAAW,CAAC;AAE3B,SAAK,OAAO,KAAK,cAAc,IAAI,SAAS;AAAA,MACxC,YAAY;AAAA,IAChB,CAAC;AACD,SAAK,aAAa,CAAC;AAEnB,SAAK,cAAc,CAAC;AACpB,SAAK,cAAc,CAAC;AAEpB,SAAK,sBACA,KAAK,QAAQ,sBAAsB,IAC/B,SAAS,EACT,QAAQ,WAAW,EAAE,EACrB,KAAK,EACL,YAAY,KAAK;AAE1B,SAAK,UAAU;AAAA,EACnB;AAAA,EAEA,MAAM,WAAW;AAEb,UAAM,KAAK,KAAK,SAAS;AAAA,EAC7B;AAAA,EAEA,MAAM,YAAY,MAAM,SAAS;AAC7B,QAAI,aAAa,KAAK;AAGtB,QAAI,WAAW,UAAU,KAAK,SAAS,KAAK,KAAK,CAAC,MAAM,MAAQ,KAAK,CAAC,MAAM,IAAM;AAE9E,eAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,YAAI,WAAW,WAAW,CAAC;AAE3B,YAAI,KAAK,WAAW,SAAS,MAAM,SAAS,KAAK,KAAK,WAAW,SAAS,MAAM,SAAS,GAAG;AACxF;AAAA,QACJ;AAEA,YAAI,eAAe,KAAK,WAAW,SAAS,MAAM,SAAS;AAE3D,YAAI,iBAAiB,KAAK,KAAK,SAAS,CAAC,MAAM,MAAQ,KAAK,KAAK,SAAS,CAAC,MAAM,KAAO;AACpF;AAAA,QACJ;AAEA,YAAI,iBAAiB;AACrB,iBAASC,KAAI,GAAGA,KAAI,SAAS,MAAM,QAAQA,MAAK;AAC5C,cAAI,KAAKA,KAAI,CAAC,MAAM,SAAS,MAAMA,EAAC,GAAG;AACnC,6BAAiB;AACjB;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,CAAC,gBAAgB;AACjB;AAAA,QACJ;AAEA,YAAI,cAAc;AACd,gBAAM,SAAS,KAAK,SAAS;AAE7B,eAAK,cAAc,SAAS,KAAK,cAAc,KAAK;AAAA,QACxD,OAAO;AAEH,gBAAM,SAAS,KAAK,mBAAmB;AAEvC,eAAK,cAAc,IAAI,SAAS;AAAA,YAC5B,YAAY;AAAA,YACZ,YAAY,SAAS;AAAA,UACzB,CAAC;AAAA,QACL;AAEA,YAAI,SAAS;AACT,iBAAO,KAAK,SAAS;AAAA,QACzB;AAEA;AAAA,MACJ;AAAA,IACJ;AAEA,SAAK,YAAY,KAAK,IAAI;AAE1B,QAAI,SAAS;AACT,aAAO,KAAK,SAAS;AAAA,IACzB;AAAA,EACJ;AAAA,EAEA,WAAW;AACP,QAAI,WAAW,KAAK;AACpB,QAAI,SAAS,KAAK;AAElB,QAAI,MAAM,6BAAM;AACZ,aAAO;AAAA,QACH,OAAO,IAAI,WAAW,KAAK,KAAK,UAAU,SAAS,QAAQ;AAAA,QAC3D,MAAM,KAAK,WAAW,KAAK,GAAG;AAAA,MAClC;AAAA,IACJ,GALU;AAOV,WAAO,KAAK,UAAU,KAAK,GAAG,QAAQ;AAClC,YAAM,IAAI,KAAK,GAAG,KAAK,SAAS;AAEhC,UAAI,MAAM,MAAQ,MAAM,IAAM;AAC1B,iBAAS,KAAK;AAAA,MAClB;AAEA,UAAI,MAAM,IAAM;AACZ,eAAO,IAAI;AAAA,MACf;AAAA,IACJ;AAEA,WAAO,IAAI;AAAA,EACf;AAAA,EAEA,MAAM,kBAAkB;AAGpB,QAAI,cAAc,CAAC;AAEnB,QAAI,YAAY,oBAAI,IAAI;AACxB,QAAI,UAAW,KAAK,UAAU,oBAAI,IAAI;AAEtC,QAAI,yBAAyB,KAAK,uBAAuB;AAEzD,QAAI,OAAO,8BAAO,MAAM,aAAa,YAAY;AAC7C,oBAAc,eAAe;AAC7B,gBAAU,WAAW;AAErB,UAAI,CAAC,KAAK,YAAY,WAAW;AAE7B,YAAI,KAAK,sBAAsB,IAAI,KAAK,CAAC,wBAAwB;AAC7D,gBAAM,YAAY,IAAI,YAAW;AACjC,eAAK,aAAa,MAAM,UAAU,MAAM,KAAK,OAAO;AAEpD,cAAI,CAAC,QAAQ,IAAI,IAAI,GAAG;AACpB,oBAAQ,IAAI,MAAM,CAAC,CAAC;AAAA,UACxB;AAEA,cAAI,YAAY,QAAQ,IAAI,IAAI;AAGhC,cAAI,KAAK,WAAW,QAAQ,CAAC,KAAK,WAAW,MAAM;AAC/C,sBAAU,QAAQ,UAAU,SAAS,CAAC;AACtC,sBAAU,MAAM,KAAK,EAAE,MAAM,cAAc,OAAO,KAAK,WAAW,CAAC;AACnE,sBAAU,IAAI,OAAO;AAAA,UACzB;AAEA,cAAI,KAAK,WAAW,MAAM;AACtB,sBAAU,OAAO,UAAU,QAAQ,CAAC;AACpC,sBAAU,KAAK,KAAK,EAAE,MAAM,cAAc,OAAO,KAAK,WAAW,CAAC;AAClE,sBAAU,IAAI,MAAM;AAAA,UACxB;AAEA,cAAI,UAAU,SAAS;AACnB,sBAAU,QAAQ,QAAQ,CAAC,cAAc,gBAAgB;AACrD,sBAAQ,IAAI,aAAa,YAAY;AAAA,YACzC,CAAC;AAAA,UACL;AAEA,mBAAS,cAAc,KAAK,WAAW,eAAe,CAAC,GAAG;AACtD,iBAAK,YAAY,KAAK,UAAU;AAAA,UACpC;AAAA,QACJ,WAGS,KAAK,iBAAiB,IAAI,GAAG;AAClC,cAAI,WAAW,KAAK,YAAY,OAAO,MAAM,OAAO,KAAK,YAAY,OAAO,MAAM,QAAQ,GAAG,IAAI,CAAC;AAElG,cAAI,eAAe,eAAe;AAClC,cAAI,CAAC,QAAQ,IAAI,YAAY,GAAG;AAC5B,oBAAQ,IAAI,cAAc,CAAC,CAAC;AAAA,UAChC;AAEA,cAAI,YAAY,QAAQ,IAAI,YAAY;AACxC,oBAAU,QAAQ,IAAI,UAAU,QAAQ,KAAK,CAAC;AAC9C,oBAAU,QAAQ,EAAE,KAAK,EAAE,MAAM,QAAQ,OAAO,KAAK,eAAe,EAAE,CAAC;AACvE,oBAAU,IAAI,QAAQ;AAAA,QAC1B,WAGS,KAAK,SAAS;AACnB,gBAAM,WAAW,KAAK,mBAAmB,OAAO,OAAO,YAAY,KAAK,YAAY,OAAO,OAAO,QAAQ;AAC1G,gBAAM,aAAa;AAAA,YACf,UAAU,WAAW,YAAY,QAAQ,IAAI;AAAA,YAC7C,UAAU,KAAK,YAAY,OAAO;AAAA,YAClC,aAAa,KAAK,mBAAmB,OAAO,SAAS;AAAA,UACzD;AAEA,cAAI,WAAW,KAAK,WAAW;AAC3B,uBAAW,UAAU;AAAA,UACzB;AAEA,cAAI,KAAK,oBAAoB;AACzB,uBAAW,cAAc,KAAK;AAAA,UAClC;AAEA,cAAI,KAAK,WAAW;AAChB,uBAAW,YAAY,KAAK;AAAA,UAChC;AAEA,kBAAQ,KAAK,YAAY,OAAO,OAAO;AAAA;AAAA,YAEnC,KAAK;AAAA,YACL,KAAK,mBAAmB;AACpB,kBAAI,KAAK,YAAY,OAAO,OAAO,QAAQ;AACvC,2BAAW,SAAS,KAAK,YAAY,OAAO,OAAO,OAAO,SAAS,EAAE,YAAY,EAAE,KAAK;AAAA,cAC5F;AAGA,oBAAM,cAAc,KAAK,eAAe,EAAE,QAAQ,UAAU,IAAI,EAAE,QAAQ,QAAQ,IAAI;AACtF,yBAAW,UAAU,YAAY,OAAO,WAAW;AACnD;AAAA,YACJ;AAAA;AAAA,YAGA;AACI,yBAAW,UAAU,KAAK;AAAA,UAClC;AAEA,eAAK,YAAY,KAAK,UAAU;AAAA,QACpC;AAAA,MACJ,WAAW,KAAK,YAAY,cAAc,eAAe;AACrD,sBAAc;AAAA,MAClB,WAAW,KAAK,YAAY,cAAc,WAAW;AACjD,kBAAU;AAAA,MACd;AAEA,eAAS,aAAa,KAAK,YAAY;AACnC,cAAM,KAAK,WAAW,aAAa,OAAO;AAAA,MAC9C;AAAA,IACJ,GA1GW;AA4GX,UAAM,KAAK,KAAK,MAAM,OAAO,CAAC,CAAC;AAE/B,YAAQ,QAAQ,cAAY;AACxB,gBAAU,QAAQ,cAAY;AAC1B,YAAI,CAAC,YAAY,QAAQ,GAAG;AACxB,sBAAY,QAAQ,IAAI,CAAC;AAAA,QAC7B;AAEA,YAAI,SAAS,QAAQ,GAAG;AACpB,mBAAS,QAAQ,EAAE,QAAQ,eAAa;AACpC,oBAAQ,UAAU,MAAM;AAAA,cACpB,KAAK;AACD,4BAAY,QAAQ,EAAE,KAAK,UAAU,KAAK;AAC1C;AAAA,cAEJ,KAAK;AACD;AACI,0BAAQ,UAAU;AAAA,oBACd,KAAK;AACD,kCAAY,QAAQ,EAAE,KAAK,iBAAiB,UAAU,KAAK,CAAC;AAC5D;AAAA,oBACJ,KAAK;AACD,kCAAY,QAAQ,EAAE,KAAK,iBAAiB,UAAU,KAAK,CAAC;AAC5D;AAAA,kBACR;AAAA,gBACJ;AACA;AAAA,YACR;AAAA,UACJ,CAAC;AAAA,QACL,OAAO;AACH,cAAI;AACJ,kBAAQ,UAAU;AAAA,YACd,KAAK;AACD,gCAAkB;AAClB;AAAA,YACJ,KAAK;AACD,gCAAkB;AAClB;AAAA,UACR;AAEA,WAAC,SAAS,eAAe,KAAK,CAAC,GAAG,QAAQ,eAAa;AACnD,oBAAQ,UAAU,MAAM;AAAA,cACpB,KAAK;AACD,wBAAQ,UAAU;AAAA,kBACd,KAAK;AACD,gCAAY,QAAQ,EAAE,KAAK,WAAW,UAAU,KAAK,CAAC;AACtD;AAAA,kBACJ,KAAK;AACD,gCAAY,QAAQ,EAAE,KAAK,WAAW,UAAU,KAAK,CAAC;AACtD;AAAA,gBACR;AACA;AAAA,cAEJ,KAAK;AACD;AACI,0BAAQ,UAAU;AAAA,oBACd,KAAK;AACD,kCAAY,QAAQ,EAAE,KAAK,iBAAiB,UAAU,KAAK,CAAC;AAC5D;AAAA,oBACJ,KAAK;AACD,kCAAY,QAAQ,EAAE,KAAK,iBAAiB,UAAU,KAAK,CAAC;AAC5D;AAAA,kBACR;AAAA,gBACJ;AACA;AAAA,YACR;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAED,WAAO,KAAK,WAAW,EAAE,QAAQ,cAAY;AACzC,kBAAY,QAAQ,IAAI,YAAY,QAAQ,EAAE,KAAK,IAAI;AAAA,IAC3D,CAAC;AAED,SAAK,cAAc;AAAA,EACvB;AAAA,EAEA,iBAAiB,MAAM;AACnB,QAAI,KAAK,mBAAmB,OAAO,UAAU,cAAc;AAEvD,aAAO;AAAA,IACX;AAEA,YAAQ,KAAK,YAAY,OAAO,OAAO;AAAA,MACnC,KAAK;AAAA,MACL,KAAK;AACD,eAAO;AAAA,MAEX,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AACI,eAAO;AAAA,IACf;AAAA,EACJ;AAAA,EAEA,sBAAsB,MAAM;AACxB,QAAI,KAAK,YAAY,OAAO,UAAU,kBAAkB;AACpD,aAAO;AAAA,IACX;AACA,QAAI,cAAc,KAAK,mBAAmB,OAAO,UAAU,KAAK,QAAQ,oBAAoB,eAAe;AAC3G,WAAO,gBAAgB;AAAA,EAC3B;AAAA;AAAA,EAGA,yBAAyB;AACrB,QAAI,KAAK,QAAQ,wBAAwB;AACrC,aAAO;AAAA,IACX;AAEA,QAAI,yBAAyB;AAC7B,QAAI,OAAO,iCAAQ;AACf,UAAI,CAAC,KAAK,YAAY,WAAW;AAC7B,YAAI,CAAC,2BAA2B,yBAAyB,EAAE,SAAS,KAAK,YAAY,OAAO,KAAK,GAAG;AAChG,mCAAyB;AAAA,QAC7B;AAAA,MACJ;AAEA,eAAS,aAAa,KAAK,YAAY;AACnC,aAAK,SAAS;AAAA,MAClB;AAAA,IACJ,GAVW;AAWX,SAAK,KAAK,IAAI;AACd,WAAO;AAAA,EACX;AAAA,EAEA,MAAM,cAAc,QAAQ;AACxB,QAAI,WAAW;AACf,QAAI,SAAS,CAAC;AACd,UAAM,SAAS,OAAO,UAAU;AAEhC,WAAO,MAAM;AACT,YAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,UAAI,MAAM;AACN;AAAA,MACJ;AACA,aAAO,KAAK,KAAK;AACjB,kBAAY,MAAM;AAAA,IACtB;AAEA,UAAM,SAAS,IAAI,WAAW,QAAQ;AACtC,QAAI,eAAe;AACnB,aAAS,SAAS,QAAQ;AACtB,aAAO,IAAI,OAAO,YAAY;AAC9B,sBAAgB,MAAM;AAAA,IAC1B;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,MAAM,MAAM,KAAK;AACb,QAAI,KAAK,SAAS;AACd,YAAM,IAAI,MAAM,sDAAsD;AAAA,IAC1E;AACA,SAAK,UAAU;AAGf,QAAI,OAAO,OAAO,IAAI,cAAc,YAAY;AAC5C,YAAM,MAAM,KAAK,cAAc,GAAG;AAAA,IACtC;AAGA,UAAM,OAAO,IAAI,YAAY,CAAC;AAG9B,QAAI,OAAO,QAAQ,UAAU;AACzB,YAAM,YAAY,OAAO,GAAG;AAAA,IAChC;AAGA,QAAI,eAAe,QAAQ,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM,iBAAiB;AAChF,YAAM,MAAM,kBAAkB,GAAG;AAAA,IACrC;AAGA,QAAI,IAAI,kBAAkB,aAAa;AACnC,YAAM,IAAI,WAAW,GAAG,EAAE;AAAA,IAC9B;AAEA,SAAK,MAAM;AAEX,SAAK,KAAK,IAAI,WAAW,GAAG;AAC5B,SAAK,UAAU;AAEf,WAAO,KAAK,UAAU,KAAK,GAAG,QAAQ;AAClC,YAAM,OAAO,KAAK,SAAS;AAE3B,YAAM,KAAK,YAAY,KAAK,OAAO,KAAK,IAAI;AAAA,IAChD;AAEA,UAAM,KAAK,gBAAgB;AAE3B,UAAM,UAAU;AAAA,MACZ,SAAS,KAAK,KAAK,QAAQ,IAAI,YAAU,EAAE,KAAK,MAAM,KAAK,OAAO,MAAM,MAAM,EAAE,EAAE,QAAQ;AAAA,IAC9F;AAEA,eAAW,OAAO,CAAC,QAAQ,QAAQ,GAAG;AAClC,YAAM,gBAAgB,KAAK,KAAK,QAAQ,KAAK,UAAQ,KAAK,QAAQ,GAAG;AACrE,UAAI,iBAAiB,cAAc,OAAO;AACtC,cAAM,YAAY,uBAAc,cAAc,KAAK;AACnD,YAAI,aAAa,UAAU,QAAQ;AAC/B,kBAAQ,GAAG,IAAI,UAAU,CAAC;AAAA,QAC9B;AAAA,MACJ;AAAA,IACJ;AAEA,eAAW,OAAO,CAAC,gBAAgB,aAAa,GAAG;AAC/C,YAAM,gBAAgB,KAAK,KAAK,QAAQ,KAAK,UAAQ,KAAK,QAAQ,GAAG;AACrE,UAAI,iBAAiB,cAAc,OAAO;AACtC,cAAM,YAAY,uBAAc,cAAc,KAAK;AACnD,YAAI,aAAa,UAAU,UAAU,UAAU,CAAC,EAAE,SAAS;AACvD,gBAAM,WAAW,IAAI,QAAQ,UAAU,CAAC,GAAG,MAAM,EAAE,YAAY,CAAC;AAChE,kBAAQ,QAAQ,IAAI,UAAU,CAAC,EAAE;AAAA,QACrC;AAAA,MACJ;AAAA,IACJ;AAEA,eAAW,OAAO,CAAC,MAAM,MAAM,OAAO,UAAU,GAAG;AAC/C,YAAM,iBAAiB,KAAK,KAAK,QAAQ,OAAO,UAAQ,KAAK,QAAQ,GAAG;AACxE,UAAI,YAAY,CAAC;AAEjB,qBACK,OAAO,WAAS,SAAS,MAAM,KAAK,EACpC,IAAI,WAAS,uBAAc,MAAM,KAAK,CAAC,EACvC,QAAQ,YAAW,YAAY,UAAU,OAAO,UAAU,CAAC,CAAC,CAAE;AAEnE,UAAI,aAAa,UAAU,QAAQ;AAC/B,cAAM,WAAW,IAAI,QAAQ,UAAU,CAAC,GAAG,MAAM,EAAE,YAAY,CAAC;AAChE,gBAAQ,QAAQ,IAAI;AAAA,MACxB;AAAA,IACJ;AAEA,eAAW,OAAO,CAAC,WAAW,cAAc,eAAe,YAAY,GAAG;AACtE,YAAM,SAAS,KAAK,KAAK,QAAQ,KAAK,UAAQ,KAAK,QAAQ,GAAG;AAC9D,UAAI,UAAU,OAAO,OAAO;AACxB,cAAM,WAAW,IAAI,QAAQ,UAAU,CAAC,GAAG,MAAM,EAAE,YAAY,CAAC;AAChE,gBAAQ,QAAQ,IAAI,YAAY,OAAO,KAAK;AAAA,MAChD;AAAA,IACJ;AAEA,QAAI,aAAa,KAAK,KAAK,QAAQ,KAAK,UAAQ,KAAK,QAAQ,MAAM;AACnE,QAAI,YAAY;AACZ,UAAI,OAAO,IAAI,KAAK,WAAW,KAAK;AACpC,UAAI,CAAC,QAAQ,KAAK,SAAS,MAAM,gBAAgB;AAC7C,eAAO,WAAW;AAAA,MACtB,OAAO;AAEH,eAAO,KAAK,YAAY;AAAA,MAC5B;AACA,cAAQ,OAAO;AAAA,IACnB;AAEA,QAAI,KAAK,aAAa,MAAM;AACxB,cAAQ,OAAO,KAAK,YAAY;AAAA,IACpC;AAEA,QAAI,KAAK,aAAa,OAAO;AACzB,cAAQ,OAAO,KAAK,YAAY;AAAA,IACpC;AAEA,YAAQ,cAAc,KAAK;AAE3B,YAAQ,KAAK,oBAAoB;AAAA,MAC7B,KAAK;AACD;AAAA,MAEJ,KAAK;AACD,iBAAS,cAAc,QAAQ,eAAe,CAAC,GAAG;AAC9C,cAAI,YAAY,SAAS;AACrB,uBAAW,UAAU,kBAAkB,WAAW,OAAO;AACzD,uBAAW,WAAW;AAAA,UAC1B;AAAA,QACJ;AACA;AAAA,MAEJ,KAAK;AACD,YAAI,oBAAoB,IAAI,YAAY,MAAM;AAC9C,iBAAS,cAAc,QAAQ,eAAe,CAAC,GAAG;AAC9C,cAAI,YAAY,SAAS;AACrB,uBAAW,UAAU,kBAAkB,OAAO,WAAW,OAAO;AAChE,uBAAW,WAAW;AAAA,UAC1B;AAAA,QACJ;AACA;AAAA,MAEJ;AACI,cAAM,IAAI,MAAM,6BAA6B;AAAA,IACrD;AAEA,WAAO;AAAA,EACX;AACJ;;;ACnhBO,IAAM,qBAAN,MAAyB;AAAA,EAHhC,OAGgC;AAAA;AAAA;AAAA,EAC9B,MAAM,WAAW,UAAsD;AACrE,QAAI;AACF,YAAM,SAAS,IAAI,WAAW;AAC9B,YAAMC,SAAQ,MAAM,OAAO,MAAM,QAAQ;AAGzC,YAAM,OAAO;AAAA,QACX,SAASA,OAAM,MAAM,WAAW;AAAA,QAChC,MAAMA,OAAM,MAAM,QAAQ;AAAA,MAC5B;AAEA,YAAM,KAAKA,OAAM,KAAK,CAAC,GAAG,WAAW;AACrC,YAAM,UAAUA,OAAM,WAAW;AACjC,YAAM,OAAOA,OAAM,QAAQ;AAC3B,YAAM,OAAOA,OAAM,QAAQ;AAG3B,YAAM,mBAAmB,KAAK,wBAAwB,OAAO,MAAM,IAAI;AAEvE,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,wBAAwB,KAAK;AAG3C,aAAO;AAAA,QACL,MAAM,EAAE,SAAS,IAAI,MAAM,GAAG;AAAA,QAC9B,IAAI;AAAA,QACJ,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,QACN,kBAAkB;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAAA,EAEQ,wBAAwB,SAAqC;AACnE,QAAI,CAAC,QAAS,QAAO;AAGrB,UAAM,WAAW;AAAA;AAAA,MAEf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAGA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAGA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA,MAGA;AAAA;AAAA,MACA;AAAA;AAAA,IACF;AAEA,eAAW,WAAW,UAAU;AAC9B,YAAM,QAAQ,QAAQ,MAAM,OAAO;AACnC,UAAI,SAAS,MAAM,CAAC,GAAG;AACrB,cAAM,OAAO,MAAM,CAAC,EAAE,QAAQ,UAAU,EAAE;AAG1C,YAAI,KAAK,UAAU,KAAK,KAAK,UAAU,GAAG;AACxC,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAGA,UAAM,oBAAoB,QAAQ,MAAM,cAAc;AACtD,QAAI,qBAAqB,kBAAkB,SAAS,GAAG;AAErD,aAAO,kBAAkB,CAAC;AAAA,IAC5B;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,aAAa,SAA2B;AACtC,UAAM,cAAc;AACpB,UAAM,UAAU,QAAQ,MAAM,WAAW;AACzC,WAAO,WAAW,CAAC;AAAA,EACrB;AAAA;AAAA,EAGA,oBAAoB,SAA2B;AAC7C,UAAM,gBAAgB;AAAA,MACpB;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACF;AAEA,UAAM,eAAyB,CAAC;AAChC,kBAAc,QAAQ,aAAW;AAC/B,YAAM,UAAU,QAAQ,MAAM,OAAO;AACrC,UAAI,SAAS;AACX,qBAAa,KAAK,GAAG,OAAO;AAAA,MAC9B;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,gBAAgB,SAAiB,SAAyB;AACxD,UAAM,eAAe,QAAQ,YAAY;AACzC,UAAM,eAAe,QAAQ,YAAY;AAGzC,QAAI,aAAa,SAAS,cAAc,KACpC,aAAa,SAAS,cAAI,KAC1B,aAAa,SAAS,mBAAmB,KACzC,aAAa,SAAS,oBAAK,GAAG;AAChC,aAAO;AAAA,IACT;AAGA,QAAI,aAAa,SAAS,OAAO,KAC7B,aAAa,SAAS,UAAU,KAChC,aAAa,SAAS,cAAI,KAC1B,aAAa,SAAS,cAAI,GAAG;AAC/B,aAAO;AAAA,IACT;AAGA,QAAI,aAAa,SAAS,SAAS,KAC/B,aAAa,SAAS,SAAS,KAC/B,aAAa,SAAS,cAAI,KAC1B,aAAa,SAAS,cAAI,GAAG;AAC/B,aAAO;AAAA,IACT;AAGA,QAAI,aAAa,SAAS,cAAc,KACpC,aAAa,SAAS,OAAO,KAC7B,aAAa,SAAS,cAAI,KAC1B,aAAa,SAAS,cAAI,GAAG;AAC/B,aAAO;AAAA,IACT;AAGA,QAAI,aAAa,SAAS,OAAO,KAC7B,aAAa,SAAS,MAAM,KAC5B,aAAa,SAAS,UAAU,KAChC,aAAa,SAAS,cAAI,KAC1B,aAAa,SAAS,cAAI,GAAG;AAC/B,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,UAAU,MAAsB;AAC9B,QAAI,CAAC,KAAM,QAAO;AAElB,WAAO,KACJ,QAAQ,gCAAgC,EAAE,EAC1C,QAAQ,8BAA8B,EAAE,EACxC,QAAQ,YAAY,EAAE,EACtB,QAAQ,WAAW,GAAG,EACtB,QAAQ,UAAU,GAAG,EACrB,QAAQ,SAAS,GAAG,EACpB,QAAQ,SAAS,GAAG,EACpB,QAAQ,WAAW,GAAG,EACtB,QAAQ,UAAU,GAAG,EACrB,QAAQ,QAAQ,GAAG,EACnB,KAAK;AAAA,EACV;AACF;;;ACjLO,IAAM,eAAN,MAAmB;AAAA,EAGxB,YACU,KACA,WACR;AAFQ;AACA;AAER,SAAK,gBAAgB,IAAI,mBAAmB;AAAA,EAC9C;AAAA,EAtBF,OAc0B;AAAA;AAAA;AAAA,EAChB;AAAA,EASR,MAAM,gBAAgB,QAAgB,SAAiD;AAErF,UAAM,OAAO,MAAM,KAAK,UAAU,YAAY,MAAM;AACpD,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,cAAc,gCAAO;AAAA,IACjC;AAEA,QAAI,KAAK,SAAS,GAAG;AACnB,YAAM,IAAI,gBAAgB,gFAAe;AAAA,IAC3C;AAGA,UAAM,SAAS,MAAM,KAAK,UAAU,cAAc,QAAQ,QAAQ;AAClE,QAAI,CAAC,UAAU,OAAO,WAAW,GAAG;AAClC,YAAM,IAAI,gBAAgB,gCAAO;AAAA,IACnC;AAGA,UAAM,SAAS,KAAK,oBAAoB;AACxC,UAAMC,SAAQ,GAAG,MAAM,IAAI,OAAO,MAAM;AAGxC,UAAM,gBAAgB,MAAM,KAAK,UAAU,oBAAoBA,MAAK;AACpE,QAAI,eAAe;AAEjB,aAAO,KAAK,gBAAgB,QAAQ,OAAO;AAAA,IAC7C;AAGA,UAAM,UAAU,MAAM,KAAK,UAAU,mBAAmB,MAAM;AAC9D,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,gBAAgB,gFAAe;AAAA,IAC3C;AAEA,QAAI;AACF,YAAM,YAAY,MAAM,KAAK,UAAU,gBAAgB,QAAQA,QAAO,QAAQ,QAAQ;AAGtF,YAAM,KAAK,UAAU,UAAU;AAAA,QAC7B;AAAA,QACA,QAAQ;AAAA,QACR,SAAS,uBAAuBA,MAAK;AAAA,MACvC,CAAC;AAED,aAAO;AAAA,IACT,SAAS,OAAO;AAEd,YAAM,KAAK,UAAU,gBAAgB,QAAQ,KAAK,KAAK;AACvD,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,cAAc,QAAsC;AACxD,WAAO,MAAM,KAAK,UAAU,sBAAsB,MAAM;AAAA,EAC1D;AAAA,EAEA,MAAM,gBAAgB,QAAgB,SAAgC;AACpE,UAAM,UAAU,MAAM,KAAK,UAAU,gBAAgB,SAAS,MAAM;AACpE,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,cAAc,gFAAe;AAAA,IACzC;AAGA,UAAM,KAAK,UAAU,UAAU;AAAA,MAC7B;AAAA,MACA,QAAQ;AAAA,MACR,SAAS,0BAA0B,OAAO;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,sBACJ,QACA,aACA,YACmC;AAEnC,UAAM,aAAa,MAAM,KAAK,UAAU,sBAAsB,MAAM;AACpE,UAAM,YAAY,WAAW,KAAK,CAAAA,WAASA,OAAM,OAAO,WAAW;AAEnE,QAAI,CAAC,WAAW;AACd,YAAM,IAAI,cAAc,gFAAe;AAAA,IACzC;AAEA,WAAO,MAAM,KAAK,UAAU,sBAAsB,aAAa,UAAU;AAAA,EAC3E;AAAA,EAEA,MAAM,YAAY,QAAgB,SAAgC;AAGhE,UAAM,UAAU,MAAM,KAAK,UAAU,YAAY,OAAO;AACxD,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,cAAc,gCAAO;AAAA,IACjC;AAGA,UAAM,KAAK,UAAU,UAAU;AAAA,MAC7B;AAAA,MACA,QAAQ;AAAA,MACR,SAAS,qBAAqB,OAAO;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,mBAAsC;AAC1C,WAAO,MAAM,KAAK,UAAU,iBAAiB;AAAA,EAC/C;AAAA,EAEA,MAAM,WAAW,QAAgB,SAAoD;AAEnF,UAAM,aAAa,MAAM,KAAK,UAAU,cAAc,QAAQ,IAAI;AAClE,QAAI,CAAC,YAAY;AACf,YAAM,IAAI,gBAAgB,sCAAQ;AAAA,IACpC;AAEA,QAAI,WAAW,MAAM;AACnB,YAAM,IAAI,gBAAgB,4CAAS;AAAA,IACrC;AAEA,QAAI,IAAI,KAAK,WAAW,WAAW,IAAI,oBAAI,KAAK,GAAG;AACjD,YAAM,IAAI,gBAAgB,sCAAQ;AAAA,IACpC;AAGA,UAAM,UAAU,MAAM,KAAK,UAAU,cAAc,QAAQ,MAAM,MAAM;AACvE,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,gBAAgB,4CAAS;AAAA,IACrC;AAGA,UAAM,OAAO,MAAM,KAAK,UAAU,YAAY,MAAM;AACpD,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,cAAc,gCAAO;AAAA,IACjC;AAEA,UAAM,WAAW,KAAK,QAAQ,WAAW;AACzC,UAAM,KAAK,UAAU,gBAAgB,QAAQ,QAAQ;AAGrD,UAAM,KAAK,UAAU,UAAU;AAAA,MAC7B;AAAA,MACA,QAAQ;AAAA,MACR,SAAS,kBAAkB,QAAQ,IAAI,YAAY,WAAW,KAAK;AAAA,IACrE,CAAC;AAED,WAAO,EAAE,OAAO,SAAS;AAAA,EAC3B;AAAA;AAAA,EAGA,MAAM,oBAAoB,UAAgC,gBAAuC;AAC/F,QAAI;AACF,cAAQ,IAAI,kCAAkC,cAAc;AAG5D,YAAM,YAAY,MAAM,KAAK,UAAU,oBAAoB,cAAc;AACzE,UAAI,CAAC,aAAa,CAAC,UAAU,QAAQ;AACnC,gBAAQ,IAAI,qCAAqC,cAAc;AAC/D;AAAA,MACF;AAGA,YAAM,cAAc,MAAM,KAAK,cAAc,WAAW,QAAQ;AAGhE,YAAMA,SAAQ,MAAM,KAAK,UAAU,YAAY;AAAA,QAC7C,aAAa,UAAU;AAAA,QACvB,QAAQ,YAAY,KAAK;AAAA,QACzB,SAAS,YAAY;AAAA,QACrB,SAAS,YAAY;AAAA,QACrB,aAAa,YAAY;AAAA,QACzB,kBAAkB,YAAY;AAAA,MAChC,CAAC;AAED,cAAQ,IAAI,8BAA8BA,OAAM,EAAE;AAAA,IAKpD,SAAS,OAAO;AACd,cAAQ,MAAM,kCAAkC,KAAK;AAAA,IACvD;AAAA,EACF;AAAA,EAEQ,sBAA8B;AACpC,UAAM,QAAQ;AACd,QAAI,SAAS;AAGb,UAAM,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,CAAC,IAAI;AAE/C,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,gBAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,MAAM,CAAC;AAAA,IACjE;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,aAAa,QAA0D;AAC3E,UAAM,OAAO,MAAM,KAAK,UAAU,YAAY,MAAM;AACpD,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,cAAc,gCAAO;AAAA,IACjC;AAEA,UAAM,aAAa,MAAM,KAAK,UAAU,sBAAsB,MAAM;AACpE,UAAM,OAAO,WAAW,OAAO,CAAAA,WAASA,OAAM,MAAM,EAAE;AAEtD,WAAO;AAAA,MACL,OAAO,KAAK;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,aACJ,QACA,QAOmC;AAGnC,WAAO;AAAA,MACL,MAAM,CAAC;AAAA,MACP,OAAO;AAAA,MACP,MAAM,OAAO;AAAA,MACb,OAAO,OAAO;AAAA,MACd,YAAY;AAAA,IACd;AAAA,EACF;AACF;;;ACnPO,IAAM,eAAN,MAAmB;AAAA,EAWxB,YAAoB,KAAU;AAAV;AAClB,UAAM,YAAY,IAAI,gBAAgB,IAAI,EAAE;AAC5C,SAAK,eAAe,IAAI,aAAa,KAAK,SAAS;AACnD,SAAK,mBAAmB,IAAI,iBAAiB,GAAG;AAGhD,SAAK,kBAAkB,SAAS,KAAK,GAAG,EAAE,CAAC,SAA+B,SAAqB;AAC7F,aAAO,KAAK,sBAAsB,SAAS,IAAI;AAAA,IACjD,CAAC;AAED,SAAK,gBAAgB,SAAS,KAAK,GAAG,EAAE,CAAC,SAA+B,SAAqB;AAC3F,aAAO,KAAK,oBAAoB,SAAS,IAAI;AAAA,IAC/C,CAAC;AAED,SAAK,kBAAkB,SAAS,KAAK,GAAG,EAAE,CAAC,SAA+B,SAAqB;AAC7F,aAAO,KAAK,sBAAsB,SAAS,IAAI;AAAA,IACjD,CAAC;AAED,SAAK,wBAAwB,SAAS,KAAK,GAAG,EAAE,CAAC,SAA+B,SAAqB;AACnG,aAAO,KAAK,4BAA4B,SAAS,IAAI;AAAA,IACvD,CAAC;AAED,SAAK,cAAc,SAAS,KAAK,GAAG,EAAE,CAAC,SAA+B,SAAqB;AACzF,aAAO,KAAK,kBAAkB,SAAS,IAAI;AAAA,IAC7C,CAAC;AAED,SAAK,aAAa,SAAS,KAAK,GAAG,EAAE,CAAC,SAA+B,SAAqB;AACxF,aAAO,KAAK,iBAAiB,SAAS,IAAI;AAAA,IAC5C,CAAC;AAED,SAAK,eAAe,SAAS,KAAK,GAAG,EAAE,CAAC,SAA+B,SAAqB;AAC1F,aAAO,KAAK,mBAAmB,SAAS,IAAI;AAAA,IAC9C,CAAC;AAAA,EACH;AAAA,EAzDF,OAa0B;AAAA;AAAA;AAAA,EAChB;AAAA,EACA;AAAA,EACD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA,EAwCP,MAAM,WAAW,SAAqC;AACpD,QAAI;AACF,YAAM,UAAU,MAAM,KAAK,aAAa,iBAAiB;AACzD,aAAO,KAAK,gBAAgB,OAAO;AAAA,IACrC,SAAS,OAAY;AACnB,cAAQ,MAAM,sBAAsB,KAAK;AACzC,aAAO,KAAK,cAAc,MAAM,WAAW,oDAAY,MAAM,cAAc,GAAG;AAAA,IAChF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,oBAAoB,SAAqC;AAC7D,QAAI;AACF,YAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,YAAM,iBAAiB,IAAI,aAAa,IAAI,IAAI;AAEhD,UAAI,CAAC,gBAAgB;AACnB,eAAO,KAAK,cAAc,8CAAW,GAAG;AAAA,MAC1C;AAEA,YAAM,WAAW,MAAM,QAAQ,YAAY;AAC3C,YAAM,KAAK,aAAa,oBAAoB,UAAU,cAAc;AAEpE,aAAO,KAAK,gBAAgB,MAAM,sCAAQ;AAAA,IAC5C,SAAS,OAAY;AACnB,cAAQ,MAAM,gCAAgC,KAAK;AACnD,aAAO,KAAK,cAAc,MAAM,WAAW,wCAAU,MAAM,cAAc,GAAG;AAAA,IAC9E;AAAA,EACF;AAAA,EAEA,MAAc,sBAAsB,SAA+B,MAAqC;AACtG,QAAI;AACF,YAAM,OAA2B,MAAM,QAAQ,KAAK;AAGpD,YAAM,WAAW,QAAQ,QAAQ,IAAI,kBAAkB,KACvC,QAAQ,QAAQ,IAAI,iBAAiB;AAErD,YAAM,mBAAmB,MAAM,KAAK,iBAAiB;AAAA,QACnD,KAAK;AAAA,QACL,YAAY;AAAA,MACd;AAEA,UAAI,CAAC,kBAAkB;AACrB,eAAO,KAAK,cAAc,wCAAU,GAAG;AAAA,MACzC;AAEA,YAAM,YAAY,MAAM,KAAK,aAAa,gBAAgB,KAAK,QAAQ,IAAI;AAC3E,aAAO,KAAK,gBAAgB,WAAW,kDAAU;AAAA,IACnD,SAAS,OAAY;AACnB,cAAQ,MAAM,4BAA4B,KAAK;AAC/C,aAAO,KAAK,cAAc,MAAM,WAAW,oDAAY,MAAM,cAAc,GAAG;AAAA,IAChF;AAAA,EACF;AAAA,EAEA,MAAc,oBAAoB,SAA+B,MAAqC;AACpG,QAAI;AACF,YAAM,aAAa,MAAM,KAAK,aAAa,cAAc,KAAK,MAAM;AACpE,aAAO,KAAK,gBAAgB,UAAU;AAAA,IACxC,SAAS,OAAY;AACnB,cAAQ,MAAM,0BAA0B,KAAK;AAC7C,aAAO,KAAK,cAAc,MAAM,WAAW,gEAAc,MAAM,cAAc,GAAG;AAAA,IAClF;AAAA,EACF;AAAA,EAEA,MAAc,sBAAsB,SAA+B,MAAqC;AACtG,QAAI;AACF,YAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,YAAM,UAAU,SAAS,IAAI,SAAS,MAAM,GAAG,EAAE,IAAI,KAAK,GAAG;AAE7D,UAAI,CAAC,SAAS;AACZ,eAAO,KAAK,cAAc,oCAAW,GAAG;AAAA,MAC1C;AAEA,YAAM,KAAK,aAAa,gBAAgB,KAAK,QAAQ,OAAO;AAC5D,aAAO,KAAK,gBAAgB,MAAM,kDAAU;AAAA,IAC9C,SAAS,OAAY;AACnB,cAAQ,MAAM,4BAA4B,KAAK;AAC/C,aAAO,KAAK,cAAc,MAAM,WAAW,oDAAY,MAAM,cAAc,GAAG;AAAA,IAChF;AAAA,EACF;AAAA,EAEA,MAAc,4BAA4B,SAA+B,MAAqC;AAC5G,QAAI;AACF,YAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,YAAM,YAAY,IAAI,SAAS,MAAM,GAAG;AACxC,YAAM,cAAc,SAAS,UAAU,UAAU,SAAS,CAAC,KAAK,GAAG;AAEnE,UAAI,CAAC,aAAa;AAChB,eAAO,KAAK,cAAc,gDAAa,GAAG;AAAA,MAC5C;AAGA,YAAM,OAAO,SAAS,IAAI,aAAa,IAAI,MAAM,KAAK,GAAG;AACzD,YAAM,QAAQ,SAAS,IAAI,aAAa,IAAI,OAAO,KAAK,IAAI;AAC5D,YAAM,UAAU,OAAO,KAAK;AAE5B,YAAM,aAA+B,EAAE,MAAM,OAAO,OAAO;AAC3D,YAAM,SAAS,MAAM,KAAK,aAAa,sBAAsB,KAAK,QAAQ,aAAa,UAAU;AAEjG,aAAO,KAAK,gBAAgB,MAAM;AAAA,IACpC,SAAS,OAAY;AACnB,cAAQ,MAAM,oCAAoC,KAAK;AACvD,aAAO,KAAK,cAAc,MAAM,WAAW,oDAAY,MAAM,cAAc,GAAG;AAAA,IAChF;AAAA,EACF;AAAA,EAEA,MAAc,kBAAkB,SAA+B,MAAqC;AAClG,QAAI;AACF,YAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,YAAM,UAAU,SAAS,IAAI,SAAS,MAAM,GAAG,EAAE,IAAI,KAAK,GAAG;AAE7D,UAAI,CAAC,SAAS;AACZ,eAAO,KAAK,cAAc,oCAAW,GAAG;AAAA,MAC1C;AAEA,YAAM,KAAK,aAAa,YAAY,KAAK,QAAQ,OAAO;AACxD,aAAO,KAAK,gBAAgB,MAAM,sCAAQ;AAAA,IAC5C,SAAS,OAAY;AACnB,cAAQ,MAAM,uBAAuB,KAAK;AAC1C,aAAO,KAAK,cAAc,MAAM,WAAW,wCAAU,MAAM,cAAc,GAAG;AAAA,IAC9E;AAAA,EACF;AAAA,EAEA,MAAc,iBAAiB,SAA+B,MAAqC;AACjG,QAAI;AACF,YAAM,OAAsB,MAAM,QAAQ,KAAK;AAG/C,YAAM,WAAW,QAAQ,QAAQ,IAAI,kBAAkB,KACvC,QAAQ,QAAQ,IAAI,iBAAiB;AAErD,YAAM,mBAAmB,MAAM,KAAK,iBAAiB;AAAA,QACnD,KAAK;AAAA,QACL,YAAY;AAAA,MACd;AAEA,UAAI,CAAC,kBAAkB;AACrB,eAAO,KAAK,cAAc,wCAAU,GAAG;AAAA,MACzC;AAEA,YAAM,SAAS,MAAM,KAAK,aAAa,WAAW,KAAK,QAAQ,IAAI;AACnE,aAAO,KAAK,gBAAgB,QAAQ,4CAAS;AAAA,IAC/C,SAAS,OAAY;AACnB,cAAQ,MAAM,sBAAsB,KAAK;AACzC,aAAO,KAAK,cAAc,MAAM,WAAW,8CAAW,MAAM,cAAc,GAAG;AAAA,IAC/E;AAAA,EACF;AAAA,EAEA,MAAc,mBAAmB,SAA+B,MAAqC;AACnG,QAAI;AACF,YAAM,YAAY,MAAM,KAAK,aAAa,aAAa,KAAK,MAAM;AAClE,aAAO,KAAK,gBAAgB,SAAS;AAAA,IACvC,SAAS,OAAY;AACnB,cAAQ,MAAM,yBAAyB,KAAK;AAC5C,aAAO,KAAK,cAAc,MAAM,WAAW,oDAAY,MAAM,cAAc,GAAG;AAAA,IAChF;AAAA,EACF;AAAA,EAEQ,gBAAmB,MAAS,SAA4B;AAC9D,UAAM,WAA2B;AAAA,MAC/B,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAEA,WAAO,IAAI,SAAS,KAAK,UAAU,QAAQ,GAAG;AAAA,MAC5C,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,+BAA+B;AAAA,QAC/B,gCAAgC;AAAA,QAChC,gCAAgC;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEQ,cAAc,OAAe,SAAiB,KAAe;AACnE,UAAM,WAAwB;AAAA,MAC5B,SAAS;AAAA,MACT;AAAA,IACF;AAEA,WAAO,IAAI,SAAS,KAAK,UAAU,QAAQ,GAAG;AAAA,MAC5C;AAAA,MACA,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,+BAA+B;AAAA,QAC/B,gCAAgC;AAAA,QAChC,gCAAgC;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;AC3PA,IAAO,cAAQ;AAAA,EACb,MAAM,MAAM,SAAkB,KAAU,KAA0C;AAChF,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,UAAM,EAAE,SAAS,IAAI;AACrB,UAAM,SAAS,QAAQ;AAGvB,QAAI,WAAW,WAAW;AACxB,aAAO,IAAI,SAAS,MAAM;AAAA,QACxB,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,+BAA+B;AAAA,UAC/B,gCAAgC;AAAA,UAChC,gCAAgC;AAAA,UAChC,0BAA0B;AAAA,QAC5B;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAI;AAEF,YAAM,cAAc,IAAI,YAAY,GAAG;AACvC,YAAM,eAAe,IAAI,aAAa,GAAG;AAGzC,UAAI,SAAS,WAAW,YAAY,GAAG;AACrC,eAAO,MAAM,iBAAiB,UAAU,QAAQ,SAAS,WAAW;AAAA,MACtE,WAAW,SAAS,WAAW,aAAa,GAAG;AAC7C,eAAO,MAAM,kBAAkB,UAAU,QAAQ,SAAS,YAAY;AAAA,MACxE,WAAW,aAAa,sBAAsB;AAE5C,eAAO,MAAM,aAAa,oBAAoB,OAAO;AAAA,MACvD,WAAW,aAAa,eAAe;AACrC,eAAO,IAAI,SAAS,KAAK,UAAU;AAAA,UACjC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QACpC,CAAC,GAAG;AAAA,UACF,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD,CAAC;AAAA,MACH,OAAO;AACL,eAAO,IAAI,SAAS,KAAK,UAAU;AAAA,UACjC,SAAS;AAAA,UACT,OAAO;AAAA,QACT,CAAC,GAAG;AAAA,UACF,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD,CAAC;AAAA,MACH;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,oBAAoB,KAAK;AACvC,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,eAAe,iBACb,UACA,QACA,SACA,SACmB;AACnB,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,UAAI,WAAW,OAAQ,QAAO,MAAM,QAAQ,SAAS,OAAO;AAC5D;AAAA,IAEF,KAAK;AACH,UAAI,WAAW,OAAQ,QAAO,MAAM,QAAQ,MAAM,OAAO;AACzD;AAAA,IAEF,KAAK;AACH,UAAI,WAAW,OAAQ,QAAO,MAAM,QAAQ,aAAa,OAAO;AAChE;AAAA,IAEF,KAAK;AACH,UAAI,WAAW,OAAQ,QAAO,MAAM,QAAQ,OAAO,OAAO;AAC1D;AAAA,IAEF,KAAK;AACH,UAAI,WAAW,MAAO,QAAO,MAAM,QAAQ,eAAe,OAAO;AACjE;AAAA,IAEF,KAAK;AACH,UAAI,WAAW,OAAQ,QAAO,MAAM,QAAQ,eAAe,OAAO;AAClE;AAAA,EACJ;AAEA,SAAO,IAAI,SAAS,KAAK,UAAU;AAAA,IACjC,SAAS;AAAA,IACT,OAAO;AAAA,EACT,CAAC,GAAG;AAAA,IACF,QAAQ;AAAA,IACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,EAChD,CAAC;AACH;AAvCe;AAyCf,eAAe,kBACb,UACA,QACA,SACA,SACmB;AAEnB,MAAI,aAAa,sBAAsB;AACrC,QAAI,WAAW,MAAO,QAAO,MAAM,QAAQ,WAAW,OAAO;AAAA,EAC/D;AAGA,MAAI,aAAa,0BAA0B;AACzC,QAAI,WAAW,MAAO,QAAO,MAAM,QAAQ,cAAc,OAAO;AAAA,EAClE;AAEA,MAAI,aAAa,qBAAqB;AACpC,QAAI,WAAW,OAAQ,QAAO,MAAM,QAAQ,gBAAgB,OAAO;AAAA,EACrE;AAGA,MAAI,SAAS,MAAM,kCAAkC,GAAG;AACtD,QAAI,WAAW,SAAU,QAAO,MAAM,QAAQ,gBAAgB,OAAO;AAAA,EACvE;AAGA,MAAI,SAAS,MAAM,0CAA0C,GAAG;AAC9D,QAAI,WAAW,MAAO,QAAO,MAAM,QAAQ,sBAAsB,OAAO;AAAA,EAC1E;AAGA,MAAI,SAAS,MAAM,6BAA6B,GAAG;AACjD,QAAI,WAAW,SAAU,QAAO,MAAM,QAAQ,YAAY,OAAO;AAAA,EACnE;AAGA,MAAI,aAAa,qBAAqB;AACpC,QAAI,WAAW,OAAQ,QAAO,MAAM,QAAQ,WAAW,OAAO;AAAA,EAChE;AAGA,MAAI,aAAa,oBAAoB;AACnC,QAAI,WAAW,MAAO,QAAO,MAAM,QAAQ,aAAa,OAAO;AAAA,EACjE;AAEA,SAAO,IAAI,SAAS,KAAK,UAAU;AAAA,IACjC,SAAS;AAAA,IACT,OAAO;AAAA,EACT,CAAC,GAAG;AAAA,IACF,QAAQ;AAAA,IACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,EAChD,CAAC;AACH;AApDe;AAuDf,eAAsB,MAAM,SAAc,KAAU,KAAuB;AACzE,MAAI;AACF,UAAM,eAAe,IAAI,aAAa,GAAG;AAGzC,UAAM,MAAM,IAAI,IAAI,uDAAuD,QAAQ,EAAE,EAAE;AACvF,UAAM,UAAU,IAAI,QAAQ,IAAI,SAAS,GAAG;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,QAAQ;AAAA,IAChB,CAAC;AAED,UAAM,aAAa,oBAAoB,OAAO;AAAA,EAChD,SAAS,OAAO;AACd,YAAQ,MAAM,wBAAwB,KAAK;AAAA,EAC7C;AACF;AAfsB;;;ACjKtB,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAG;AACX,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAQ;AAChB,UAAM,QAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAM,gCAA8D;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EArBD,OAYoE;AAAA;AAAA;AAAA,EAC1D;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,kCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAEA,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,wBACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B,GAXyE;AAAA,IAazE,cAA0B,wBAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD,GAT0B;AAAA,IAW1B,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": ["email", "email", "c", "address", "addresses", "i", "email", "email"]}